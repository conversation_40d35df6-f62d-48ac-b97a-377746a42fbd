#!/usr/bin/env node

import { readFileSync, writeFileSync, readdirSync, statSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __dirname = dirname(fileURLToPath(import.meta.url));

// 配置
const contentDir = join(__dirname, "src/content/posts");
const dryRun = process.argv.includes("--dry-run");

// 函数：递归获取所有 markdown 文件
function getAllMarkdownFiles(dir) {
  const files = [];

  function walk(currentDir) {
    const items = readdirSync(currentDir);

    for (const item of items) {
      const fullPath = join(currentDir, item);
      const stat = statSync(fullPath);

      if (stat.isDirectory()) {
        walk(fullPath);
      } else if (item.endsWith(".md")) {
        files.push(fullPath);
      }
    }
  }

  walk(dir);
  return files;
}

// 函数：从文件内容中提取 frontmatter
function extractFrontmatter(content) {
  const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
  if (!frontmatterMatch) return null;

  const frontmatterText = frontmatterMatch[1];
  const lines = frontmatterText.split("\n");
  const data = {};

  for (const line of lines) {
    const colonIndex = line.indexOf(":");
    if (colonIndex === -1) continue;

    const key = line.substring(0, colonIndex).trim();
    const value = line.substring(colonIndex + 1).trim();

    if (key === "date") {
      data.date = new Date(value.replace(/['"]/g, ""));
    } else if (key === "title") {
      data.title = value.replace(/['"]/g, "");
    }
  }

  return data;
}

// 函数：生成 slug（从文件名获取）
function generateSlugFromFilename(filePath) {
  // 找到 posts 后面的路径部分
  const postsIndex = filePath.indexOf("/posts/");
  if (postsIndex !== -1) {
    // 获取 posts 后面的完整路径，去掉 .md 扩展名
    const pathAfterPosts = filePath.substring(postsIndex + "/posts/".length);
    const slug = pathAfterPosts.replace(/\.md$/, "");
    return slug;
  }

  // 如果没找到 posts 目录，回退到原来的逻辑
  const filename = filePath.split("/").pop();
  const slug = filename.replace(/\.md$/, "");
  return slug;
}

// 函数：生成 Astro 路由兼容的 slug（处理特殊字符丢失）
function generateAstroCompatibleSlug(originalSlug) {
  // Astro 在生成路由时会丢失某些特殊字符，如冒号 :
  return originalSlug.replace(/:/g, "");
}

// 函数：根据日期和slug生成正确的URL
function generateCorrectURL(date, slug) {
  if (!date || isNaN(date.getTime())) {
    console.warn(`Invalid date for slug: ${slug}`);
    return null;
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `/${year}/${month}/${day}/${slug}/`;
}

// 函数：创建文件映射（旧URL -> 新URL）
function createFileMapping() {
  const files = getAllMarkdownFiles(contentDir);
  const mapping = new Map();

  for (const filePath of files) {
    try {
      const content = readFileSync(filePath, "utf-8");
      const frontmatter = extractFrontmatter(content);

      if (!frontmatter || !frontmatter.date) {
        console.warn(`No valid frontmatter found for: ${filePath}`);
        continue;
      }

      const slug = generateSlugFromFilename(filePath);
      const astroSlug = generateAstroCompatibleSlug(slug); // Astro 实际使用的 slug
      const title = generateAstroCompatibleSlug(frontmatter.title);
      const correctURL = generateCorrectURL(frontmatter.date, astroSlug);

      if (correctURL) {
        // 创建可能的旧URL格式映射
        const possibleOldUrls = [
          `/${slug}/`, // 原始 slug
          `/${astroSlug}/`, // Astro 处理后的 slug
          `/${frontmatter.date.getFullYear()}/${slug}/`,
          `/${frontmatter.date.getFullYear()}/${astroSlug}/`,
          `/${frontmatter.date.getFullYear()}/${String(
            frontmatter.date.getMonth() + 1
          ).padStart(2, "0")}/${slug}/`,
          `/${frontmatter.date.getFullYear()}/${String(
            frontmatter.date.getMonth() + 1
          ).padStart(2, "0")}/${astroSlug}/`,
          // 处理URL编码的情况
          `/${encodeURIComponent(slug)}/`,
          `/${encodeURIComponent(astroSlug)}/`,
          // 处理不同的日期格式
          `/${frontmatter.date.getFullYear()}/${String(
            frontmatter.date.getMonth() + 1
          ).padStart(2, "0")}/${String(frontmatter.date.getDate()).padStart(
            2,
            "0"
          )}/${slug}/`,
          `/${frontmatter.date.getFullYear()}/${String(
            frontmatter.date.getMonth() + 1
          ).padStart(2, "0")}/${String(frontmatter.date.getDate()).padStart(
            2,
            "0"
          )}/${title}/`,
          `/${frontmatter.date.getFullYear()}/${String(
            frontmatter.date.getMonth() + 1
          ).padStart(2, "0")}/${String(frontmatter.date.getDate()).padStart(
            2,
            "0"
          )}/${slug}/`,
          `/${frontmatter.date.getFullYear()}/${String(
            frontmatter.date.getMonth() + 1
          ).padStart(2, "0")}/${String(frontmatter.date.getDate()).padStart(
            2,
            "0"
          )}/${astroSlug}/`,
        ];

        for (const oldUrl of possibleOldUrls) {
          mapping.set(oldUrl, correctURL.toLowerCase());
        }

        // 特殊处理：处理slug中的特殊字符和编码
        const decodedSlug = decodeURIComponent(slug);
        if (decodedSlug !== slug) {
          const decodedAstroSlug = generateAstroCompatibleSlug(decodedSlug);
          mapping.set(`/${decodedSlug}/`, correctURL);
          mapping.set(`/${decodedAstroSlug}/`, correctURL);
        }

        // 处理原始 slug 中包含冒号的情况
        if (slug.includes(":")) {
          const slugWithoutColon = slug.replace(/:/g, "");
          if (slugWithoutColon !== astroSlug) {
            mapping.set(`/${slugWithoutColon}/`, correctURL);
          }
        }
      }
    } catch (error) {
      console.error(`Error processing file ${filePath}:`, error.message);
    }
  }

  console.log(mapping);

  return mapping;
}

// 函数：修复文件中的内部链接
function fixLinksInFile(filePath, urlMapping) {
  try {
    const content = readFileSync(filePath, "utf-8");
    let modifiedContent = content;
    let changesMade = 0;

    // 匹配 Markdown 链接格式: [text](/path)
    const linkRegex = /\[([^\]]+)\]\(\/[^)]+\)/g;

    modifiedContent = modifiedContent.replace(
      linkRegex,
      (match, linkText, offset) => {
        // 提取 URL 部分
        const urlMatch = match.match(/\[([^\]]+)\]\(([^)]+)\)/);
        if (!urlMatch) return match;

        const [, text, url] = urlMatch;

        // 检查是否是内部链接（以 / 开头但不是 http 开头的完整URL）
        if (
          !url.startsWith("/") ||
          url.startsWith("//") ||
          url.includes("http")
        ) {
          return match;
        }

        // 尝试找到对应的正确URL
        let correctedURL = null;

        // 直接匹配
        if (urlMapping.has(url)) {
          correctedURL = urlMapping.get(url);
        } else {
          // 尝试模糊匹配
          for (const [oldUrl, newUrl] of urlMapping) {
            // 提取slug部分进行比较
            const oldSlugMatch = oldUrl.match(/\/([^/]+)\/?$/);
            const currentSlugMatch = url.match(/\/([^/]+)\/?$/);

            if (oldSlugMatch && currentSlugMatch) {
              const oldSlug = oldSlugMatch[1].toLowerCase();
              const currentSlug = currentSlugMatch[1].toLowerCase();

              // 处理URL编码和解码
              if (
                oldSlug === currentSlug ||
                decodeURIComponent(oldSlug) ===
                  decodeURIComponent(currentSlug) ||
                oldSlug.replace(/[^a-z0-9]/g, "") ===
                  currentSlug.replace(/[^a-z0-9]/g, "") ||
                // 处理冒号丢失的情况
                oldSlug.replace(/:/g, "") === currentSlug ||
                currentSlug.replace(/:/g, "") === oldSlug
              ) {
                correctedURL = newUrl;
                break;
              }
            }
          }
        }

        if (correctedURL && correctedURL !== url) {
          changesMade++;
          console.log(`  ${url} -> ${correctedURL}`);
          return `[${text}](${correctedURL})`;
        }

        return match;
      }
    );

    if (changesMade > 0) {
      console.log(`\n${filePath}:`);
      console.log(`  Made ${changesMade} link corrections`);

      if (!dryRun) {
        writeFileSync(filePath, modifiedContent, "utf-8");
        console.log(`  File updated successfully`);
      }
    }

    return changesMade;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
    return 0;
  }
}

// 主函数
function main() {
  console.log("🔗 Internal Links Fixer");
  console.log("========================\n");

  if (dryRun) {
    console.log("🔍 Running in DRY RUN mode - no files will be modified\n");
  }

  console.log("📁 Scanning content directory...");
  const files = getAllMarkdownFiles(contentDir);
  console.log(`Found ${files.length} markdown files\n`);

  console.log("🗺️  Creating URL mapping...");
  const urlMapping = createFileMapping();
  console.log(`Created ${urlMapping.size} URL mappings\n`);

  console.log("🔧 Fixing internal links...");
  let totalChanges = 0;

  for (const filePath of files) {
    const changes = fixLinksInFile(filePath, urlMapping);
    totalChanges += changes;
  }

  console.log(`\n✅ Process completed!`);
  console.log(`📊 Total files processed: ${files.length}`);
  console.log(`🔗 Total links corrected: ${totalChanges}`);

  if (dryRun && totalChanges > 0) {
    console.log("\n💡 Run without --dry-run to apply changes");
  }
}

// 运行脚本
main();
