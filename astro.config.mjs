// @ts-check
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import sitemap from '@astrojs/sitemap';
import solidJs from '@astrojs/solid-js';
import rehypeExternalLinks from 'rehype-external-links';

// https://astro.build/config
export default defineConfig({
  site: 'https://www.cclliang.com',
  integrations: [
    tailwind(), 
    solidJs(),
    sitemap({
      changefreq: 'weekly',
      priority: 0.8,
      lastmod: new Date(),
      entryLimit: 10000,
      customPages: [
        'https://www.cclliang.com/about/',
        'https://www.cclliang.com/links/',
        'https://www.cclliang.com/portfolio/',
        'https://www.cclliang.com/cooperation/',
        'https://www.cclliang.com/pricing/'
      ],
      serialize(item) {
        // Set different priorities for different page types
        if (item.url.includes('/page/')) {
          item.priority = 0.4;
          item.changefreq = 'weekly';
        } else if (item.url.includes('/archives/')) {
          item.priority = 0.5;
          item.changefreq = 'monthly';
        } else if (item.url.match(/\/\d{4}\/\d{2}\/\d{2}\//)) {
          // Blog posts with date pattern get higher priority
          item.priority = 0.8;
          item.changefreq = 'monthly';
        } else if (item.url.includes('/categories/') || item.url.includes('/tags/')) {
          // Category and tag pages get medium priority
          item.priority = 0.6;
          item.changefreq = 'weekly';
        }
        return item;
      }
    })
  ],
  markdown: {
    rehypePlugins: [
      [rehypeExternalLinks, {
        target: '_blank',
        rel: ['nofollow', 'noopener', 'noreferrer']
      }]
    ],
    shikiConfig: {
      theme: 'css-variables',
      wrap: true
    },
    // Enable syntax highlighting for code blocks
    syntaxHighlight: 'shiki',
    // Configure markdown processing for better performance
    gfm: true,
    smartypants: true
  },
  output: 'static',
  build: {
    format: 'directory',
    // Optimize build for better performance
    inlineStylesheets: 'auto'
  },
  trailingSlash: 'always',
  // Configure server for development
  server: {
    port: 3000,
    host: true
  },
});