{"extends": "astro/tsconfigs/strict", "include": [".astro/types.d.ts", "**/*"], "exclude": ["dist", "node_modules"], "compilerOptions": {"jsx": "preserve", "jsxImportSource": "solid-js", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/layouts/*": ["./src/layouts/*"], "@/utils/*": ["./src/utils/*"], "@/content/*": ["./src/content/*"]}, "types": ["astro/client"], "strict": true, "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowImportingTsExtensions": true}}