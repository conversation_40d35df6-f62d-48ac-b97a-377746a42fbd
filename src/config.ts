import type { SiteConfig } from './types';

export const siteConfig: SiteConfig = {
  title: '沧沧凉凉的博客',
  description: '专注于前端开发等技术分享',
  author: '沧沧凉凉',
  url: 'https://www.cclliang.com',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  social: {
    github: 'https://github.com/cclliang',
    email: '<EMAIL>',
  },
  seo: {
    keywords: [
      'JavaScript',
      'React',
      'Vue',
      'Node.js',
      'TypeScript',
      'Web开发',
      '前端开发',
      '技术博客'
    ],
    // SEO images
    ogImage: '/img/og-image.png',
    favicon: '/img/favicon.png',
    appleTouchIcon: '/img/apple-touch-icon.png',
    // Add analytics IDs if available
    googleAnalytics: undefined,
    baiduAnalytics: undefined,
  },
};

// URL generation utilities
export const generatePostURL = (date: Date, slug: string): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `/${year}/${month}/${day}/${slug}/`;
};

export const generateCategoryURL = (category: string): string => {
  return `/categories/${category}/`;
};

export const generateTagURL = (tag: string): string => {
  return `/tags/${tag}/`;
};

export const generateArchiveURL = (year: number): string => {
  return `/archives/${year}/`;
};

export const generatePaginationURL = (page: number): string => {
  return page === 1 ? '/' : `/page/${page}/`;
};