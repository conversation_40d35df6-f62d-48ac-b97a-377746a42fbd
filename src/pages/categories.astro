---
import { getCollection } from 'astro:content';
import BaseLayout from '../layouts/BaseLayout.astro';

const posts = await getCollection('posts');

// 统计每个分类的文章数量
const categoryStats = posts.reduce((acc, post) => {
  const categories = post.data.categories || [];
  categories.forEach(category => {
    if (!acc[category]) {
      acc[category] = {
        name: category,
        count: 0,
        posts: []
      };
    }
    acc[category].count++;
    acc[category].posts.push(post);
  });
  return acc;
}, {} as Record<string, { name: string; count: number; posts: typeof posts }>);

// 按文章数量排序
const sortedCategories = Object.values(categoryStats).sort((a, b) => b.count - a.count);

// 获取分类图标emoji
const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, string> = {
    'JavaScript': '🟨',
    'React': '⚛️',
    'Vue': '💚',
    'Node': '🟢',
    'TypeScript': '🔷',
    'CSS': '🎨',
    'HTML': '🌐',
    'web开发': '🖥️',
    'web工具': '🔧',
    '杂谈': '💭',
    '面试': '📝',
    '架构': '🏗️',
    '游戏人生': '🎮',
    '脚本': '📜',
    '小程序': '📱',
    '服务器开发': '🖥️',
    'ai': '🤖',
    'flutter': '📱',
    'blog': '📖',
    'unity3D': '🎮'
  };
  return iconMap[category] || '📝';
};

// 获取分类颜色
const getCategoryColor = (index: number) => {
  const colors = [
    'bg-blue-100 text-blue-800 border-blue-200',
    'bg-green-100 text-green-800 border-green-200',
    'bg-purple-100 text-purple-800 border-purple-200',
    'bg-yellow-100 text-yellow-800 border-yellow-200',
    'bg-red-100 text-red-800 border-red-200',
    'bg-indigo-100 text-indigo-800 border-indigo-200',
    'bg-pink-100 text-pink-800 border-pink-200',
    'bg-gray-100 text-gray-800 border-gray-200'
  ];
  return colors[index % colors.length];
};
---

<BaseLayout title="分类" description="按分类浏览所有文章">
  <main class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <section class="bg-white py-6 sm:py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">文章分类</h1>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            探索 {sortedCategories.length} 个分类中的 {posts.length} 篇文章
          </p>
          
          <!-- 统计信息 -->
          <div class="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
              </svg>
              <span class="font-medium">{sortedCategories.length}</span>
              <span class="ml-1">个分类</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <span class="font-medium">{posts.length}</span>
              <span class="ml-1">篇文章</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
              </svg>
              <span class="font-medium">{Math.round(posts.length / sortedCategories.length)}</span>
              <span class="ml-1">篇/分类</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 搜索框 -->
    <section class="py-8 bg-white border-b border-gray-200">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div class="relative">
            <input 
              type="text" 
              placeholder="搜索分类..." 
              class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              id="category-search"
            >
            <svg class="w-5 h-5 absolute left-3 top-3.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
        </div>
      </div>
    </section>

    <!-- 分类网格 -->
    <section class="py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="categories-grid">
            {sortedCategories.map((category, index) => (
              <div class="category-card bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 overflow-hidden"
                   data-category={category.name.toLowerCase()}>
                <div class="p-4">
                  <!-- 分类头部 -->
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                      <div class="text-2xl">
                        {getCategoryIcon(category.name)}
                      </div>
                      <div>
                        <h3 class="text-lg font-semibold text-gray-900">
                          {category.name}
                        </h3>
                        <p class="text-sm text-gray-600">
                          {category.count} 篇文章
                        </p>
                      </div>
                    </div>
                    <div class={`px-3 py-1 rounded-full text-xs font-medium border ${getCategoryColor(index)}`}>
                      {category.count}
                    </div>
                  </div>

                  <!-- 进度条 -->
                  <div class="mb-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={`width: ${(category.count / Math.max(...sortedCategories.map(c => c.count))) * 100}%`}
                      ></div>
                    </div>
                  </div>

                  <!-- 最新文章预览 -->
                  <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">最新文章</h4>
                    <div class="space-y-2">
                      {category.posts.slice(0, 3).map(post => (
                        <div class="text-sm">
                          <a href={`/${new Date(post.data.date).getFullYear()}/${String(new Date(post.data.date).getMonth() + 1).padStart(2, '0')}/${String(new Date(post.data.date).getDate()).padStart(2, '0')}/${post.slug.replace(/\.md$/, '')}/`} 
                             class="text-gray-700 hover:text-blue-600 transition-colors line-clamp-1">
                            {post.data.title}
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>

                  <!-- 查看全部按钮 -->
                  <div class="pt-4 border-t border-gray-100">
                    <a href={`/categories/${category.name}/`} 
                       class="inline-flex items-center w-full justify-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                      查看全部
                      <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <!-- 无结果提示 -->
          <div id="no-results" class="hidden text-center py-12">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
            </svg>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">未找到相关分类</h3>
            <p class="text-gray-600">请尝试其他关键词</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 搜索功能 -->
  <script>
    const searchInput = document.getElementById('category-search') as HTMLInputElement;
    const categoriesGrid = document.getElementById('categories-grid');
    const categoryCards = document.querySelectorAll('.category-card');
    const noResults = document.getElementById('no-results');

    function filterCategories() {
      const searchTerm = searchInput?.value?.toLowerCase() || '';
      let visibleCount = 0;

      categoryCards.forEach(card => {
        const element = card as HTMLElement;
        const categoryName = element.dataset.category || '';
        
        if (!searchTerm || categoryName.includes(searchTerm)) {
          element.style.display = 'block';
          visibleCount++;
        } else {
          element.style.display = 'none';
        }
      });

      // 显示/隐藏无结果提示
      if (visibleCount === 0) {
        noResults?.classList.remove('hidden');
      } else {
        noResults?.classList.add('hidden');
      }
    }

    // 绑定搜索事件
    searchInput?.addEventListener('input', filterCategories);
  </script>
</BaseLayout>