---
import { getCollection } from 'astro:content';
import BaseLayout from '../../layouts/BaseLayout.astro';
import dayjs from 'dayjs';

export async function getStaticPaths() {
  const posts = await getCollection('posts');
  const tags = [...new Set(posts.flatMap(post => post.data.tags || []))];
  
  return tags.map(tag => ({
    params: { tag },
    props: { tag }
  }));
}

const { tag } = Astro.params;
const posts = await getCollection('posts');

// 筛选该标签的文章
const tagPosts = posts.filter(post => 
  post.data.tags && post.data.tags.includes(tag)
).sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime());

// 按年份分组
const postsByYear = tagPosts.reduce((acc, post) => {
  const year = new Date(post.data.date).getFullYear();
  if (!acc[year]) {
    acc[year] = [];
  }
  acc[year].push(post);
  return acc;
}, {} as Record<number, typeof tagPosts>);

const years = Object.keys(postsByYear).map(Number).sort((a, b) => b - a);

// 获取相关分类
const relatedCategories = [...new Set(tagPosts.flatMap(post => post.data.categories || []))];

// 获取相关标签（除了当前标签）
const relatedTags = [...new Set(tagPosts.flatMap(post => post.data.tags || []))].filter(t => t !== tag);

// 获取标签颜色
const getTagColor = (tagName: string) => {
  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-purple-500',
    'bg-yellow-500',
    'bg-red-500',
    'bg-indigo-500',
    'bg-pink-500',
    'bg-gray-500'
  ];
  const index = tagName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[index % colors.length];
};
---

<BaseLayout title={`${tag} - 标签`} description={`${tag} 标签下的所有文章`}>
  <main class="min-h-screen bg-gray-50">
    <!-- 标签标题 -->
    <section class="bg-white py-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <!-- 面包屑导航 -->
          <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
            <a href="/" class="hover:text-blue-600 transition-colors">首页</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <a href="/tags/" class="hover:text-blue-600 transition-colors">标签</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <span class="text-gray-900">{tag}</span>
          </nav>

          <!-- 标签信息 -->
          <div class="text-center">
            <div class={`inline-flex items-center justify-center w-16 h-16 ${getTagColor(tag)} rounded-full text-white font-bold text-xl mb-4`}>
              #
            </div>
            <h1 class="text-4xl font-bold text-gray-900 mb-4">{tag}</h1>
            <p class="text-xl text-gray-600 mb-8">
              共有 {tagPosts.length} 篇文章使用了该标签
            </p>
            
            <!-- 统计信息 -->
            <div class="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <span class="font-medium">{tagPosts.length}</span>
                <span class="ml-1">篇文章</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                <span class="font-medium">{years.length}</span>
                <span class="ml-1">个年份</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                </svg>
                <span class="font-medium">{relatedCategories.length}</span>
                <span class="ml-1">个相关分类</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 搜索框 -->
    <section class="py-8 bg-white border-b border-gray-200">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div class="flex flex-col md:flex-row gap-4 items-center">
            <div class="flex-1 relative">
              <input 
                type="text" 
                placeholder="搜索该标签下的文章..." 
                class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                id="post-search"
              >
              <svg class="w-5 h-5 absolute left-3 top-3.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
            
            <!-- 排序选择 -->
            <select id="sort-select" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="date">按日期排序</option>
              <option value="title">按标题排序</option>
            </select>
          </div>
        </div>
      </div>
    </section>

    <!-- 文章列表 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div id="posts-container">
            {years.map(year => (
              <div class="year-section mb-12" data-year={year}>
                <!-- 年份标题 -->
                <div class="flex items-center mb-8">
                  <div class={`flex-shrink-0 w-12 h-12 ${getTagColor(tag)} rounded-full flex items-center justify-center text-white font-bold`}>
                    {year}
                  </div>
                  <div class="ml-4">
                    <h2 class="text-xl font-bold text-gray-900">
                      {year} 年
                    </h2>
                    <p class="text-gray-600 text-sm">
                      {postsByYear[year].length} 篇文章
                    </p>
                  </div>
                  <div class="flex-1 ml-6">
                    <div class="h-px bg-gray-300"></div>
                  </div>
                </div>

                <!-- 该年份的文章列表 -->
                <div class="space-y-6">
                  {postsByYear[year].map(post => {
                    const date = new Date(post.data.date);
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const slug = post.slug.replace(/\.md$/, '');
                    const url = `/${year}/${month}/${day}/${slug}/`;
                    
                    return (
                      <article class="post-item bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 p-6"
                               data-title={post.data.title.toLowerCase()}
                               data-description={post.data.description || ''}
                               data-categories={post.data.categories?.join(',') || ''}
                               data-date={post.data.date.toString()}
                      >
                        <div class="flex flex-col lg:flex-row lg:items-start gap-4">
                          <!-- 日期 -->
                          <div class="flex-shrink-0 text-center lg:text-left">
                            <div class="text-sm text-gray-500">
                              {dayjs(post.data.date).format('MM-DD')}
                            </div>
                          </div>

                          <!-- 文章信息 -->
                          <div class="flex-1 min-w-0">
                            <h3 class="text-lg font-semibold mb-2 leading-tight">
                              <a href={url} class="text-gray-900 hover:text-blue-600 transition-colors">
                                {post.data.title}
                              </a>
                            </h3>
                            
                            {post.data.description && (
                              <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                {post.data.description}
                              </p>
                            )}
                            
                            <div class="flex flex-wrap items-center gap-2 text-xs">
                              {post.data.categories && post.data.categories.map(category => (
                                <a href={`/categories/${category}/`} class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors">
                                  {category}
                                </a>
                              ))}
                              {post.data.tags && post.data.tags.filter(t => t !== tag).map(otherTag => (
                                <a href={`/tags/${otherTag}/`} class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">
                                  {otherTag}
                                </a>
                              ))}
                            </div>
                          </div>

                          <!-- 阅读链接 -->
                          <div class="flex-shrink-0">
                            <a href={url} class="text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium">
                              阅读 →
                            </a>
                          </div>
                        </div>
                      </article>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>

          <!-- 无结果提示 -->
          <div id="no-results" class="hidden text-center py-12">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">未找到相关文章</h3>
            <p class="text-gray-600">请尝试其他关键词</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 相关内容 -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            
            <!-- 相关分类 -->
            <div>
              <h2 class="text-xl font-bold text-gray-900 mb-4">相关分类</h2>
              <div class="flex flex-wrap gap-2">
                {relatedCategories.slice(0, 10).map(category => (
                  <a href={`/categories/${category}/`} 
                     class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors text-sm">
                    {category}
                  </a>
                ))}
              </div>
            </div>

            <!-- 相关标签 -->
            <div>
              <h2 class="text-xl font-bold text-gray-900 mb-4">相关标签</h2>
              <div class="flex flex-wrap gap-2">
                {relatedTags.slice(0, 15).map(relatedTag => (
                  <a href={`/tags/${relatedTag}/`} 
                     class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full hover:bg-gray-200 transition-colors text-sm">
                    {relatedTag}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 搜索和排序功能 -->
  <script>
    const searchInput = document.getElementById('post-search') as HTMLInputElement;
    const sortSelect = document.getElementById('sort-select') as HTMLSelectElement;
    const postItems = document.querySelectorAll('.post-item');
    const yearSections = document.querySelectorAll('.year-section');
    const postsContainer = document.getElementById('posts-container');
    const noResults = document.getElementById('no-results');

    function filterAndSort() {
      const searchTerm = searchInput?.value?.toLowerCase() || '';
      const sortBy = sortSelect?.value || 'date';
      
      let visiblePosts = Array.from(postItems).filter(item => {
        const element = item as HTMLElement;
        const title = element.dataset.title || '';
        const description = element.dataset.description || '';
        const categories = element.dataset.categories || '';

        const matches = !searchTerm || 
          title.includes(searchTerm) || 
          description.toLowerCase().includes(searchTerm) ||
          categories.toLowerCase().includes(searchTerm);

        return matches;
      });

      // 排序
      if (sortBy === 'title') {
        visiblePosts.sort((a, b) => {
          const aTitle = (a as HTMLElement).dataset.title || '';
          const bTitle = (b as HTMLElement).dataset.title || '';
          return aTitle.localeCompare(bTitle);
        });
      } else {
        visiblePosts.sort((a, b) => {
          const aDate = new Date((a as HTMLElement).dataset.date || '').getTime();
          const bDate = new Date((b as HTMLElement).dataset.date || '').getTime();
          return bDate - aDate;
        });
      }

      // 隐藏所有文章
      postItems.forEach(item => {
        (item as HTMLElement).style.display = 'none';
      });

      // 显示筛选后的文章
      visiblePosts.forEach(item => {
        (item as HTMLElement).style.display = 'block';
      });

      // 统计每年的可见文章数量
      const yearCounts: Record<string, number> = {};
      visiblePosts.forEach(item => {
        const yearSection = item.closest('.year-section') as HTMLElement;
        const year = yearSection?.dataset.year || '';
        yearCounts[year] = (yearCounts[year] || 0) + 1;
      });

      // 显示/隐藏年份分组
      yearSections.forEach(section => {
        const element = section as HTMLElement;
        const year = element.dataset.year || '';
        if (yearCounts[year]) {
          element.style.display = 'block';
        } else {
          element.style.display = 'none';
        }
      });

      // 显示/隐藏无结果提示
      if (visiblePosts.length === 0) {
        noResults?.classList.remove('hidden');
      } else {
        noResults?.classList.add('hidden');
      }
    }

    // 绑定事件
    searchInput?.addEventListener('input', filterAndSort);
    sortSelect?.addEventListener('change', filterAndSort);
  </script>
</BaseLayout>