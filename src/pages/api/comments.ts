import type { APIRoute } from 'astro';
import type { Comment, CreateCommentRequest } from '../../api/index';

// 临时内存存储（生产环境应使用数据库）
const comments: Comment[] = [];

// 生成ID的辅助函数
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// 格式化评论数据，构建回复关系
function formatComments(pageUrl: string): Comment[] {
  const pageComments = comments.filter(comment => 
    comment.pageUrl === pageUrl && !comment.parentId
  );
  
  pageComments.forEach(comment => {
    comment.replies = comments.filter(reply => reply.parentId === comment.id);
  });
  
  return pageComments.sort((a, b) => b.timestamp - a.timestamp);
}

export const GET: APIRoute = async ({ url }) => {
  const pageUrl = url.searchParams.get('pageUrl');
  
  if (!pageUrl) {
    return new Response(JSON.stringify({
      success: false,
      message: '缺少页面URL参数'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  const pageComments = formatComments(pageUrl);
  
  return new Response(JSON.stringify({
    success: true,
    comments: pageComments,
    total: pageComments.reduce((total, comment) => total + 1 + comment.replies.length, 0)
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const POST: APIRoute = async ({ request }) => {
  try {
    const data: CreateCommentRequest = await request.json();
    
    // 验证必填字段
    if (!data.nickname?.trim() || !data.content?.trim() || !data.pageUrl?.trim()) {
      return new Response(JSON.stringify({
        success: false,
        message: '昵称、内容和页面URL不能为空'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // 验证字符长度
    if (data.nickname.length > 20) {
      return new Response(JSON.stringify({
        success: false,
        message: '昵称不能超过20个字符'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    if (data.content.length > 500) {
      return new Response(JSON.stringify({
        success: false,
        message: '评论内容不能超过500个字符'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // 如果是回复，验证父评论是否存在
    if (data.parentId) {
      const parentExists = comments.some(comment => comment.id === data.parentId);
      if (!parentExists) {
        return new Response(JSON.stringify({
          success: false,
          message: '父评论不存在'
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
    }

    // 创建新评论
    const newComment: Comment = {
      id: generateId(),
      nickname: data.nickname.trim(),
      content: data.content.trim(),
      timestamp: Date.now(),
      pageUrl: data.pageUrl.trim(),
      parentId: data.parentId,
      replies: []
    };

    // 添加到内存存储
    comments.push(newComment);

    return new Response(JSON.stringify({
      success: true,
      comment: newComment,
      message: '评论发布成功'
    }), {
      status: 201,
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Failed to create comment:', error);
    return new Response(JSON.stringify({
      success: false,
      message: '服务器内部错误'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

export const DELETE: APIRoute = async ({ params }) => {
  const commentId = params.id;
  
  if (!commentId) {
    return new Response(JSON.stringify({
      success: false,
      message: '缺少评论ID'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  const commentIndex = comments.findIndex(comment => comment.id === commentId);
  
  if (commentIndex === -1) {
    return new Response(JSON.stringify({
      success: false,
      message: '评论不存在'
    }), {
      status: 404,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  // 删除评论及其所有回复
  const commentToDelete = comments[commentIndex];
  const toDelete = [commentId];
  
  // 如果是主评论，也要删除所有回复
  if (!commentToDelete.parentId) {
    comments.forEach(comment => {
      if (comment.parentId === commentId) {
        toDelete.push(comment.id);
      }
    });
  }

  // 执行删除
  toDelete.forEach(id => {
    const index = comments.findIndex(comment => comment.id === id);
    if (index !== -1) {
      comments.splice(index, 1);
    }
  });

  return new Response(JSON.stringify({
    success: true,
    message: '评论删除成功'
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json'
    }
  });
};