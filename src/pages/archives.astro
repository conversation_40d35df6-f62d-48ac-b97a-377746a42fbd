---
import { getCollection } from 'astro:content';
import BaseLayout from '../layouts/BaseLayout.astro';
import dayjs from 'dayjs';

const posts = await getCollection('posts');
const sortedPosts = posts.sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime());

// 按年份分组文章
const postsByYear = sortedPosts.reduce((acc, post) => {
  const year = new Date(post.data.date).getFullYear();
  if (!acc[year]) {
    acc[year] = [];
  }
  acc[year].push(post);
  return acc;
}, {} as Record<number, typeof posts>);

// 获取年份列表并排序
const years = Object.keys(postsByYear).map(Number).sort((a, b) => b - a);

// 统计信息
const totalPosts = posts.length;
const categories = [...new Set(posts.flatMap(post => post.data.categories || []))];
const tags = [...new Set(posts.flatMap(post => post.data.tags || []))];
---

<BaseLayout title="文章归档" description="按时间顺序浏览所有文章">
  <main class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <section class="bg-white py-6 sm:py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">文章归档</h1>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            探索 {totalPosts} 篇文章的技术之旅
          </p>
          
          <!-- 统计信息 -->
          <div class="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <span class="font-medium">{totalPosts}</span>
              <span class="ml-1">篇文章</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
              </svg>
              <span class="font-medium">{categories.length}</span>
              <span class="ml-1">个分类</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
              </svg>
              <span class="font-medium">{tags.length}</span>
              <span class="ml-1">个标签</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 搜索和筛选 -->
    <section class="py-8 bg-white border-b border-gray-200">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div class="flex flex-col md:flex-row gap-4 items-center">
            <!-- 搜索框 -->
            <div class="flex-1 relative">
              <input 
                type="text" 
                placeholder="搜索文章标题或内容..." 
                class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                id="search-input"
              >
              <svg class="w-5 h-5 absolute left-3 top-2.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>

            <!-- 年份筛选 -->
            <select id="year-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">所有年份</option>
              {years.map(year => (
                <option value={year}>{year} ({postsByYear[year].length}篇)</option>
              ))}
            </select>

            <!-- 分类筛选 -->
            <select id="category-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">所有分类</option>
              {categories.map(category => (
                <option value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </section>

    <!-- 文章列表 -->
    <section class="py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          {years.map(year => (
            <div class="year-section mb-12" data-year={year}>
              <!-- 年份标题 -->
              <div class="flex items-center mb-6">
                <div class="flex-shrink-0 w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  {year}
                </div>
                <div class="ml-4">
                  <h2 class="text-2xl font-bold text-gray-900">
                    {year} 年
                  </h2>
                  <p class="text-gray-600">
                    {postsByYear[year].length} 篇文章
                  </p>
                </div>
                <div class="flex-1 ml-8">
                  <div class="h-px bg-gray-300"></div>
                </div>
              </div>

              <!-- 该年份的文章列表 -->
              <div class="space-y-4">
                {postsByYear[year].map(post => {
                  const date = new Date(post.data.date);
                  const month = String(date.getMonth() + 1).padStart(2, '0');
                  const day = String(date.getDate()).padStart(2, '0');
                  const slug = post.slug.replace(/\.md$/, '');
                  const url = `/${year}/${month}/${day}/${slug}/`;
                  
                  return (
                    <article class="post-item bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 p-4"
                             data-title={post.data.title.toLowerCase()}
                             data-categories={post.data.categories?.join(',') || ''}
                             data-tags={post.data.tags?.join(',') || ''}
                             data-description={post.data.description || ''}
                    >
                      <div class="flex flex-col md:flex-row md:items-center gap-4">
                        <!-- 日期 -->
                        <div class="flex-shrink-0 text-center">
                          <div class="text-sm text-gray-500">
                            {dayjs(post.data.date).format('MM-DD')}
                          </div>
                        </div>

                        <!-- 文章信息 -->
                        <div class="flex-1 min-w-0">
                          <h3 class="text-lg font-semibold mb-2 leading-tight">
                            <a href={url} class="text-gray-900 hover:text-blue-600 transition-colors">
                              {post.data.title}
                            </a>
                          </h3>
                          
                          {post.data.description && (
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                              {post.data.description}
                            </p>
                          )}
                          
                          <div class="flex flex-wrap items-center gap-2 text-xs">
                            {post.data.categories && post.data.categories.map(category => (
                              <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                {category}
                              </span>
                            ))}
                            {post.data.tags && post.data.tags.slice(0, 3).map(tag => (
                              <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>

                        <!-- 阅读链接 -->
                        <div class="flex-shrink-0">
                          <a href={url} class="text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium">
                            阅读 →
                          </a>
                        </div>
                      </div>
                    </article>
                  );
                })}
              </div>
            </div>
          ))}

          <!-- 无结果提示 -->
          <div id="no-results" class="hidden text-center py-12">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">未找到相关文章</h3>
            <p class="text-gray-600">请尝试调整搜索条件或筛选器</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 搜索和筛选功能 -->
  <script>
    // 搜索和筛选功能
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    const yearFilter = document.getElementById('year-filter') as HTMLSelectElement;
    const categoryFilter = document.getElementById('category-filter') as HTMLSelectElement;
    const postItems = document.querySelectorAll('.post-item');
    const yearSections = document.querySelectorAll('.year-section');
    const noResults = document.getElementById('no-results');

    function filterPosts() {
      const searchTerm = searchInput?.value?.toLowerCase() || '';
      const selectedYear = yearFilter?.value || '';
      const selectedCategory = categoryFilter?.value || '';
      
      let visibleCount = 0;
      const yearCounts: Record<string, number> = {};

      postItems.forEach(item => {
        const element = item as HTMLElement;
        const title = element.dataset.title || '';
        const description = element.dataset.description || '';
        const categories = element.dataset.categories || '';
        const yearSection = element.closest('.year-section') as HTMLElement;
        const year = yearSection?.dataset.year || '';

        // 检查是否匹配搜索条件
        const matchesSearch = !searchTerm || 
          title.includes(searchTerm) || 
          description.toLowerCase().includes(searchTerm);
        
        const matchesYear = !selectedYear || year === selectedYear;
        const matchesCategory = !selectedCategory || categories.includes(selectedCategory);

        const isVisible = matchesSearch && matchesYear && matchesCategory;
        
        if (isVisible) {
          element.style.display = 'block';
          visibleCount++;
          yearCounts[year] = (yearCounts[year] || 0) + 1;
        } else {
          element.style.display = 'none';
        }
      });

      // 显示/隐藏年份分组
      yearSections.forEach(section => {
        const element = section as HTMLElement;
        const year = element.dataset.year || '';
        if (yearCounts[year]) {
          element.style.display = 'block';
        } else {
          element.style.display = 'none';
        }
      });

      // 显示/隐藏无结果提示
      if (visibleCount === 0) {
        noResults?.classList.remove('hidden');
      } else {
        noResults?.classList.add('hidden');
      }
    }

    // 绑定事件监听器
    searchInput?.addEventListener('input', filterPosts);
    yearFilter?.addEventListener('change', filterPosts);
    categoryFilter?.addEventListener('change', filterPosts);
  </script>
</BaseLayout>