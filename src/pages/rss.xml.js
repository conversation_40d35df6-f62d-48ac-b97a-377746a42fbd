import rss from '@astrojs/rss';
import { getCollection } from 'astro:content';

export async function GET(context) {
  const posts = await getCollection('posts');
  const sortedPosts = posts.sort((a, b) => new Date(b.data.date) - new Date(a.data.date));
  
  return rss({
    title: '沧沧凉凉的个人博客',
    description: '记录前端学习路上的点点滴滴',
    site: context.site,
    items: sortedPosts.map((post) => {
      const date = new Date(post.data.date);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const slug = post.slug.replace(/\.md$/, '');
      
      return {
        title: post.data.title,
        pubDate: post.data.date,
        description: post.data.description || '',
        link: `/${year}/${month}/${day}/${slug}/`,
        categories: post.data.categories || [],
      };
    }),
    customData: `<language>zh-CN</language>`,
  });
}