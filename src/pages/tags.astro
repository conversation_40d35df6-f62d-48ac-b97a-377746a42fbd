---
import { getCollection } from 'astro:content';
import BaseLayout from '../layouts/BaseLayout.astro';

const posts = await getCollection('posts');

// 统计每个标签的文章数量
const tagStats = posts.reduce((acc, post) => {
  const tags = post.data.tags || [];
  tags.forEach(tag => {
    if (!acc[tag]) {
      acc[tag] = {
        name: tag,
        count: 0,
        posts: []
      };
    }
    acc[tag].count++;
    acc[tag].posts.push(post);
  });
  return acc;
}, {} as Record<string, { name: string; count: number; posts: typeof posts }>);

// 按文章数量排序
const sortedTags = Object.values(tagStats).sort((a, b) => b.count - a.count);

// 获取标签大小 (基于文章数量)
const getTagSize = (count: number, maxCount: number) => {
  const ratio = count / maxCount;
  if (ratio >= 0.8) return 'text-2xl';
  if (ratio >= 0.6) return 'text-xl';
  if (ratio >= 0.4) return 'text-lg';
  if (ratio >= 0.2) return 'text-base';
  return 'text-sm';
};

// 获取标签颜色
const getTagColor = (count: number, maxCount: number) => {
  const ratio = count / maxCount;
  if (ratio >= 0.8) return 'bg-blue-600 text-white';
  if (ratio >= 0.6) return 'bg-blue-500 text-white';
  if (ratio >= 0.4) return 'bg-blue-400 text-white';
  if (ratio >= 0.2) return 'bg-blue-300 text-blue-900';
  return 'bg-blue-100 text-blue-800';
};

const maxCount = Math.max(...sortedTags.map(tag => tag.count));
---

<BaseLayout title="标签" description="按标签浏览所有文章">
  <main class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <section class="bg-white py-6 sm:py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">文章标签</h1>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            探索 {sortedTags.length} 个标签中的 {posts.length} 篇文章
          </p>
          
          <!-- 统计信息 -->
          <div class="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
              </svg>
              <span class="font-medium">{sortedTags.length}</span>
              <span class="ml-1">个标签</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <span class="font-medium">{posts.length}</span>
              <span class="ml-1">篇文章</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
              </svg>
              <span class="font-medium">{Math.round(posts.length / sortedTags.length * 10) / 10}</span>
              <span class="ml-1">篇/标签</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 搜索和排序 -->
    <section class="py-8 bg-white border-b border-gray-200">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div class="flex flex-col md:flex-row gap-4 items-center">
            <!-- 搜索框 -->
            <div class="flex-1 relative">
              <input 
                type="text" 
                placeholder="搜索标签..." 
                class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                id="tag-search"
              >
              <svg class="w-5 h-5 absolute left-3 top-2.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>

            <!-- 排序选择 -->
            <select id="sort-select" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="count">按文章数量</option>
              <option value="name">按标签名称</option>
            </select>

            <!-- 视图切换 -->
            <div class="flex border border-gray-300 rounded-lg overflow-hidden">
              <button id="cloud-view" class="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                </svg>
              </button>
              <button id="list-view" class="px-4 py-2 bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 标签内容 -->
    <section class="py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          
          <!-- 标签云视图 -->
          <div id="cloud-container" class="text-center">
            <div class="flex flex-wrap justify-center gap-3">
              {sortedTags.map(tag => (
                <a href={`/tags/${tag.name}/`} 
                   class={`tag-cloud-item inline-block px-3 py-1 rounded-full transition-all duration-200 hover:scale-105 ${getTagColor(tag.count, maxCount)} ${getTagSize(tag.count, maxCount)}`}
                   data-tag={tag.name.toLowerCase()}
                   data-count={tag.count}
                   title={`${tag.count} 篇文章`}>
                  {tag.name}
                </a>
              ))}
            </div>
          </div>

          <!-- 列表视图 -->
          <div id="list-container" class="hidden">
            <div class="space-y-4">
              {sortedTags.map(tag => (
                <div class="tag-list-item bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 p-4"
                     data-tag={tag.name.toLowerCase()}
                     data-count={tag.count}>
                  <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-900">
                      {tag.name}
                    </h3>
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {tag.count}
                    </span>
                  </div>
                  
                  <div class="mb-3">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={`width: ${(tag.count / maxCount) * 100}%`}
                      ></div>
                    </div>
                  </div>

                  <div class="mb-3">
                    <p class="text-sm text-gray-600 mb-2">最新文章:</p>
                    <div class="space-y-1">
                      {tag.posts.slice(0, 2).map(post => (
                        <div class="text-xs text-gray-500 line-clamp-1">
                          {post.data.title}
                        </div>
                      ))}
                    </div>
                  </div>

                  <a href={`/tags/${tag.name}/`} 
                     class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium">
                    查看全部
                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                  </a>
                </div>
              ))}
            </div>
          </div>

          <!-- 无结果提示 -->
          <div id="no-results" class="hidden text-center py-12">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
            </svg>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">未找到相关标签</h3>
            <p class="text-gray-600">请尝试其他关键词</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 交互功能 -->
  <script>
    const searchInput = document.getElementById('tag-search') as HTMLInputElement;
    const sortSelect = document.getElementById('sort-select') as HTMLSelectElement;
    const cloudView = document.getElementById('cloud-view') as HTMLButtonElement;
    const listView = document.getElementById('list-view') as HTMLButtonElement;
    const cloudContainer = document.getElementById('cloud-container');
    const listContainer = document.getElementById('list-container');
    const noResults = document.getElementById('no-results');

    let currentView = 'cloud';
    let allTags = Array.from(document.querySelectorAll('.tag-cloud-item, .tag-list-item'));

    // 视图切换
    function switchView(view: string) {
      currentView = view;
      
      if (view === 'cloud') {
        cloudContainer?.classList.remove('hidden');
        listContainer?.classList.add('hidden');
        cloudView?.classList.add('bg-blue-600', 'text-white');
        cloudView?.classList.remove('bg-gray-200', 'text-gray-700');
        listView?.classList.add('bg-gray-200', 'text-gray-700');
        listView?.classList.remove('bg-blue-600', 'text-white');
      } else {
        cloudContainer?.classList.add('hidden');
        listContainer?.classList.remove('hidden');
        listView?.classList.add('bg-blue-600', 'text-white');
        listView?.classList.remove('bg-gray-200', 'text-gray-700');
        cloudView?.classList.add('bg-gray-200', 'text-gray-700');
        cloudView?.classList.remove('bg-blue-600', 'text-white');
      }
      
      filterAndSort();
    }

    // 过滤和排序
    function filterAndSort() {
      const searchTerm = searchInput?.value?.toLowerCase() || '';
      const sortBy = sortSelect?.value || 'count';
      
      // 获取当前视图的标签元素
      const items = currentView === 'cloud' 
        ? document.querySelectorAll('.tag-cloud-item')
        : document.querySelectorAll('.tag-list-item');
      
      let visibleItems = Array.from(items).filter(item => {
        const element = item as HTMLElement;
        const tagName = element.dataset.tag || '';
        const matches = !searchTerm || tagName.includes(searchTerm);
        
        if (matches) {
          element.style.display = currentView === 'cloud' ? 'inline-block' : 'block';
        } else {
          element.style.display = 'none';
        }
        
        return matches;
      });

      // 排序
      visibleItems.sort((a, b) => {
        const aElement = a as HTMLElement;
        const bElement = b as HTMLElement;
        
        if (sortBy === 'count') {
          return parseInt(bElement.dataset.count || '0') - parseInt(aElement.dataset.count || '0');
        } else {
          return (aElement.dataset.tag || '').localeCompare(bElement.dataset.tag || '');
        }
      });

      // 重新排列元素
      const container = currentView === 'cloud' 
        ? cloudContainer?.querySelector('div') 
        : listContainer?.querySelector('div');
      
      if (container) {
        visibleItems.forEach(item => {
          container.appendChild(item);
        });
      }

      // 显示/隐藏无结果提示
      if (visibleItems.length === 0) {
        noResults?.classList.remove('hidden');
      } else {
        noResults?.classList.add('hidden');
      }
    }

    // 绑定事件
    searchInput?.addEventListener('input', filterAndSort);
    sortSelect?.addEventListener('change', filterAndSort);
    cloudView?.addEventListener('click', () => switchView('cloud'));
    listView?.addEventListener('click', () => switchView('list'));
  </script>
</BaseLayout>