---
import { getCollection } from 'astro:content';
import BaseLayout from '@/layouts/BaseLayout.astro';
import dayjs from 'dayjs';
import { siteConfig } from '@/config';
import AboutSection from '@/components/AboutSection.astro';

const posts = await getCollection('posts');
const sortedPosts = posts.sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime());

// 只显示前10篇文章
const recentPosts = sortedPosts.slice(0, 10);

---

<BaseLayout title="首页" description={siteConfig.description}>
  <!-- Website Schema -->
  <script type="application/ld+json" slot="head" is:inline>
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": `${siteConfig.title}`,
      "description": `${siteConfig.description}`,
      "url": `${siteConfig.url}`,
      "author": {
        "@type": "Person",
        "name": `${siteConfig.author}`,
        "url": `${siteConfig.url}/about/`
      },
      "publisher": {
        "@type": "Organization",
        "name": `${siteConfig.title}`,
        "logo": {
          "@type": "ImageObject",
          "url": `${siteConfig.url}/img/avatar.png`
        }
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": `${siteConfig.url}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      },
      "inLanguage": "zh-CN"
    }
  </script>

  <!-- Organization Schema -->
  <script type="application/ld+json" slot="head" is:inline>
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": `${siteConfig.title}`,
      "url": `${siteConfig.url}`,
      "logo": `${siteConfig.url}/img/avatar.png`,
      "description": `${siteConfig.description}`,
      "founder": {
        "@type": "Person",
        "name": `${siteConfig.author}`
      },
      "sameAs": [
        `${siteConfig.social.github}`
      ]
    }
  </script>
  <main class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-white pt-8 sm:pt-12">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {siteConfig.title}
          </h1>
          <p class="text-lg text-gray-600 pb-6 leading-relaxed">
            {siteConfig.description}
          </p>
        </div>
      </div>
    </section>

    <AboutSection />

    <!-- 最新文章 -->
    <section class="py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <header class="mb-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">最新文章</h2>
            <p class="text-gray-600">记录前端开发过程中的思考与实践</p>
          </header>
          
          <div class="space-y-4">
            {recentPosts.map(async (post) => {
              const date = new Date(post.data.date);
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const slug = post.slug.replace(/\.md$/, '');
              const url = `/${year}/${month}/${day}/${slug}/`;
              
              // 获取描述信息：优先使用 YAML 中的 description，否则从正文提取
              let description = post.data.description;
              if (!description) {
                // 从原始 markdown 内容中提取文本
                const { body } = post;
                // 移除 markdown 语法，提取纯文本
                const textContent = body
                  .replace(/^---[\s\S]*?---/, '') // 移除 front matter
                  .replace(/#{1,6}\s+/g, '') // 移除标题标记
                  .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
                  .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
                  .replace(/`(.*?)`/g, '$1') // 移除行内代码标记
                  .replace(/```[\s\S]*?```/g, '') // 移除代码块
                  .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
                  .replace(/\[.*?\]\(.*?\)/g, '') // 移除链接
                  .replace(/\n+/g, ' ') // 将换行符替换为空格
                  .replace(/\s+/g, ' ') // 合并多个空格
                  .trim();
                
                // 提取前120个字符作为描述
                description = textContent.length > 120 ? textContent.substring(0, 120) + '...' : textContent;
              }
              
              return (
                <article class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 overflow-hidden">
                  <div class="p-4">
                    <header class="mb-3">
                      <h3 class="text-lg font-semibold mb-2 leading-tight">
                        <a href={url} class="text-gray-900 hover:text-gray-600 transition-colors">
                          {post.data.title}
                        </a>
                      </h3>
                      
                      <div class="flex flex-wrap items-center gap-3 text-sm text-gray-500">
                        <time datetime={post.data.date.toISOString()} class="flex items-center">
                          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                          </svg>
                          {dayjs(post.data.date).format('YYYY年MM月DD日')}
                        </time>
                        
                        {post.data.categories && post.data.categories.length > 0 && (
                          <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                            </svg>
                            <span>{post.data.categories[0]}</span>
                          </div>
                        )}
                      </div>
                    </header>
                    
                    {description && (
                      <a href={url} class="text-gray-700 leading-relaxed mb-3 line-clamp-2 block hover:text-gray-900 transition-colors">
                        {description}
                      </a>
                    )}
                    
                    <div class="flex items-center justify-between">
                      <a href={url} class="text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium">
                        阅读更多 →
                      </a>
                      
                      {post.data.tags && post.data.tags.length > 0 && (
                        <div class="flex flex-wrap gap-1">
                          {post.data.tags.slice(0, 3).map(tag => (
                            <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </article>
              );
            })}
          </div>
          
          <div class="text-center mt-8">
            <a href="/archives/" class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
              查看全部文章
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  </main>
</BaseLayout>