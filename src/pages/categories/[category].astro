---
import { getCollection } from 'astro:content';
import BaseLayout from '../../layouts/BaseLayout.astro';
import dayjs from 'dayjs';

export async function getStaticPaths() {
  const posts = await getCollection('posts');
  const categories = [...new Set(posts.flatMap(post => post.data.categories || []))];
  
  return categories.map(category => ({
    params: { category },
    props: { category }
  }));
}

const { category } = Astro.params;
const posts = await getCollection('posts');

// 筛选该分类的文章
const categoryPosts = posts.filter(post => 
  post.data.categories && post.data.categories.includes(category)
).sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime());

// 获取分类图标
const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, string> = {
    'JavaScript': '🟨',
    'React': '⚛️',
    'Vue': '💚',
    'Node': '🟢',
    'TypeScript': '🔷',
    'CSS': '🎨',
    'HTML': '🌐',
    'web开发': '🖥️',
    'web工具': '🔧',
    '杂谈': '💭',
    '面试': '📝',
    '架构': '🏗️',
    '游戏人生': '🎮',
    '脚本': '📜',
    '小程序': '📱',
    '服务器开发': '🖥️',
    'ai': '🤖',
    'flutter': '📱',
    'blog': '📖',
    'unity3D': '🎮'
  };
  return iconMap[category] || '📝';
};

// 按年份分组
const postsByYear = categoryPosts.reduce((acc, post) => {
  const year = new Date(post.data.date).getFullYear();
  if (!acc[year]) {
    acc[year] = [];
  }
  acc[year].push(post);
  return acc;
}, {} as Record<number, typeof categoryPosts>);

const years = Object.keys(postsByYear).map(Number).sort((a, b) => b - a);

// 获取相关标签
const relatedTags = [...new Set(categoryPosts.flatMap(post => post.data.tags || []))];
---

<BaseLayout title={`${category} - 分类`} description={`${category} 分类下的所有文章`}>
  <main class="min-h-screen bg-gray-50">
    <!-- 分类标题 -->
    <section class="bg-white py-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <!-- 面包屑导航 -->
          <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
            <a href="/" class="hover:text-blue-600 transition-colors">首页</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <a href="/categories/" class="hover:text-blue-600 transition-colors">分类</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <span class="text-gray-900">{category}</span>
          </nav>

          <!-- 分类信息 -->
          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full text-2xl mb-4">
              {getCategoryIcon(category)}
            </div>
            <h1 class="text-4xl font-bold text-gray-900 mb-4">{category}</h1>
            <p class="text-xl text-gray-600 mb-8">
              共有 {categoryPosts.length} 篇文章
            </p>
            
            <!-- 统计信息 -->
            <div class="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <span class="font-medium">{categoryPosts.length}</span>
                <span class="ml-1">篇文章</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                <span class="font-medium">{years.length}</span>
                <span class="ml-1">个年份</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                </svg>
                <span class="font-medium">{relatedTags.length}</span>
                <span class="ml-1">个相关标签</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 搜索框 -->
    <section class="py-8 bg-white border-b border-gray-200">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div class="relative">
            <input 
              type="text" 
              placeholder="搜索该分类下的文章..." 
              class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              id="post-search"
            >
            <svg class="w-5 h-5 absolute left-3 top-3.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
        </div>
      </div>
    </section>

    <!-- 文章列表 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          {years.map(year => (
            <div class="year-section mb-12" data-year={year}>
              <!-- 年份标题 -->
              <div class="flex items-center mb-8">
                <div class="flex-shrink-0 w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                  {year}
                </div>
                <div class="ml-4">
                  <h2 class="text-xl font-bold text-gray-900">
                    {year} 年
                  </h2>
                  <p class="text-gray-600 text-sm">
                    {postsByYear[year].length} 篇文章
                  </p>
                </div>
                <div class="flex-1 ml-6">
                  <div class="h-px bg-gray-300"></div>
                </div>
              </div>

              <!-- 该年份的文章列表 -->
              <div class="space-y-6">
                {postsByYear[year].map(post => {
                  const date = new Date(post.data.date);
                  const month = String(date.getMonth() + 1).padStart(2, '0');
                  const day = String(date.getDate()).padStart(2, '0');
                  const slug = post.slug.replace(/\.md$/, '');
                  const url = `/${year}/${month}/${day}/${slug}/`;
                  
                  return (
                    <article class="post-item bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 p-6"
                             data-title={post.data.title.toLowerCase()}
                             data-description={post.data.description || ''}
                             data-tags={post.data.tags?.join(',') || ''}
                    >
                      <div class="flex flex-col lg:flex-row lg:items-center gap-4">
                        <!-- 日期 -->
                        <div class="flex-shrink-0 text-center lg:text-left">
                          <div class="text-sm text-gray-500">
                            {dayjs(post.data.date).format('MM-DD')}
                          </div>
                        </div>

                        <!-- 文章信息 -->
                        <div class="flex-1 min-w-0">
                          <h3 class="text-lg font-semibold mb-2 leading-tight">
                            <a href={url} class="text-gray-900 hover:text-blue-600 transition-colors">
                              {post.data.title}
                            </a>
                          </h3>
                          
                          {post.data.description && (
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                              {post.data.description}
                            </p>
                          )}
                          
                          <div class="flex flex-wrap items-center gap-2 text-xs">
                            {post.data.tags && post.data.tags.map(tag => (
                              <a href={`/tags/${tag}/`} class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">
                                {tag}
                              </a>
                            ))}
                          </div>
                        </div>

                        <!-- 阅读链接 -->
                        <div class="flex-shrink-0">
                          <a href={url} class="text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium">
                            阅读 →
                          </a>
                        </div>
                      </div>
                    </article>
                  );
                })}
              </div>
            </div>
          ))}

          <!-- 无结果提示 -->
          <div id="no-results" class="hidden text-center py-12">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">未找到相关文章</h3>
            <p class="text-gray-600">请尝试其他关键词</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 相关标签 -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <h2 class="text-2xl font-bold text-gray-900 mb-8">相关标签</h2>
          <div class="flex flex-wrap gap-2">
            {relatedTags.slice(0, 20).map(tag => (
              <a href={`/tags/${tag}/`} 
                 class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors text-sm">
                {tag}
              </a>
            ))}
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- 搜索功能 -->
  <script>
    const searchInput = document.getElementById('post-search') as HTMLInputElement;
    const postItems = document.querySelectorAll('.post-item');
    const yearSections = document.querySelectorAll('.year-section');
    const noResults = document.getElementById('no-results');

    function filterPosts() {
      const searchTerm = searchInput?.value?.toLowerCase() || '';
      let visibleCount = 0;
      const yearCounts: Record<string, number> = {};

      postItems.forEach(item => {
        const element = item as HTMLElement;
        const title = element.dataset.title || '';
        const description = element.dataset.description || '';
        const tags = element.dataset.tags || '';

        const matches = !searchTerm || 
          title.includes(searchTerm) || 
          description.toLowerCase().includes(searchTerm) ||
          tags.toLowerCase().includes(searchTerm);

        if (matches) {
          element.style.display = 'block';
          visibleCount++;
          
          const yearSection = element.closest('.year-section') as HTMLElement;
          const year = yearSection?.dataset.year || '';
          yearCounts[year] = (yearCounts[year] || 0) + 1;
        } else {
          element.style.display = 'none';
        }
      });

      // 显示/隐藏年份分组
      yearSections.forEach(section => {
        const element = section as HTMLElement;
        const year = element.dataset.year || '';
        if (yearCounts[year]) {
          element.style.display = 'block';
        } else {
          element.style.display = 'none';
        }
      });

      // 显示/隐藏无结果提示
      if (visibleCount === 0) {
        noResults?.classList.remove('hidden');
      } else {
        noResults?.classList.add('hidden');
      }
    }

    // 绑定搜索事件
    searchInput?.addEventListener('input', filterPosts);
  </script>
</BaseLayout>