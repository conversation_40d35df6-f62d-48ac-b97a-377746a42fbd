---
import { getCollection } from 'astro:content';
import BaseLayout from '../../layouts/BaseLayout.astro';
import dayjs from 'dayjs';

export async function getStaticPaths({ paginate }) {
  const posts = await getCollection('posts');
  const sortedPosts = posts.sort((a, b) => new Date(b.data.date) - new Date(a.data.date));
  
  return paginate(sortedPosts, {
    pageSize: 10,
  });
}

const { page } = Astro.props;
const pageTitle = page.currentPage === 1 ? '首页' : `第${page.currentPage}页`;
---

<BaseLayout title={pageTitle} description="沧沧凉凉的个人博客，记录前端学习路上的点点滴滴">
  <main class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <header class="mb-8">
        <h1 class="text-4xl font-bold mb-4">
          {page.currentPage === 1 ? '沧沧凉凉的个人博客' : `文章列表 - 第${page.currentPage}页`}
        </h1>
        <p class="text-gray-600">记录前端学习路上的点点滴滴</p>
      </header>
      
      <div class="space-y-8">
        {page.data.map(async (post) => {
          const date = new Date(post.data.date);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const slug = post.slug.replace(/\.md$/, '');
          const url = `/${year}/${month}/${day}/${slug}/`;
          
          return (
            <article class="border-b pb-8 last:border-b-0">
              <header class="mb-4">
                <h2 class="text-2xl font-bold mb-2">
                  <a href={url} class="hover:text-blue-600 transition-colors">
                    {post.data.title}
                  </a>
                </h2>
                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                  <time datetime={post.data.date.toISOString()}>
                    {dayjs(post.data.date).format('YYYY-MM-DD')}
                  </time>
                  
                  {post.data.categories && post.data.categories.length > 0 && (
                    <div class="flex flex-wrap gap-1">
                      {post.data.categories.map(cat => (
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">{cat}</span>
                      ))}
                    </div>
                  )}
                  
                  {post.data.tags && post.data.tags.length > 0 && (
                    <div class="flex flex-wrap gap-1">
                      {post.data.tags.map(tag => (
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">{tag}</span>
                      ))}
                    </div>
                  )}
                </div>
              </header>
              
              {post.data.description && (
                <p class="text-gray-700 leading-relaxed">{post.data.description}</p>
              )}
            </article>
          );
        })}
      </div>
      
      <!-- 分页导航 -->
      <nav class="mt-12 flex justify-center items-center space-x-4">
        {page.url.prev && (
          <a 
            href={page.url.prev} 
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            上一页
          </a>
        )}
        
        <span class="px-4 py-2 bg-gray-100 rounded-lg">
          第 {page.currentPage} 页，共 {page.lastPage} 页
        </span>
        
        {page.url.next && (
          <a 
            href={page.url.next} 
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            下一页
          </a>
        )}
      </nav>
    </div>
  </main>
</BaseLayout>