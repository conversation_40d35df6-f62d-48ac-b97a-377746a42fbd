import type { APIRoute } from 'astro';
import { getCollection } from 'astro:content';
import dayjs from 'dayjs';

export const GET: APIRoute = async () => {
  const posts = await getCollection('posts');
  
  const searchData = posts.map(post => {
    const date = new Date(post.data.date);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const slug = post.slug.replace(/\.md$/, '');
    const url = `/${year}/${month}/${day}/${slug}/`;
    
    return {
      id: post.id,
      title: post.data.title,
      description: post.data.description || '',
      url: url,
      date: dayjs(post.data.date).format('YYYY-MM-DD'),
      categories: post.data.categories || [],
      tags: post.data.tags || [],
      // 添加一些额外的搜索字段
      searchText: [
        post.data.title,
        post.data.description || '',
        ...(post.data.tags || []),
        ...(post.data.categories || [])
      ].join(' ').toLowerCase()
    };
  }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  return new Response(JSON.stringify(searchData), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=3600' // 缓存1小时
    }
  });
};