---
import { getCollection } from 'astro:content';
import PostLayout from '../../../../layouts/PostLayout.astro';

export async function getStaticPaths() {
  const posts = await getCollection('posts');
  
  // 按日期排序文章
  const sortedPosts = posts.sort((a, b) => 
    new Date(b.data.date).getTime() - new Date(a.data.date).getTime()
  );
  
  return sortedPosts.map((post, index) => {
    const date = new Date(post.data.date);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // 获取文章的 slug（去掉 .md 扩展名）
    const slug = post.slug.replace(/\.md$/, '');
    
    // 获取相邻文章
    const prevPost = index < sortedPosts.length - 1 ? sortedPosts[index + 1] : null;
    const nextPost = index > 0 ? sortedPosts[index - 1] : null;
    
    return {
      params: {
        year: year.toString(),
        month: month.toString(),
        day: day.toString(),
        slug: slug
      },
      props: { post, prevPost, nextPost }
    };
  });
}

const { post, prevPost, nextPost } = Astro.props;
const { Content } = await post.render();
---

<PostLayout post={post} prevPost={prevPost} nextPost={nextPost}>
  <Content />
</PostLayout>