interface RequestConfig extends RequestInit {
  timeout?: number;
  baseURL?: string;
  params?: Record<string, any>;
  retry?: number;
  retryDelay?: number;
}

interface RequestInterceptor {
  request?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;
  requestError?: (error: any) => any;
}

interface ResponseInterceptor {
  response?: (response: Response) => Response | Promise<Response>;
  responseError?: (error: any) => any;
}

interface HttpError extends Error {
  status?: number;
  statusText?: string;
  response?: Response;
}

class HttpClient {
  private baseURL: string;
  private defaultConfig: RequestConfig;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];

  constructor(config: RequestConfig = {}) {
    this.baseURL = config.baseURL || '';
    this.defaultConfig = {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
      ...config,
    };
  }

  private createAbortController(timeout?: number): AbortController {
    const controller = new AbortController();
    if (timeout) {
      setTimeout(() => controller.abort(), timeout);
    }
    return controller;
  }

  private buildURL(url: string, params?: Record<string, any>): string {
    const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    
    if (!params || Object.keys(params).length === 0) {
      return fullURL;
    }

    const urlObj = new URL(fullURL);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        urlObj.searchParams.append(key, String(value));
      }
    });

    return urlObj.toString();
  }

  private async applyRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
    let finalConfig = { ...config };
    
    for (const interceptor of this.requestInterceptors) {
      if (interceptor.request) {
        try {
          finalConfig = await interceptor.request(finalConfig);
        } catch (error) {
          if (interceptor.requestError) {
            throw await interceptor.requestError(error);
          }
          throw error;
        }
      }
    }
    
    return finalConfig;
  }

  private async applyResponseInterceptors(response: Response): Promise<Response> {
    let finalResponse = response;
    
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.response) {
        try {
          finalResponse = await interceptor.response(finalResponse);
        } catch (error) {
          if (interceptor.responseError) {
            throw await interceptor.responseError(error);
          }
          throw error;
        }
      }
    }
    
    return finalResponse;
  }

  private async handleResponseError(error: any): Promise<never> {
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.responseError) {
        try {
          throw await interceptor.responseError(error);
        } catch (interceptedError) {
          throw interceptedError;
        }
      }
    }
    throw error;
  }

  private async retryRequest(
    url: string, 
    config: RequestConfig, 
    attempt: number = 1
  ): Promise<Response> {
    try {
      return await this.makeRequest(url, config);
    } catch (error) {
      const maxRetries = config.retry || 0;
      
      if (attempt <= maxRetries) {
        const delay = config.retryDelay || 1000;
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
        return this.retryRequest(url, config, attempt + 1);
      }
      
      throw error;
    }
  }

  private async makeRequest(url: string, config: RequestConfig): Promise<Response> {
    const controller = this.createAbortController(config.timeout);
    const finalURL = this.buildURL(url, config.params);
    
    const requestConfig: RequestInit = {
      ...this.defaultConfig,
      ...config,
      signal: controller.signal,
    };

    delete (requestConfig as any).timeout;
    delete (requestConfig as any).baseURL;
    delete (requestConfig as any).params;
    delete (requestConfig as any).retry;
    delete (requestConfig as any).retryDelay;

    try {
      const response = await fetch(finalURL, requestConfig);
      
      if (!response.ok) {
        const error: HttpError = new Error(`HTTP Error: ${response.status} ${response.statusText}`);
        error.status = response.status;
        error.statusText = response.statusText;
        error.response = response;
        throw error;
      }
      
      return await this.applyResponseInterceptors(response);
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        const timeoutError: HttpError = new Error('Request timeout');
        timeoutError.name = 'TimeoutError';
        throw timeoutError;
      }
      
      return this.handleResponseError(error);
    }
  }

  public addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  public addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  public removeRequestInterceptor(interceptor: RequestInterceptor): void {
    const index = this.requestInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.requestInterceptors.splice(index, 1);
    }
  }

  public removeResponseInterceptor(interceptor: ResponseInterceptor): void {
    const index = this.responseInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.responseInterceptors.splice(index, 1);
    }
  }

  public async request<T = any>(url: string, config: RequestConfig = {}): Promise<T> {
    const mergedConfig = { ...this.defaultConfig, ...config };
    const processedConfig = await this.applyRequestInterceptors(mergedConfig);
    
    const response = await (processedConfig.retry ? 
      this.retryRequest(url, processedConfig) : 
      this.makeRequest(url, processedConfig)
    );
    
    const contentType = response.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      return response.json();
    } else if (contentType?.startsWith('text/')) {
      return response.text() as any;
    } else {
      return response.blob() as any;
    }
  }

  public async get<T = any>(url: string, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(url, { ...config, method: 'GET' });
  }

  public async post<T = any>(url: string, data?: any, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(url, {
      ...config,
      method: 'POST',
      body: data ? (typeof data === 'object' ? JSON.stringify(data) : data) : undefined,
    });
  }

  public async put<T = any>(url: string, data?: any, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(url, {
      ...config,
      method: 'PUT',
      body: data ? (typeof data === 'object' ? JSON.stringify(data) : data) : undefined,
    });
  }

  public async patch<T = any>(url: string, data?: any, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(url, {
      ...config,
      method: 'PATCH',
      body: data ? (typeof data === 'object' ? JSON.stringify(data) : data) : undefined,
    });
  }

  public async delete<T = any>(url: string, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(url, { ...config, method: 'DELETE' });
  }

  public async head<T = any>(url: string, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(url, { ...config, method: 'HEAD' });
  }

  public async options<T = any>(url: string, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(url, { ...config, method: 'OPTIONS' });
  }
}

export const http = new HttpClient({
  baseURL: import.meta.env.PUBLIC_API_BASE_URL + "/api",
});

export const createHttpClient = (config: RequestConfig) => new HttpClient(config);

export type { RequestConfig, RequestInterceptor, ResponseInterceptor, HttpError };