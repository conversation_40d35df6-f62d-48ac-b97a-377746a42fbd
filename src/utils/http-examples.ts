import { http, createHttpClient } from './http';

// 基础使用示例
export async function basicUsageExamples() {
  try {
    // GET 请求
    const posts = await http.get('/api/posts');
    console.log('Posts:', posts);

    // POST 请求
    const newPost = await http.post('/api/posts', {
      title: '新文章',
      content: '文章内容'
    });
    console.log('Created post:', newPost);

    // 带参数的GET请求
    const searchResults = await http.get('/api/search', {
      params: { q: 'javascript', page: 1, limit: 10 }
    });
    console.log('Search results:', searchResults);

  } catch (error) {
    console.error('Request failed:', error);
  }
}

// 配置示例
export function configurationExamples() {
  // 创建带基础配置的客户端
  const apiClient = createHttpClient({
    baseURL: 'https://api.example.com',
    timeout: 5000,
    headers: {
      'Authorization': 'Bearer token',
      'X-Custom-Header': 'value'
    }
  });

  // 使用配置的客户端
  return apiClient.get('/user/profile');
}

// 拦截器示例
export function interceptorExamples() {
  // 请求拦截器 - 添加认证token
  http.addRequestInterceptor({
    request: (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers = {
          ...config.headers,
          'Authorization': `Bearer ${token}`
        };
      }
      console.log('Request config:', config);
      return config;
    },
    requestError: (error) => {
      console.error('Request error:', error);
      return Promise.reject(error);
    }
  });

  // 响应拦截器 - 处理认证失败
  http.addResponseInterceptor({
    response: (response) => {
      console.log('Response received:', response.status);
      return response;
    },
    responseError: (error) => {
      if (error.status === 401) {
        // 清除token并重定向到登录页
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  });
}

// 重试机制示例
export async function retryExamples() {
  try {
    // 请求失败时自动重试3次，每次间隔1秒
    const data = await http.get('/api/unstable-endpoint', {
      retry: 3,
      retryDelay: 1000,
      timeout: 3000
    });
    console.log('Data received after retry:', data);
  } catch (error) {
    console.error('Request failed after retries:', error);
  }
}

// 错误处理示例
export async function errorHandlingExamples() {
  try {
    await http.get('/api/nonexistent');
  } catch (error: any) {
    if (error.name === 'TimeoutError') {
      console.error('Request timed out');
    } else if (error.status === 404) {
      console.error('Resource not found');
    } else if (error.status >= 500) {
      console.error('Server error');
    } else {
      console.error('Unknown error:', error.message);
    }
  }
}

// 文件上传示例
export async function uploadExample() {
  const formData = new FormData();
  formData.append('file', document.querySelector('input[type="file"]')?.files?.[0]);
  formData.append('description', '文件描述');

  try {
    const result = await http.post('/api/upload', formData, {
      headers: {
        // 不设置 Content-Type，让浏览器自动设置
      },
      timeout: 30000 // 上传文件需要更长的超时时间
    });
    console.log('Upload result:', result);
  } catch (error) {
    console.error('Upload failed:', error);
  }
}

// 下载文件示例
export async function downloadExample() {
  try {
    const blob = await http.get('/api/download/report.pdf', {
      // 直接返回blob数据
    });
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob as Blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'report.pdf';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Download failed:', error);
  }
}

// 并发请求示例
export async function concurrentRequestsExample() {
  try {
    const [posts, users, comments] = await Promise.all([
      http.get('/api/posts'),
      http.get('/api/users'),
      http.get('/api/comments')
    ]);
    
    console.log('All data loaded:', { posts, users, comments });
  } catch (error) {
    console.error('Some requests failed:', error);
  }
}

// 取消请求示例
export function cancelRequestExample() {
  const controller = new AbortController();
  
  // 发起可取消的请求
  const request = http.get('/api/slow-endpoint', {
    signal: controller.signal
  });
  
  // 5秒后取消请求
  setTimeout(() => {
    controller.abort();
  }, 5000);
  
  request.catch(error => {
    if (error.name === 'AbortError') {
      console.log('Request was cancelled');
    }
  });
}