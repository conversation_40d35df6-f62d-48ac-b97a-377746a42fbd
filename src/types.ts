import type { CollectionEntry } from 'astro:content';

// Type definitions for content collections
export type Post = CollectionEntry<'posts'>;
export type Page = CollectionEntry<'pages'>;

// Post data type for easier access
export type PostData = Post['data'];
export type PageData = Page['data'];

// URL structure types for migration
export interface URLStructure {
  year: string;
  month: string;
  day: string;
  slug: string;
}

// Site configuration type
export interface SiteConfig {
  title: string;
  description: string;
  author: string;
  url: string;
  language: string;
  timezone: string;
  social: {
    github?: string;
    twitter?: string;
    email?: string;
  };
  seo: {
    keywords: string[];
    ogImage: string;
    favicon: string;
    appleTouchIcon: string;
    googleAnalytics?: string;
    baiduAnalytics?: string;
  };
}

// Migration utility types
export interface MigrationReport {
  totalPosts: number;
  migratedPosts: number;
  errors: string[];
  warnings: string[];
}

// SEO types
export interface SEOData {
  title: string;
  description: string;
  keywords?: string[];
  author?: string;
  canonicalURL?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
}

// Pagination types
export interface PaginationData {
  currentPage: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  nextUrl?: string;
  prevUrl?: string;
}