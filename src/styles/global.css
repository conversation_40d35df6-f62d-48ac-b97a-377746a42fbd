@tailwind base;
@tailwind components;
@tailwind utilities;

/* Material Design 基础样式 */
@layer base {
  html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
  }
  
  body {
    @apply text-gray-900 bg-gray-50 font-sans;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 链接样式 */
  a {
    @apply transition-colors duration-200;
  }
  
  /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    @apply text-gray-900 font-semibold;
  }
  
  /* 段落样式 */
  p {
    @apply text-gray-700 leading-relaxed;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200;
  }
  
  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  /* 表单样式 */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }
}

/* 工具类样式 */
@layer utilities {
  /* 文本截断 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 阴影变体 */
  .shadow-elevation-1 {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  }
  
  .shadow-elevation-2 {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  }
  
  .shadow-elevation-3 {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  }
}

/* 特殊内容样式 */
/* Summary 元素特殊样式 */
details {
  @apply bg-blue-50 border-l-4 border-blue-400 px-4 py-3 my-4 rounded-r-lg;
}

details summary {
  @apply cursor-pointer font-medium text-blue-800;
}

/* 代码块样式 */
pre {
  @apply bg-gray-100 rounded-lg p-4 overflow-x-auto text-sm;
}

code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
}

pre code {
  @apply bg-transparent px-0 py-0;
}

/* 引用样式 */
blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4;
}

/* 列表样式 */
ul {
  @apply list-disc ml-6 space-y-1;
}

ol {
  @apply list-decimal ml-6 space-y-1;
}

/* 表格样式 */
table {
  @apply w-full border-collapse border border-gray-300;
}

th, td {
  @apply border border-gray-300 px-4 py-2;
}

th {
  @apply bg-gray-50 font-semibold text-left;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 选择文本样式 */
::selection {
  background: #dbeafe;
  color: #1e40af;
}

/* 焦点样式 */
:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 打印样式 */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  pre, blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  
  img {
    max-width: 100% !important;
  }
  
  h1, h2, h3, h4, h5, h6 {
    break-after: avoid;
  }
}