---
import { siteConfig } from '../config';
---

<!-- Google Analytics -->
{siteConfig.seo.googleAnalytics && (
  <>
    <script async src={`https://www.googletagmanager.com/gtag/js?id=${siteConfig.seo.googleAnalytics}`}></script>
    <script is:inline>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', `${siteConfig.seo.googleAnalytics}`);
    </script>
  </>
)}

<!-- Baidu Analytics -->
{siteConfig.seo.baiduAnalytics && (
  <script is:inline>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = `https://hm.baidu.com/hm.js?${siteConfig.seo.baiduAnalytics}`;
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
  </script>
)}

<!-- Performance Monitoring -->
<script is:inline>
  // Web Vitals monitoring
  function sendToAnalytics(metric) {
    if (typeof gtag !== 'undefined') {
      gtag('event', metric.name, {
        event_category: 'Web Vitals',
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        event_label: metric.id,
        non_interaction: true,
      });
    }
  }

  // Report Web Vitals if available
  try {
    // Only try to import web-vitals if it's available
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(sendToAnalytics);
      getFID(sendToAnalytics);
      getFCP(sendToAnalytics);
      getLCP(sendToAnalytics);
      getTTFB(sendToAnalytics);
    }).catch(() => {
      // web-vitals not available, skip monitoring
    });
  } catch (e) {
    // web-vitals not available, skip monitoring
  }
</script>