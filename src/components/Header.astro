---
import { siteConfig } from '../config';
import SearchModal from './SearchModal.astro';
---

<header class="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
  <div class="container mx-auto px-4">
    <div class="flex items-center justify-between h-16">
      <!-- Logo和标题 -->
      <div class="flex items-center space-x-3">
        <a href="/" class="flex items-center space-x-3 group">
          <div class="w-8 h-8 bg-gray-800 rounded-md flex items-center justify-center text-white font-bold text-sm">
            {siteConfig.author.charAt(0).toUpperCase()}
          </div>
          <div class="hidden sm:block">
            <h1 class="text-xl font-semibold text-gray-900 group-hover:text-gray-600 transition-colors">
              {siteConfig.title}
            </h1>
          </div>
        </a>
      </div>

      <!-- 导航菜单 -->
      <nav class="hidden md:flex items-center space-x-6">
        <a href="/" class="text-gray-700 hover:text-gray-900 transition-colors text-sm font-medium">
          首页
        </a>
        <a href="/archives/" class="text-gray-700 hover:text-gray-900 transition-colors text-sm font-medium">
          归档
        </a>
        <a href="/categories/" class="text-gray-700 hover:text-gray-900 transition-colors text-sm font-medium">
          分类
        </a>
        <a href="/tags/" class="text-gray-700 hover:text-gray-900 transition-colors text-sm font-medium">
          标签
        </a>
        <!-- <a href="/about/" class="text-gray-700 hover:text-gray-900 transition-colors text-sm font-medium">
          关于
        </a> -->
      </nav>

      <!-- 右侧工具 -->
      <div class="flex items-center space-x-4">
        <!-- 搜索按钮 -->
        <button 
          id="search-button"
          class="text-gray-600 hover:text-gray-900 transition-colors p-2" 
          aria-label="搜索"
          title="搜索文章 (Ctrl+K)"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
        </button>
        
        <!-- 移动端菜单按钮 -->
        <button 
          class="md:hidden p-2 text-gray-600 hover:text-gray-900 transition-colors"
          aria-label="菜单"
          id="mobile-menu-button"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div class="md:hidden border-t border-gray-100 hidden" id="mobile-menu">
      <nav class="py-4 space-y-1">
        <a href="/" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors">
          首页
        </a>
        <a href="/archives/" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors">
          归档
        </a>
        <a href="/categories/" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors">
          分类
        </a>
        <a href="/tags/" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors">
          标签
        </a>
        <!-- <a href="/about/" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors">
          关于
        </a> -->
      </nav>
    </div>
  </div>
</header>

<!-- 搜索模态框 -->
<SearchModal />

<script>
  // 移动端菜单切换
  const menuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  menuButton?.addEventListener('click', () => {
    mobileMenu?.classList.toggle('hidden');
  });

  // 搜索功能
  const searchButton = document.getElementById('search-button');
  
  searchButton?.addEventListener('click', () => {
    // 调用搜索模态框的打开函数
    if (typeof (window as any).openSearchModal === 'function') {
      (window as any).openSearchModal();
    }
  });

  // 键盘快捷键支持
  document.addEventListener('keydown', (e) => {
    // Ctrl+K 或 Cmd+K 打开搜索
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault();
      if (typeof (window as any).openSearchModal === 'function') {
        (window as any).openSearchModal();
      }
    }
  });
</script>