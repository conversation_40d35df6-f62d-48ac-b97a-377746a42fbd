---
// 搜索模态框组件
---

<!-- 搜索模态框 -->
<div id="search-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
  <div class="flex items-start justify-center min-h-screen pt-20 px-4">
    <div
      class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden"
    >
      <!-- 搜索头部 -->
      <div class="border-b border-gray-200 px-6 py-4">
        <div class="flex items-center">
          <svg
            class="w-5 h-5 text-gray-400 mr-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input
            type="text"
            id="modal-search-input"
            placeholder="搜索文章标题、内容或标签..."
            class="flex-1 text-lg border-none outline-none placeholder-gray-400"
            autocomplete="off"
          />
          <button
            id="search-close"
            class="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="关闭搜索"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div class="overflow-y-auto max-h-96">
        <!-- 加载状态 -->
        <div id="search-loading" class="hidden p-6 text-center">
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"
          >
          </div>
          <p class="mt-3 text-gray-600">搜索中...</p>
        </div>

        <!-- 搜索结果列表 -->
        <div id="search-results" class="py-2">
          <!-- 结果将在这里动态插入 -->
        </div>

        <!-- 无结果提示 -->
        <div id="search-empty" class="hidden p-8 text-center">
          <svg
            class="w-12 h-12 mx-auto text-gray-400 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            未找到相关文章
          </h3>
          <p class="text-gray-600">请尝试其他关键词</p>
        </div>

        <!-- 默认提示 -->
        <div id="search-tips" class="p-6 text-center text-gray-600">
          <div class="space-y-3">
            <p>🔍 输入关键词搜索文章</p>
            <div class="flex justify-center space-x-4 text-sm">
              <span class="px-2 py-1 bg-gray-100 rounded">标题</span>
              <span class="px-2 py-1 bg-gray-100 rounded">内容</span>
              <span class="px-2 py-1 bg-gray-100 rounded">标签</span>
            </div>
            <p class="text-sm text-gray-500">
              支持搜索文章标题、内容描述和标签
            </p>
          </div>
        </div>
      </div>

      <!-- 搜索底部 -->
      <div class="border-t border-gray-200 px-6 py-3 bg-gray-50">
        <div class="flex items-center justify-between text-sm text-gray-500">
          <div class="flex items-center space-x-4">
            <kbd
              class="px-2 py-1 bg-white border border-gray-300 rounded text-xs"
              >↑↓</kbd
            >
            <span>导航</span>
            <kbd
              class="px-2 py-1 bg-white border border-gray-300 rounded text-xs"
              >Enter</kbd
            >
            <span>打开</span>
          </div>
          <div class="flex items-center space-x-2">
            <kbd
              class="px-2 py-1 bg-white border border-gray-300 rounded text-xs"
              >ESC</kbd
            >
            <span>关闭</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // 搜索模态框功能
  let searchData: any[] = [];
  let currentIndex = -1;
  let searchTimeout: number | null = null;

  // DOM元素
  const searchModal = document.getElementById("search-modal");
  const searchInput = document.getElementById(
    "modal-search-input"
  ) as HTMLInputElement;
  const searchClose = document.getElementById("search-close");
  const searchLoading = document.getElementById("search-loading");
  const searchResults = document.getElementById("search-results");
  const searchEmpty = document.getElementById("search-empty");
  const searchTips = document.getElementById("search-tips");

  // 打开搜索模态框
  function openSearchModal() {
    searchModal?.classList.remove("hidden");
    searchInput?.focus();
    // 如果没有搜索数据，则加载
    if (searchData.length === 0) {
      loadSearchData();
    }
  }

  // 关闭搜索模态框
  function closeSearchModal() {
    searchModal?.classList.add("hidden");
    searchInput.value = "";
    currentIndex = -1;
    showSearchTips();
  }

  // 显示搜索提示
  function showSearchTips() {
    if (searchLoading) searchLoading.classList.add("hidden");
    if (searchResults) searchResults.classList.add("hidden");
    if (searchEmpty) searchEmpty.classList.add("hidden");
    if (searchTips) searchTips.classList.remove("hidden");
  }

  // 显示加载状态
  function showLoading() {
    if (searchTips) searchTips.classList.add("hidden");
    if (searchResults) searchResults.classList.add("hidden");
    if (searchEmpty) searchEmpty.classList.add("hidden");
    if (searchLoading) searchLoading.classList.remove("hidden");
  }

  // 显示搜索结果
  function showResults() {
    if (searchTips) searchTips.classList.add("hidden");
    if (searchLoading) searchLoading.classList.add("hidden");
    if (searchEmpty) searchEmpty.classList.add("hidden");
    if (searchResults) searchResults.classList.remove("hidden");
  }

  // 显示无结果
  function showEmpty() {
    if (searchTips) searchTips.classList.add("hidden");
    if (searchLoading) searchLoading.classList.add("hidden");
    if (searchResults) searchResults.classList.add("hidden");
    if (searchEmpty) searchEmpty.classList.remove("hidden");
  }

  // 加载搜索数据
  async function loadSearchData() {
    try {
      showLoading();

      // 获取所有文章数据
      const response = await fetch("/search-data.json");
      if (!response.ok) {
        throw new Error("Failed to load search data");
      }

      searchData = await response.json();
      showSearchTips();
    } catch (error) {
      console.error("Error loading search data:", error);
      showSearchTips();
    }
  }

  // 搜索功能
  function performSearch(query: string) {
    if (!query.trim()) {
      showSearchTips();
      return;
    }

    const results = searchData.filter((post) => {
      const titleMatch = post.title.toLowerCase().includes(query.toLowerCase());
      const descriptionMatch = post.description
        ?.toLowerCase()
        .includes(query.toLowerCase());
      const tagsMatch = post.tags?.some((tag: string) =>
        tag.toLowerCase().includes(query.toLowerCase())
      );
      const categoriesMatch = post.categories?.some((cat: string) =>
        cat.toLowerCase().includes(query.toLowerCase())
      );

      return titleMatch || descriptionMatch || tagsMatch || categoriesMatch;
    });

    if (results.length === 0) {
      showEmpty();
      return;
    }

    // 渲染搜索结果
    renderSearchResults(results);
    showResults();
    currentIndex = -1;
  }

  // 渲染搜索结果
  function renderSearchResults(results: any[]) {
    if (!searchResults) return;

    searchResults.innerHTML = results
      .map(
        (post, index) => `
      <div class="search-result-item px-6 py-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0" data-index="${index}">
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 font-semibold">
            ${post.title.charAt(0).toUpperCase()}
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-semibold text-gray-900 mb-1 line-clamp-1">
              ${post.title}
            </h3>
            ${post.description ? `<p class="text-gray-600 text-sm mb-2 line-clamp-2">${post.description}</p>` : ""}
            <div class="flex items-center space-x-4 text-xs text-gray-500">
              <span>${post.date}</span>
              ${post.categories?.length ? `<span class="px-2 py-1 bg-blue-100 text-blue-800 rounded">${post.categories[0]}</span>` : ""}
            </div>
          </div>
          <div class="flex-shrink-0 text-gray-400">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          </div>
        </div>
      </div>
    `
      )
      .join("");

    // 绑定点击事件
    searchResults
      .querySelectorAll(".search-result-item")
      .forEach((item, index) => {
        item.addEventListener("click", () => {
          window.location.href = results[index].url;
        });
      });
  }

  // 键盘导航
  function handleKeyNavigation(event: KeyboardEvent) {
    const resultItems = searchResults?.querySelectorAll(".search-result-item");
    if (!resultItems || resultItems.length === 0) return;

    if (event.key === "ArrowDown") {
      event.preventDefault();
      currentIndex = Math.min(currentIndex + 1, resultItems.length - 1);
      updateSelection(resultItems);
    } else if (event.key === "ArrowUp") {
      event.preventDefault();
      currentIndex = Math.max(currentIndex - 1, -1);
      updateSelection(resultItems);
    } else if (event.key === "Enter" && currentIndex >= 0) {
      event.preventDefault();
      (resultItems[currentIndex] as HTMLElement).click();
    }
  }

  // 更新选中状态
  function updateSelection(resultItems: NodeListOf<Element>) {
    resultItems.forEach((item, index) => {
      if (index === currentIndex) {
        item.classList.add("bg-blue-50", "border-blue-200");
      } else {
        item.classList.remove("bg-blue-50", "border-blue-200");
      }
    });
  }

  // 绑定事件监听器
  searchInput?.addEventListener("input", (e) => {
    const query = (e.target as HTMLInputElement).value;

    // 防抖处理
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    searchTimeout = setTimeout(() => {
      performSearch(query);
    }, 300);
  });

  searchInput?.addEventListener("keydown", handleKeyNavigation);

  searchClose?.addEventListener("click", closeSearchModal);

  // 点击背景关闭
  searchModal?.addEventListener("click", (e) => {
    if (e.target === searchModal) {
      closeSearchModal();
    }
  });

  // ESC键关闭
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && !searchModal?.classList.contains("hidden")) {
      closeSearchModal();
    }
  });

  // 导出打开搜索的函数供外部使用
  (window as any).openSearchModal = openSearchModal;
</script>
