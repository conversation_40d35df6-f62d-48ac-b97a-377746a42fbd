---
import { siteConfig } from "../config";
const currentYear = new Date().getFullYear();
---

<footer class="bg-white border-t border-gray-200">
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <!-- 版权信息 -->
      <div class="text-center text-sm text-gray-600">
        <p>© {currentYear} {siteConfig.author}. 保留所有权利。</p>
        <p class="mt-2">
          Built with <a
            href="https://astro.build/"
            target="_blank"
            rel="noopener noreferrer"
            class="text-blue-600 hover:text-blue-800 transition-colors">Astro</a
          >
        </p>
        <!-- 访问统计 -->
        <div class="text-center mt-2" id="busuanzi_container">
          <div
            class="flex flex-wrap justify-center items-center gap-4 text-xs text-gray-400"
          >
            <div id="busuanzi_container_page_pv">
              <span
                >本页访问 <span
                  id="busuanzi_value_page_pv"
                  class="font-medium text-gray-500">-</span
                > 次</span
              >
            </div>
            <div class="hidden sm:block text-gray-300">|</div>
            <div id="busuanzi_container_site_pv">
              <span
                >本站总访问 <span
                  id="busuanzi_value_site_pv"
                  class="font-medium text-gray-500">-</span
                > 次</span
              >
            </div>
            <div class="hidden sm:block text-gray-300">|</div>
            <div>
              <span
                >访客 <span
                  id="busuanzi_value_site_uv"
                  class="font-medium text-gray-500">-</span
                > 人</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>

<!-- 回到顶部按钮 -->
<button
  id="back-to-top"
  class="fixed bottom-8 right-8 bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition-all duration-300 opacity-0 invisible z-50"
  aria-label="回到顶部"
>
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
  </svg>
</button>

<script>
  // 回到顶部功能
  const backToTopButton = document.getElementById("back-to-top");

  function toggleBackToTop() {
    if (window.pageYOffset > 300) {
      backToTopButton?.classList.remove("opacity-0", "invisible");
      backToTopButton?.classList.add("opacity-100", "visible");
    } else {
      backToTopButton?.classList.add("opacity-0", "invisible");
      backToTopButton?.classList.remove("opacity-100", "visible");
    }
  }

  window.addEventListener("scroll", toggleBackToTop);

  backToTopButton?.addEventListener("click", () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  });

  // 访问统计脚本加载
  const loadBusuanziScript = () => {
    const script = document.createElement("script");
    script.src =
      "https://busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js";
    script.async = true;
    script.onload = () => {
      console.log("访问统计脚本加载成功");
    };
    script.onerror = () => {
      console.warn("访问统计脚本加载失败");
      // 隐藏整个统计容器
      const container = document.getElementById("busuanzi_container");
      if (container) {
        container.style.display = "none";
      }
    };
    document.head.appendChild(script);
  };

  // 页面加载完成后加载统计脚本
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", loadBusuanziScript);
  } else {
    loadBusuanziScript();
  }
</script>
