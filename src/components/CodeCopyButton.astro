---
// 代码复制按钮组件
---

<button 
  class="code-copy-btn absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-2 rounded-md bg-gray-700/80 hover:bg-gray-600 text-white text-sm flex items-center gap-1.5 backdrop-blur-sm border border-gray-600/50"
  title="复制代码"
  aria-label="复制代码"
>
  <svg class="copy-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
  </svg>
  <svg class="check-icon w-4 h-4 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
  </svg>
  <span class="copy-text">复制</span>
</button>

<script>
  function setupCodeCopyButtons() {
    // 为所有代码块添加复制按钮
    const codeBlocks = document.querySelectorAll('pre');
    
    codeBlocks.forEach((codeBlock, index) => {
      // 为代码块添加相对定位和group类以支持hover效果
      codeBlock.classList.add('relative', 'group');
      
      // 创建复制按钮
      const copyButton = document.createElement('button');
      copyButton.className = 'code-copy-btn absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-2 rounded-md bg-gray-700/80 hover:bg-gray-600 text-white text-sm flex items-center gap-1.5 backdrop-blur-sm border border-gray-600/50';
      copyButton.title = '复制代码';
      copyButton.setAttribute('aria-label', '复制代码');
      
      copyButton.innerHTML = `
        <svg class="copy-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
        </svg>
        <svg class="check-icon w-4 h-4 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
        <span class="copy-text">复制</span>
      `;
      
      // 添加点击事件
      copyButton.addEventListener('click', async () => {
        const code = codeBlock.querySelector('code');
        if (!code) return;
        
        const text = code.textContent || code.innerText;
        
        try {
          await navigator.clipboard.writeText(text);
          
          // 显示成功状态
          const copyIcon = copyButton.querySelector('.copy-icon');
          const checkIcon = copyButton.querySelector('.check-icon');
          const copyText = copyButton.querySelector('.copy-text');
          
          copyIcon?.classList.add('hidden');
          checkIcon?.classList.remove('hidden');
          if (copyText) copyText.textContent = '已复制';
          
          copyButton.classList.remove('hover:bg-gray-600');
          copyButton.classList.add('bg-green-600', 'hover:bg-green-500');
          
          // 2秒后恢复原状态
          setTimeout(() => {
            copyIcon?.classList.remove('hidden');
            checkIcon?.classList.add('hidden');
            if (copyText) copyText.textContent = '复制';
            
            copyButton.classList.add('hover:bg-gray-600');
            copyButton.classList.remove('bg-green-600', 'hover:bg-green-500');
          }, 2000);
          
        } catch (err) {
          console.error('复制失败:', err);
          
          // 显示错误状态
          const copyText = copyButton.querySelector('.copy-text');
          if (copyText) copyText.textContent = '复制失败';
          
          setTimeout(() => {
            if (copyText) copyText.textContent = '复制';
          }, 2000);
        }
      });
      
      // 将按钮添加到代码块中
      codeBlock.appendChild(copyButton);
    });
  }
  
  // DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupCodeCopyButtons);
  } else {
    setupCodeCopyButtons();
  }
  
  // 支持SPA路由的页面更新
  document.addEventListener('astro:page-load', setupCodeCopyButtons);
</script>

<style>
  .code-copy-btn {
    /* 确保按钮在代码块上方 */
    z-index: 10;
  }
  
  /* 暗色模式支持 */
  @media (prefers-color-scheme: dark) {
    .code-copy-btn {
      @apply bg-gray-800/90 border-gray-500/50 hover:bg-gray-700;
    }
  }
  
  /* 移动设备上始终显示按钮 */
  @media (max-width: 768px) {
    .code-copy-btn {
      opacity: 1;
    }
  }
  
  /* 确保按钮不影响代码块滚动 */
  pre {
    padding-right: 60px !important;
  }
</style>