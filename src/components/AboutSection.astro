---
import { siteConfig } from '@/config'
---

<!-- 重要信息展示 -->
<section class="pb-8 bg-white">
  <div class="container mx-auto px-4">
    <div class="max-w-4xl mx-auto">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- GitHub 项目 -->
        <div class="bg-gray-50 rounded-lg border border-gray-200 hover:shadow-md transition-all duration-200 p-6 group">
          <div class="flex items-center mb-3">
            <div class="w-10 h-10 bg-gray-900 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900 group-hover:text-gray-700 transition-colors">开源项目</h3>
              <p class="text-sm text-gray-600">GitHub 代码仓库</p>
            </div>
          </div>
          <p class="text-gray-700 text-sm mb-4 leading-relaxed">
            探索我的设计和技术项目，包括 RenderedWeekly - 一个专注于前端设计和开发灵感的网站，每周精选优质资源和创意案例。
          </p>
          <a href={siteConfig.social.github} target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium">
            访问 GitHub
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
            </svg>
          </a>
        </div>

        <!-- 技术栈 -->
        <div class="bg-gray-50 rounded-lg border border-gray-200 hover:shadow-md transition-all duration-200 p-6 group">
          <div class="flex items-center mb-3">
            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900 group-hover:text-gray-700 transition-colors">技术专长</h3>
              <p class="text-sm text-gray-600">前端技术栈</p>
            </div>
          </div>
          <p class="text-gray-700 text-sm mb-4 leading-relaxed">
            专注于现代前端开发，熟悉 JavaScript、React、Vue、TypeScript 等技术。
          </p>
          <div class="flex flex-wrap gap-2">
            {siteConfig.seo.keywords.slice(0, 4).map(keyword => (
              <span class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                {keyword}
              </span>
            ))}
          </div>
        </div>

        <!-- 联系方式 -->
        <div class="bg-gray-50 rounded-lg border border-gray-200 hover:shadow-md transition-all duration-200 p-6 group">
          <div class="flex items-center mb-3">
            <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900 group-hover:text-gray-700 transition-colors">联系交流</h3>
              <p class="text-sm text-gray-600">技术讨论 · 合作机会</p>
            </div>
          </div>
          <p class="text-gray-700 text-sm mb-4 leading-relaxed">
            如果有商业合作，可以通过邮箱联系我。
          </p>
          <a href={`mailto:${siteConfig.social.email}`} class="inline-flex items-center text-green-600 hover:text-green-800 transition-colors text-sm font-medium">
            发送邮件
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>
