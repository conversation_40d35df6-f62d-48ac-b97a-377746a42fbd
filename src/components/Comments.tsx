import { createSignal, For, Show, onMount } from 'solid-js';
import {  postComments, postCommentsPage, type Comment } from '../api/index';

// 评论工具类
class CommentStore {
  private currentPageUrl: string;

  constructor() {
    this.currentPageUrl = typeof window !== 'undefined' ? window.location.pathname : '/';
  }

  getCurrentPageUrl(): string {
    return this.currentPageUrl;
  }


  /**
   * 格式化时间
   * @param timestamp 时间戳
   * @returns 格式化后的时间
   */
  formatTime(timestamp: number): string {
    const now = Date.now();
    const commentTime = timestamp * 1000;
    const diff = now - commentTime;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 30) return `${days}天前`;
    
    return new Date(commentTime).toLocaleDateString('zh-CN');
  }

  getTotalCommentCount(comments: Comment[]): number {
    return comments.reduce((total, comment) => total + 1 + comment.replies.length, 0);
  }
}

// Toast 通知组件
function Toast(props: { message: string; type: 'success' | 'error'; show: boolean }) {
  return (
    <Show when={props.show}>
      <div class={`fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white text-sm font-medium transition-all duration-300 ${
        props.type === 'success' ? 'bg-green-500' : 'bg-red-500'
      }`}>
        {props.message}
      </div>
    </Show>
  );
}

// 评论表单组件
function CommentForm(props: { 
  onSubmit: (nickname: string, content: string) => Promise<void>;
  placeholder?: string;
  submitText?: string;
  onCancel?: () => void;
}) {
  const [nickname, setNickname] = createSignal('');
  const [content, setContent] = createSignal('');
  const [isSubmitting, setIsSubmitting] = createSignal(false);

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!nickname().trim() || !content().trim()) return;
    
    setIsSubmitting(true);
    
    try {
      await props.onSubmit(nickname().trim(), content().trim());
      setNickname('');
      setContent('');
    } catch (error) {
      console.error('Submit failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} class="space-y-4">
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            昵称 <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={nickname()}
            onInput={(e) => setNickname(e.currentTarget.value)}
            required
            maxlength="20"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="请输入您的昵称"
          />
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          评论内容 <span class="text-red-500">*</span>
        </label>
        <textarea
          value={content()}
          onInput={(e) => setContent(e.currentTarget.value)}
          required
          maxlength="500"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-vertical"
          placeholder={props.placeholder || "请输入您的评论内容..."}
        />
        <div class="flex justify-between items-center mt-2">
          <span class="text-xs text-gray-500">支持换行，最多500字</span>
          <span class={`text-xs ${content().length > 450 ? 'text-red-500' : 'text-gray-500'}`}>
            {content().length}/500
          </span>
        </div>
      </div>
      
      <div class="flex justify-end space-x-2">
        <Show when={props.onCancel}>
          <button
            type="button"
            onClick={props.onCancel}
            class="px-4 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            取消
          </button>
        </Show>
        <button
          type="submit"
          disabled={isSubmitting() || !nickname().trim() || !content().trim()}
          class="px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting() ? '发布中...' : (props.submitText || '发布评论')}
        </button>
      </div>
    </form>
  );
}

// 单个评论组件
function CommentItem(props: { 
  comment: Comment; 
  store: CommentStore;
  onReply: (parentId: string, nickname: string, content: string) => Promise<void>;
  isReply?: boolean;
}) {
  const [showReplyForm, setShowReplyForm] = createSignal(false);

  const handleReply = async (nickname: string, content: string) => {
    await props.onReply(props.comment.id, nickname, content);
    setShowReplyForm(false);
  };

  return (
    <div class={`comment-item ${props.isReply ? 'ml-8 sm:ml-12 border-l-2 border-gray-200 pl-4' : ''}`}>
      <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
            {props.comment.nickname.charAt(0).toUpperCase()}
          </div>
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-2 mb-2">
              <h4 class="text-sm font-semibold text-gray-900">{props.comment.nickname}</h4>
              <span class="text-xs text-gray-500">{props.store.formatTime(props.comment.timestamp)}</span>
            </div>
            <div class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
              {props.comment.content}
            </div>
            <Show when={!props.isReply}>
              <div class="mt-3 flex items-center space-x-4">
                <button 
                  onClick={() => setShowReplyForm(!showReplyForm())}
                  class="text-xs text-gray-500 hover:text-blue-600 transition-colors flex items-center gap-1"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                  </svg>
                  回复
                </button>
              </div>
            </Show>
          </div>
        </div>
      </div>
      
      <Show when={showReplyForm()}>
        <div class="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div class="flex items-center gap-2 mb-3 text-sm text-gray-600">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
            </svg>
            回复 <span class="font-medium">{props.comment.nickname}</span>
          </div>
          <CommentForm
            onSubmit={handleReply}
            placeholder="请输入回复内容..."
            submitText="回复"
            onCancel={() => setShowReplyForm(false)}
          />
        </div>
      </Show>
      
      <Show when={props.comment.replies.length > 0}>
        <div class="replies mt-4 space-y-4">
          <For each={props.comment.replies}>
            {(reply) => (
              <CommentItem 
                comment={reply} 
                store={props.store}
                onReply={props.onReply}
                isReply={true}
              />
            )}
          </For>
        </div>
      </Show>
    </div>
  );
}

// 主评论组件
export default function Comments() {
  const [comments, setComments] = createSignal<Comment[]>([]);
  const [toast, setToast] = createSignal<{ message: string; type: 'success' | 'error'; show: boolean }>({
    message: '', type: 'success', show: false
  });
  
  const store = new CommentStore();

  onMount(async () => {
    await loadComments();
  });

  const loadComments = async () => {
    try {
      const response = await postCommentsPage({
        pageUrl: store.getCurrentPageUrl()
      });
      if (response.success) {
        setComments(response.data);
      } else {
        showToast(response.message || '加载评论失败', 'error');
      }
    } catch (error) {
      console.error('Failed to load comments:', error);
      showToast('加载评论失败', 'error');
    }
  };

  const showToast = (message: string, type: 'success' | 'error') => {
    setToast({ message, type, show: true });
    setTimeout(() => {
      setToast(prev => ({ ...prev, show: false }));
    }, 3000);
  };

  const addComment = async (nickname: string, content: string, parentId?: string) => {
    try {
      const response = await postComments({
        nickname,
        content,
        pageUrl: store.getCurrentPageUrl(),
        parentId
      });

      if (response.success) {
        // 重新加载评论列表以确保数据同步
        await loadComments();
        showToast('评论发布成功！', 'success');
      } else {
        showToast(response.message || '发布评论失败', 'error');
      }
    } catch (error) {
      console.error('Failed to create comment:', error);
      showToast('发布评论失败', 'error');
    }
  };

  const handleMainComment = async (nickname: string, content: string) => {
    await addComment(nickname, content);
  };

  const handleReply = async (parentId: string, nickname: string, content: string) => {
    await addComment(nickname, content, parentId);
  };

  const totalComments = () => store.getTotalCommentCount(comments());

  return (
    <section class="mt-12 border-t border-gray-200 pt-8">
      <div class="max-w-4xl mx-auto">
        <h3 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center gap-2">
          <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          评论
          <span class="text-sm font-normal text-gray-500">({totalComments()})</span>
        </h3>

        {/* 评论表单 */}
        <div class="mb-8 bg-white border border-gray-200 rounded-lg p-6">
          <CommentForm onSubmit={handleMainComment} />
        </div>

        {/* 评论列表 */}
        <Show when={comments().length > 0} fallback={
          <div class="text-center py-12 text-gray-500">
            <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <p class="text-lg font-medium text-gray-400">暂无评论</p>
            <p class="text-sm text-gray-400 mt-1">来发表第一条评论吧！</p>
          </div>
        }>
          <div class="space-y-6">
            <For each={comments()}>
              {(comment) => (
                <CommentItem 
                  comment={comment} 
                  store={store}
                  onReply={handleReply}
                />
              )}
            </For>
          </div>
        </Show>

        {/* Toast 通知 */}
        <Toast {...toast()} />
      </div>
    </section>
  );
}