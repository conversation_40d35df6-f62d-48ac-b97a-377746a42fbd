import { http } from "../utils/http";

// Comment interfaces
export interface Comment {
  id: string;
  nickname: string;
  content: string;
  timestamp: number;
  pageUrl: string;
  parentId?: string;
  replies: Comment[];
}

export interface CreateCommentRequest {
  nickname: string;
  content: string;
  pageUrl: string;
  parentId?: string;
}

export interface CreateCommentResponse {
  success: boolean;
  comment: Comment;
  message?: string;
}

export interface GetCommentsResponse {
  success: boolean;
  data: Comment[];
  total: number;
  message?: string;
}

/** 按页面获取评论 */
export const postCommentsPage = (data: { pageUrl: string }) =>
  http.post<GetCommentsResponse>(`/comments/page`, data);

/** 创建评论 */
export const postComments = (data: CreateCommentRequest) =>
  http.post<GetCommentsResponse>(`/comments`, data);

/** 创建回复 */
export const postCommentsReply = (id: string) =>
  http.post<GetCommentsResponse>(`/comments/${id}/reply`);
