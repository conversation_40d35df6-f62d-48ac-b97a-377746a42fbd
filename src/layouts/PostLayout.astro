---
import BaseLayout from './BaseLayout.astro';
import CodeCopyButton from '../components/CodeCopyButton.astro';
import dayjs from 'dayjs';
import Comments from '@/components/Comments';

const { post, prevPost, nextPost }: { post: any, prevPost?: any, nextPost?: any } = Astro.props;
const { title, description, date, tags, categories } = post.data;

// 生成文章链接的工具函数
function getPostUrl(post: any) {
  const date = new Date(post.data.date);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const slug = post.slug.replace(/\.md$/, '');
  return `/${year}/${month}/${day}/${slug}/`;
}
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
---

<BaseLayout
  title={title}
  description={description}
  keywords={tags?.join(',')}
  canonicalURL={canonicalURL.toString()}
>
  <!-- Open Graph -->
  <meta property="og:title" content={title} slot="head">
  <meta property="og:description" content={description} slot="head">
  <meta property="og:type" content="article" slot="head">
  <meta property="og:url" content={canonicalURL} slot="head">
  <meta property="og:site_name" content="沧沧凉凉的个人博客" slot="head">
  <meta property="og:image" content={new URL('/img/og-image.png', Astro.site).toString()} slot="head">
  <meta property="og:image:width" content="1200" slot="head">
  <meta property="og:image:height" content="630" slot="head">
  <meta property="article:published_time" content={date.toISOString()} slot="head">
  <meta property="article:author" content="沧沧凉凉" slot="head">
  {categories && categories.map((cat: string) => (
    <meta property="article:section" content={cat} slot="head">
  ))}
  {tags && tags.map((tag: string) => (
    <meta property="article:tag" content={tag} slot="head">
  ))}
  
  <!-- Twitter Cards -->
  <meta name="twitter:card" content="summary_large_image" slot="head">
  <meta name="twitter:title" content={title} slot="head">
  <meta name="twitter:description" content={description} slot="head">
  <meta name="twitter:image" content={new URL('/img/og-image.png', Astro.site).toString()} slot="head">
  
  <!-- 结构化数据 -->
  <script type="application/ld+json" slot="head" is:inline>
    {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      "headline": `${title}`,
      "description": `${description}`,
      "image": `${new URL('/img/og-image.png', Astro.site).toString()}`,
      "author": {
        "@type": "Person",
        "name": "沧沧凉凉",
        "url": "https://www.cclliang.com/about/"
      },
      "datePublished": `${date.toISOString()}`,
      "dateModified": `${date.toISOString()}`,
      "url": `${canonicalURL}`,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `${canonicalURL}`
      },
      "publisher": {
        "@type": "Organization",
        "name": "沧沧凉凉的个人博客",
        "logo": {
          "@type": "ImageObject",
          "url": "https://www.cclliang.com/img/avatar.png"
        }
      },
      "keywords": `${tags?.join(', ') || ''}`,
      "articleSection": `${categories?.[0] || 'Web开发'}`,
      "inLanguage": "zh-CN",
      "copyrightHolder": {
        "@type": "Person",
        "name": "沧沧凉凉"
      },
      "license": "https://creativecommons.org/licenses/by-nc-sa/4.0/"
    }
  </script>

  <!-- Breadcrumb Schema -->
  <script type="application/ld+json" slot="head" is:inline>
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://www.cclliang.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": `${categories?.[0] || 'Web开发'}`,
          "item": `https://www.cclliang.com/categories/${categories?.[0] || 'web开发'}/`
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": `${title}`,
          "item": `${canonicalURL}`
        }
      ]
    }
  </script>

  <main class="container mx-auto px-4 py-8">
    <article class="max-w-4xl mx-auto">
      <header class="mb-8">
        <h1 class="text-3xl font-bold mb-4">{title}</h1>
        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
          <time datetime={date.toISOString()} class="flex items-center gap-1">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            {dayjs(date).format('YYYY-MM-DD')}
          </time>
          
          {categories && categories.length > 0 && (
            <div class="flex items-center gap-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
              <div class="flex flex-wrap gap-1">
                {categories.map(cat => (
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">{cat}</span>
                ))}
              </div>
            </div>
          )}
          
          {tags && tags.length > 0 && (
            <div class="flex items-center gap-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
              <div class="flex flex-wrap gap-1">
                {tags.map(tag => (
                  <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">{tag}</span>
                ))}
              </div>
            </div>
          )}
        </div>
      </header>
      
      <div class="prose prose-lg max-w-none">
        <slot />
      </div>
      
      <!-- 转载协议 -->
      <div class="border border-gray-200 bg-gray-50 rounded-lg p-4 my-8">
        <div class="flex items-start gap-3">
          <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <div class="flex-1">
            <h4 class="font-medium text-gray-900 mb-2">转载协议</h4>
            <p class="text-sm text-gray-700 mb-2">
              本文采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">CC BY-NC-SA 4.0</a> 协议进行许可，转载请注明出处。
            </p>
            <p class="text-xs text-gray-600">
              允许转载、修改和分享，但必须注明作者和出处，且不得用于商业用途，衍生作品需采用相同协议。
            </p>
          </div>
        </div>
      </div>
      
      <!-- 文章导航 -->
      {(prevPost || nextPost) && (
        <nav class="border-t border-gray-200 mt-12 pt-8">
          <div class="flex flex-col sm:flex-row justify-between gap-6">
            {prevPost ? (
              <div class="flex-1">
                <div class="text-sm text-gray-500 mb-2 flex items-center gap-1">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                  上一篇
                </div>
                <a 
                  href={getPostUrl(prevPost)} 
                  class="block p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group"
                >
                  <h3 class="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                    {prevPost.data.title}
                  </h3>
                  {prevPost.data.description && (
                    <p class="text-sm text-gray-600 mt-2">
                      {prevPost.data.description.length > 100 ? prevPost.data.description.slice(0, 100) + '...' : prevPost.data.description}
                    </p>
                  )}
                  <div class="text-xs text-gray-500 mt-2">
                    {dayjs(prevPost.data.date).format('YYYY-MM-DD')}
                  </div>
                </a>
              </div>
            ) : (
              <div class="flex-1"></div>
            )}
            
            {nextPost ? (
              <div class="flex-1">
                <div class="text-sm text-gray-500 mb-2 flex items-center justify-end gap-1">
                  下一篇
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
                <a 
                  href={getPostUrl(nextPost)} 
                  class="block p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group text-right"
                >
                  <h3 class="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                    {nextPost.data.title}
                  </h3>
                  {nextPost.data.description && (
                    <p class="text-sm text-gray-600 mt-2">
                      {nextPost.data.description.length > 100 ? nextPost.data.description.slice(0, 100) + '...' : nextPost.data.description}
                    </p>
                  )}
                  <div class="text-xs text-gray-500 mt-2">
                    {dayjs(nextPost.data.date).format('YYYY-MM-DD')}
                  </div>
                </a>
              </div>
            ) : (
              <div class="flex-1"></div>
            )}
          </div>
         <Comments client:only="load"/>
        </nav>
      )}
    </article>
  </main>
  <!-- 代码复制按钮组件 -->
  <CodeCopyButton />
</BaseLayout>