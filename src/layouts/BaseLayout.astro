---
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import AnalyticsScript from '../components/AnalyticsScript.astro';
import { siteConfig } from '../config';

export interface Props {
  title: string;
  description?: string;
  keywords?: string;
  canonicalURL?: string;
}

const { title, description, keywords, canonicalURL } = Astro.props;
const defaultDescription = siteConfig.description;
const defaultKeywords = siteConfig.seo.keywords.join(',');
---

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <title>{title} - {siteConfig.title}</title>
  <meta name="description" content={description || defaultDescription}>
  <meta name="keywords" content={keywords || defaultKeywords}>
  <meta name="author" content={siteConfig.author}>
  <meta name="generator" content="Astro">
  <meta name="theme-color" content="#2f4154">
  <meta name="format-detection" content="telephone=no">
  
  <!-- Security -->
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  
  <!-- Canonical URL -->
  {canonicalURL && <link rel="canonical" href={canonicalURL}>}
  
  <!-- Icons -->
  <link rel="apple-touch-icon" sizes="180x180" href={siteConfig.seo.appleTouchIcon}>
  <link rel="icon" type="image/png" sizes="32x32" href={siteConfig.seo.favicon}>
  <link rel="icon" type="image/png" sizes="16x16" href={siteConfig.seo.favicon}>
  <meta name="msapplication-TileColor" content="#2f4154">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Open Graph -->
  <meta property="og:site_name" content={siteConfig.title}>
  <meta property="og:type" content="website">
  <meta property="og:title" content={title}>
  <meta property="og:description" content={description || defaultDescription}>
  <meta property="og:url" content={canonicalURL || siteConfig.url}>
  <meta property="og:image" content={new URL(siteConfig.seo.ogImage, siteConfig.url).toString()}>
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="zh_CN">
  
  <!-- Twitter Cards -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content={title}>
  <meta name="twitter:description" content={description || defaultDescription}>
  <meta name="twitter:image" content={new URL(siteConfig.seo.ogImage, siteConfig.url).toString()}>
  
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" href="/sitemap.xml">
  
  <!-- RSS Feed -->
  <link rel="alternate" type="application/rss+xml" title="RSS Feed" href="/rss.xml">
  
  <slot name="head" />
  
  <!-- Analytics Scripts -->
  <AnalyticsScript />
</head>
<body class="bg-white">
  <Header />
  <slot />
  <Footer />
  
  <slot name="scripts" />
</body>
</html>