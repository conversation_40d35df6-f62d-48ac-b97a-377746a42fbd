import { defineCollection, z } from 'astro:content';
import dayjs from 'dayjs';

const dateTransform = z.preprocess((val) => {
  if(val instanceof Date) {
    const date = dayjs(val);
    
    return date.subtract(8, 'hour').toDate();
  }

  return val;
}, z.date());

// Define the posts collection schema to match Hexo front matter structure
const postsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    // Required fields
    title: z.string(),
    date: dateTransform,
    categories: z.array(z.string()).optional(),
    
    // Optional fields that may exist in Hexo posts
    description: z.string().optional(),
    tags: z.array(z.string()).optional(),
    draft: z.boolean().default(false),
    updated: dateTransform.optional(),
    
    // SEO and social media fields
    keywords: z.array(z.string()).optional(),
    author: z.string().optional(),
    
    // Image and media fields
    cover: z.string().optional(),
    thumbnail: z.string().optional(),
    
    // Additional metadata that might exist in Hexo
    permalink: z.string().optional(),
    comments: z.boolean().default(true),
    toc: z.boolean().default(true),
    
    // Custom fields for migration tracking
    hexo_id: z.string().optional(),
    migrated_from: z.string().optional(),
  }),
});

// Define pages collection for static pages (about, links, etc.)
const pagesCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string().optional(),
    layout: z.string().optional(),
    date: dateTransform.optional(),
    updated: dateTransform.optional(),
    comments: z.boolean().default(false),
    toc: z.boolean().default(false),
  }),
});

export const collections = {
  posts: postsCollection,
  pages: pagesCollection,
};