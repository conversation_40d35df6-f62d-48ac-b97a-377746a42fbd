---
title: "GitHub为什么连接缓慢以及解决方法"
date: 2020-8-8 22:43:37
categories:
  - "其它"
tags:
  - "GitHub"
---


# 1. 前言

之前有一篇文章介绍了`Git`的使用方法：[项目或者文件管理神器-Git](/2020/07/13/项目或者文件管理神器-git/)。

一般对于电脑有一定认知的人都会知道`GitHub`，但是因为`GitHub`的服务器在国外，所以有时候连接速度非常的感人。

我们通过`ipip.net`的路由跟踪来看一下我们访问`GitHub`走的线路。

![image-20200806235520307](/images/image-20200806235520307.png)

可以看到在广州出口走的骨干网到新加坡的服务器，这就是为什么访问`GitHub`有时候会异常缓慢的原因，尤其是在晚高峰时期，通过骨干网出口的数据量过于庞大，电信还会选择权重不高的用户进行丢包，从而缓解骨干网拥挤的问题。

具体的服务器和线路详解可以参考：[VPS的各种线路到底有什么区别](/2020/07/21/vps的各种线路到底有什么区别/)。

# 2. 终极解决办法

看了网上有非常多的帖子说修改`host`，但是我个人认为，修改`host`并不能解决骨干网晚高峰拥挤的问题。

终极解决方法是通过代理服务器。

首先你需要有一个境外的服务器，参考[VPS的各种线路到底有什么区别](/2020/07/21/vps的各种线路到底有什么区别/)该文章中讲解的服务器，或者你有其它的代理软件也可以，但是**连接代理服务器时的出口线路一定不能是骨干网，不然跟直连没有任何区别，依然连接缓慢**。

关于代理服务器这里就不详细讲解了，这篇文章着重讲解如何通过代理服务器来连接`GitHub`，从而摆脱缓慢的`Clone`速度。

## 2.1 代码

```bash
# 设置ss
git config --global http.proxy socks5://127.0.0.1:1080

git config --global https.proxy socks5://127.0.0.1:1080

# 设置代理
git config --global https.proxy http://127.0.0.1:1080

git config --global https.proxy https://127.0.0.1:1080

# 取消代理
git config --global --unset http.proxy

git config --global --unset https.proxy

# 仅代理GitHub
git config --global http.https://github.com.proxy https://127.0.0.1:1080

git config --global https.https://github.com.proxy https://127.0.0.1:1080
```

## 2.2 步骤

首先需要知道你的代理服务器使用的端口号，在你的代理软件上可以看到，一般是`1080`，但是`Clash`默认使用的是`7890`。

1. 打开Git命令窗口，该窗口安装Git后在文件夹中点击右键选择`Git bash here`就会出现：

![image-20200807001106816](/images/image-20200807001106816.png)

2. 输入`git config --global http.proxy socks5://127.0.0.1:1080`点击回车。（`1080`需要填写你的代理软件使用的端口号，我的是`7890`）

![image-20200807005413488](/images/image-20200807005413488.png)

3. 没有任何消息就是最好的消息，这个时候就设置完成了。

**注意：其它的设置可以参考上面给出的代码。**

# 3. 速度测试

## 3.1 没有用代理的速度

![image-20200807004012064](/images/image-20200807004012064.png)

## 3.2 用代理后的速度

设置完毕后，`Clone`和`git push`的速度就跟你代理服务器的质量密切相关。

![image-20200807001636943](/images/image-20200807001636943.png)

# 4. 使用镜像站

该方法仅对`git clone`有用，对`git push`无效，并且无法登陆账号，私人项目无法进行`Clone`。

比如`Clone` `Vue`项目，项目地址：`https://github.com/vuejs/vue.git`。

则将地址改为`https://github.com.cnpmjs.org/vuejs/vue.git`。

## 4.1 速度

![image-20200807011539081](/images/image-20200807011539081.png)

只需要在`Clone`时将项目地址中的`github.com`改为`github.com.cnpmjs.org`，就可以使用镜像站。

那么为什么该镜像网站会这么快呢？我们再次通过`ipip.net`的路由跟踪测试一下：

![image-20200807011733439](/images/image-20200807011733439.png)

可以看到，在省级城市已经走上了`59.43`开头的线路，说明该服务器用的是`CN2 GIA`的线路，所以拥有这么快的传输速度完全不奇怪。
