---
title: "仅用CSS实现fullpage.js全屏滚动效果"
date: 2021-3-10 00:05:53
categories:
  - "CSS"
tags:
  - "CSS"
---


最近研究CSS的时候发现了**仅使用两个CSS属性就可以制作出全屏滚动效果**，这两个属性就是：

- scroll-snap-type
- scroll-snap-align

使用它就可以实现`fullpage.js`这种全屏滚动效果，其实，这种全屏滚动效果的理论非常简单，就是使用js监听界面滚动，当界面滚动到某个值时就让界面持续滚动到下一个屏幕，但是！要考虑到屏幕尺寸大小带来的兼容性问题就是一件非常麻烦的事情。

今天说的这两个属性并不能替代`fullpage.js`，有下面2个原因：

1. 它们在浏览器上面存在兼容性问题。
2. CSS属性无法监听事件，也就无法提供动画完成时的回调函数。

# 1. 兼容性

目前主流的浏览器都已经支持了这两个CSS属性，可以放心的使用。如果你需要兼容IE浏览器，那么请选择`fullpage.js`。

# 2. 使用

使用的方法其实很简单，`scroll-snap-type`属性放在**需要全屏滚动的容器的父容器上**，而`scroll-snap-align`则需要**放在全屏滚动的容器上**，多说无益，我们直接来看一下代码就可以很清楚的知道如何使用这两个CSS属性。

![img](/images/css/仅用CSS实现fullpage.js全屏滚动效果/CSS-scroll-snap.gif)

整个网页的完整代码很简单，下面直接将它贴上来：

```html
<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CSS scroll snap</title>
    <style>
      body {
        margin: 0;
      }

      .container {
        height: 100vh;
        overflow-y: scroll;
        /* 在父容器上面使用 scroll-snap-type 属性 */
        scroll-snap-type: y mandatory;
      }

      section {
        padding: 112px;
        height: calc(100vh - 224px);
        color: white;
        /* 在需要滚动的容器上使用 scroll-snap-align 属性 */
        scroll-snap-align: start;
      }

      section:nth-of-type(1) {
        background-color: #60af15;
      }

      section:nth-of-type(2) {
        background-color: #158baf;
      }

      section:nth-of-type(3) {
        background-color: #af1581;
      }

      section h3 {
        font-size: 48px;
      }

      section p {
        font-size: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <section>
        <h3>A subtitle lives here</h3>
        <p>
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Accusamus
          deleniti dignissimos ducimus expedita iure maxime qui rerum veniam
          voluptatibus. Accusamus asperiores assumenda atque consectetur
          consequuntur culpa cum deserunt dicta distinctio error excepturi fuga
          ipsa iste magnam modi nobis, obcaecati, pariatur perspiciatis placeat
          quo quod reiciendis repudiandae saepe soluta tempora unde vel? Aliquam
          exercitationem iste maiores placeat reprehenderit voluptates
          voluptatum. Ad at commodi culpa cumque debitis delectus dolorum, eius
          error et explicabo harum in ipsum iste labore laborum libero magni
          maiores nam non nostrum nulla officia pariatur quam quasi quia quo
          recusandae reprehenderit saepe similique vel vero vitae voluptas
          voluptatem! Quibusdam.
        </p>
      </section>
      <section>
        <h3>A subtitle lives here</h3>
        <p>
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Accusamus
          deleniti dignissimos ducimus expedita iure maxime qui rerum veniam
          voluptatibus. Accusamus asperiores assumenda atque consectetur
          consequuntur culpa cum deserunt dicta distinctio error excepturi fuga
          ipsa iste magnam modi nobis, obcaecati, pariatur perspiciatis placeat
          quo quod reiciendis repudiandae saepe soluta tempora unde vel? Aliquam
          exercitationem iste maiores placeat reprehenderit voluptates
          voluptatum. Ad at commodi culpa cumque debitis delectus dolorum, eius
          error et explicabo harum in ipsum iste labore laborum libero magni
          maiores nam non nostrum nulla officia pariatur quam quasi quia quo
          recusandae reprehenderit saepe similique vel vero vitae voluptas
          voluptatem! Quibusdam.
        </p>
      </section>
      <section>
        <h3>A subtitle lives here</h3>
        <p>
          Lorem ipsum dolor sit amet, consectetur adipisicing elit. Accusamus
          deleniti dignissimos ducimus expedita iure maxime qui rerum veniam
          voluptatibus. Accusamus asperiores assumenda atque consectetur
          consequuntur culpa cum deserunt dicta distinctio error excepturi fuga
          ipsa iste magnam modi nobis, obcaecati, pariatur perspiciatis placeat
          quo quod reiciendis repudiandae saepe soluta tempora unde vel? Aliquam
          exercitationem iste maiores placeat reprehenderit voluptates
          voluptatum. Ad at commodi culpa cumque debitis delectus dolorum, eius
          error et explicabo harum in ipsum iste labore laborum libero magni
          maiores nam non nostrum nulla officia pariatur quam quasi quia quo
          recusandae reprehenderit saepe similique vel vero vitae voluptas
          voluptatem! Quibusdam.
        </p>
      </section>
    </div>
  </body>
</html>
```

可以看到代码并不复杂，下面我们就着重讲解一下这两个CSS属性。

# 3. scroll-snap-type

该CSS属性拥有下面这些值：

- none：当这个滚动容器的可视的 viewport 是滚动的，不做任何处理。
- **x**：滚动容器只捕捉其水平轴上的捕捉位置。
- **y**：滚动容器只捕捉其垂直轴上的捕捉位置。
- block：滚动容器仅捕捉到其块轴上的捕捉位置。
- inline：滚动容器仅捕捉到其内联轴上的捕捉位置。
- both：滚动容器会独立捕捉到其两个轴上的位置（可能会捕捉到每个轴上的不同元素）。
- **mandatory**：如果滚动容器被滚动，那么它超过临界值后会自动滚动到下个容器上。
- **proximity**：如果滚动容器被滚动，那么它超过临界值后不会自动滚动到下个容器上。

其中需要注意的就是上面粗体标注的几个属性，使用`mandatory`就是全屏滚动，**则当滚动过一定阈值后，会自动滚动到下一屏幕，如果没有滚动过某一阈值，则回弹。**

而`proximity`不一样的是：**滚动过一定阈值后，就可以正常进行滚动（而`mandatory`是直接进入下一屏），如果没有滚动过某一阈值，则回弹。**

理解这两个属性其实非常简单，将上面的代码改改自己体验下就明白了。

注：使用`mandatory`，如果**滚动容器的高度已经大于屏幕的高度时**需要慎用，因为可能会导致有一部分内容因为强制滚屏的原因导致阅读起来非常困难。

# 4. scroll-snap-align

该CSS属性拥有下面这些值：

- none：该容器不会进行定义在父容器上面对应轴的捕捉。
- start：该容器被捕捉的位置是该容器开始的部分。
- end：该容器被捕捉的位置是该容器结束的部分。
- center：该容器被捕捉的位置是该容器中间的部分。

用一张图可以很形象的明白这些属性所代表的容器位置：

![img](/images/css/仅用CSS实现fullpage.js全屏滚动效果/scroll-snap-align.jpg)

# 5. 最后

因为我看到`scroll-snap`的**其它属性大部分都存在很严重的兼容性问题**，所以就不在这里细讲了，如果有兴趣的话可以到[CSS Scroll Snap](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Scroll_Snap)直接查看，不过使用上面的这两个属性其实已经完全够用了。

参考文章：

- [scroll-snap-align MDN](https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-snap-align)
- [scroll-snap-type MDN](https://developer.mozilla.org/zh-CN/docs/Web/CSS/scroll-snap-type)
- [Practical CSS Scroll Snapping](https://css-tricks.com/practical-css-scroll-snapping/)

