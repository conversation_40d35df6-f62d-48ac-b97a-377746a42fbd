---
title: "项目或者文件管理神器-Git"
date: 2020-7-13 12:58:48
categories:
  - "实用工具"
tags:
  - "Git"
---


现在网上Git的教程以及介绍非常的多，我一直觉得一个东西首先要会用，然后有时间再去了解原理，所以本篇文章就讲讲怎么使用Git，完全不谈其原理~~其实是我自己也没有研究过~~。

# 1. 思维导图

![Git命令](/images/Git命令思维导图.png)

**注：标红的为常用命令。**

# 2. Git是什么

> git是一个分布式版本控制软件，最初由林纳斯·托瓦兹创作，于2005年以GPL发布。最初目的是为更好地管理Linux内核开发而设计。应注意的是，这与GNU Interactive Tools不同。 git最初的开发动力来自于BitKeeper和Monotone。
>

# 3. 安装Git

到[Git官网](https://git-scm.com/)直接下载并且安装。

打开Git的方式非常简单，在需要使用Git的文件夹中点击右键-`Git Bash Here`。

![打开Git](/images/打开Git.gif)

# 4. 配置Git

如果要使用Git首先需要进行配置用户名和邮箱，目的是清楚谁进行了提交。

## 4.1 全局配置

在个人电脑上首次使用`Git`需要进行配置，公共电脑慎用。

```bash
git config --global user.name "[name]" #你的用户名
git config --global user.email "[email]" #你的邮箱
```

## 4.2 当前仓库配置

对当前仓库的用户名和邮箱进行配置，公共电脑推荐使用。

```bash
git config user.name "[name]"
git config user.email "[email]"
```

# 5. Git使用步骤

说了那么多，其实Git常用的命令就那么几个，其它的等用到的时候再去翻翻。

## 5.1 初始化Git

```bash
git init
```

## 5.2 添加文件

### 5.2.1 添加单个文件

```bash
git add 文件名
```

仅添加单个文件到暂存区。

### 5.2.2 添加所有文件

```bash
git add .
```

**常用，添加所有文件到暂存区。**

## 5.3 提交到本地仓库

```bash
git commit -m '版本信息'
```

建议遵守`git commit`规范，当然如果仅仅自己使用的仓库就随意了。

[规范参考文章](https://juejin.im/post/5afc5242f265da0b7f44bee4)

## 5.4 推送

我们往往会将Git仓库推送到远程仓库，例如`GitHub`，目的是方便换电脑时也能同步自己的代码，如果是放在本地仓库，可能存在硬盘损坏或者其它不可预料的情况。

首次推送会弹出登陆框，然后需要登陆`GitHub`。

```bash
git push 远程库地址/远程库别名 分支名
```

强制推送命令（慎用）：

```bash
git push -u origin master -f
```

**该命令一般来讲千万不要用于多人维护的项目中，因为它会强制推送你当前的版本，可能会引起版本丢失等一系列的不可预料的损失。**

不过如果是你个人的项目，这条命令还是很好用的，主要是用于版本回退后远程仓库的版本比你现在的版本高，而导致无法进行推送到远程仓库时使用。

## 5.5 总结

最简单的`Git`使用其实就是下面的5个步骤。

```bash
git init #初始化Git仓库
git add . #添加所有文件到暂存区
git commit -m "first commit" #提交到本地仓库
git remote add origin 远程仓库地址  #设置远程仓库别名
git push origin master #推送到远程仓库
```

上面这是在仓库初始化的时候，如果是第二次或者以后的提交只需要进行下面的步骤就可以了：

```bash
git add . #添加所有文件到暂存区
git commit -m "first commit" #提交到本地仓库
git remote add origin 远程仓库地址  #设置远程仓库别名
git push origin master #推送到远程仓库
```

# 6. 更多命令

看完了上面的4个步骤，那么你就可以愉快的使用`Git`了，是不是非常的简单。当然，`Git`所包含的内容可远远不止这些，它还能实现更为复杂的功能。

## 6.1 克隆项目

如果你将项目托管到了`GitHub`或者其它代码托管平台上，或者你需要拉取别人项目，那么可以直接通过：

```bash
git clone 仓库地址
```

以`GitHub`为例：

![image-20200712204226893](/images/image-20200712204226893.png)

然后输入：

```bash
git clone https://github.com/vuejs/vue.git
```

## 6.2 拉取更新

如果远程仓库有更新，可以直接在项目中通过

```bash
git pull
```

进行更新，比如说你在一台电脑上更新了远程仓库，在另一台电脑上你就可以通过`git pull`将代码进行同步。当然，必须要是更新已经推送到远程仓库的情况下，如果仅仅是提交到了本地仓库，那是没有效果的。

## 6.3 版本回退

试想一下，如果你做了一个表格，发送给你的上司，你的上司觉得你做的表格不太满意，让你进行修改，你修改后又发给你的上司，你的上司看了后觉得还是刚才未修改的好，这个时候你是否很抓狂？

为了解决这个问题，你可以在每次修改的时候都进行一次备份，但如果不止一次呢，是很多次修改。每次都进行备份嘛？这样后面找起来也很麻烦。

这个时候`Git`的强大就得以体现。

我们先创建一个文件夹，在里面随便创建一个`Excel`，弄一些简单的数据：

![image-20200712201839402](/images/image-20200712201839402.png)

假设这就是第一个版本。

然后我们根据上面的`Git`使用步骤，

先初始化`Git`：`git init`。

再添加文件到暂存区：`git add .`。

最后提交到仓库：`git commit -m '第一版'`。

到这里，你就可以打开`Excel`继续进行修改了~

![image-20200712202349897](/images/image-20200712202349897.png)

上面这个进行了一次修改，这个时候如果你的上司觉得之前的那个版本好，怎么办。

这个时候我们再次进行一下上面的命令：

添加文件到暂存区：`git add .`。

提交到仓库：`git commit -m '第二版'`。

那么我们应该如何回退到第一版呢？

首先运行`git log`。

![image-20200712202838633](/images/image-20200712202838633.png)

可以看到，红框圈起来的就是`hash`值，比如要回退到第一版，那么就输入下面的命令：

```bash
git reset --hard hash
```

![image-20200712202935938](/images/image-20200712202935938.png)

**`hash`值不一定要全部复制，复制一部分就可以了。**

然后按一下回车，再打开`Excel`看一下，数据已经回到了第一版了。

那么这个时候如果觉得第二版更好，怎么回到刚才的第二版呢？

输入：

```bash
git reflog
```

![image-20200712203241440](/images/image-20200712203241440.png)

因为我的窗口是没有关闭的，所以之前的命令还在，我们这一步的主要目的是为了找到第二版的`hash`值，

**注意：该记录有时限，且仅仅在本地。**

然后再使用上面的命令：

```bash
git reset --hard hash
```

![image-20200712203507968](/images/image-20200712203507968.png)

这个时候再打开`Excel`看一下，又回到了第二版。

# 7. 最后

看到这里，`Git`基础的用法你已经全部学会了，至于分支、标签、多人协作等进阶的功能，由于篇幅限制，就不在这里讲解了。学会上面这些，对于一个非程序员来讲，已经完全够用了。
