---
title: "制作油猴Tampermonkey脚本需要哪些知识"
date: 2020-7-27 12:02:53
categories:
  - "JavaScript"
tags:
  - "油猴脚本"
  - "浏览器插件"
---


之前有篇文章讲述了如何使用油猴来编写浏览器脚本，以及用油猴编写脚本相对于Chrome插件有什么优势：[强大的油猴Tampermonkey：简单的脚本制作](/2020/05/28/强大的油猴tampermonkey简单的脚本制作/)。

因为之前的文章中只讲解了油猴脚本怎么编写，但是没有细说油猴脚本如何编写，以及编写油猴脚本需要哪些知识与技能点。

所以这篇文章就着重讲解一下油猴脚本涉及了哪些知识：

# 1. HTML，CSS

将`HTML`和`CSS`放在一起是因为在油猴脚本的编写中，这两项技术并不是太能派的上用场，除非你的脚本需要大量的交互，否则就不会编写很多`HTML`和`CSS`。

![img](/images/v2-134f4d56dc979fcc5d4dadb7775f4f38_b.webp)

就好比上图来说，你需要做出这种按钮、图片集成框时才需要使用到`HTML`和`CSS`。

如果有这方面的需求，那么就必须要学会`HTML`和`CSS`基础，至于`Vue`，如果有兴趣的话也可以学习，当你需要编写界面的时候`Vue`会提供很多便利。

# 2. JavaScript

重点！一个油猴脚本本质上就是一个`JavaScript`文件，如果连`JavaScript`不会使用，那根本无从下手。

因为上一篇文件是直接放出了脚本代码，但是没有讲解脚本代码的含义，所以本篇文章就着重讲解一下我之前的代码做了什么，下面贴上上次的代码：

```js
// ==UserScript==
// @name         B站图片爬取
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  try to take over the world!
// <AUTHOR>
// @match        https://www.bilibili.com/*
// @grant        none
// @resource     customCSS https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css
// @require      https://cdn.jsdelivr.net/npm/jquery@3.4.1/dist/jquery.slim.min.js
// @require      https://cdn.jsdelivr.net/npm/vue
// ==/UserScript==

(function () {
  "use strict";
  $("head").append($(`<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css">`));
  var divApp = $(
    `
<div style="position:fixed; z-index:1000;top: 80vh;left: 5vw" id="myPlan">
  <div v-if="showMassage" style="position: fixed;z-index: -1;width: 100vw;height: 100vh;top: 0;right:0;background-color:rgba(0,0,0,0.5);"></div>
  <button class="btn btn-primary" @click="showM">提取图片</button>
  <div v-if="showMassage"
       class="container"
       style="position:fixed;background-color:rgba(0,0,0,0.5);;display: flex;flex-wrap: wrap;overflow: auto;width: 600px;height: 500px;top: 50%;left: 50%;transform: translate(-50%, -50%);">
       <div  v-for="(image,index) in images"  :key="index">

       <a :href="image.src" target="frame1"><img :src="image.src" class="rounded float-left" style="min-width: 100px;margin: 10px;height: 80px" alt=""></a>
</div>
</li>
</div>
</div>
</div>`
  );
  $("#app").append(divApp);

  /*Vue操作*/
  var app = new Vue({
    el: "#myPlan",
    data: {
      showMassage: false,
      images: []
    }, methods: {
      showM() {
        this.showMassage = !this.showMassage;
        this.images = $("img");
      }
    }
  });

})();
```

## 2.1 (function(){})()

`(function(){})`在`JavaScript`的代码中经常出现，其实本质上是声明了一个匿名函数，然后对它进行调用。

```js
// 普通的函数声明
function a(){}

// 匿名函数
let a = function(){}

// 调用函数
a();

// (function(){})() 就相当于是声明了一个匿名函数，并且调用
// 等价于上面的先声明 function a(){} 再调用 a()
```

## 2.2 jQuery

曾经风光无限的`JavaScript`库，它简化了`HTML`与`JavaScript`之间的操作。

近些年来越来越少的开发者会用到它，因为现在几乎都是使用的`Vue`和`React`前端框架，这些前端框架提出了一个概念叫做**虚拟DOM**，通过虚拟`DOM`技术，我们在实际的开发中减少了大量的`DOM`操作，自然就不需要使用简化`DOM`操作的`jQuery`。

由于`jQuery`过去的风光，所以现在很多前端项目依然还在使用`jQuery`进行维护，同时由于编写油猴脚本需要大量操作`DOM`，所以如果需要**编写油猴脚本，还是要学一学`jQuery`。**

那么说了这么多，通过下面的例子我们来看看`jQuery`到底起了什么作用：

比如我们要通过`ID`获取到一个`DOM`元素，使用原生`JavaScript`进行获取就是`document.getElementById("app");`，而通过`jQuery`获取就可以简化为`$("#app")`。

通过上面的例子可以看到`jQuery`在操作`DOM`元素时大大的简化了代码量。

当然`jQuery`的作用不仅仅是这些，至于进一步的用法，可以参考一下网上`jQuery`的教程。

## 2.3 引入BootStrap

### 2.3.1 BootStrap是什么

> Bootstrap是一组用于网站和网络应用程序开发的开源前端框架，包括HTML、CSS及JavaScript的框架，提供字体排印、窗体、按钮、导航及其他各种组件及Javascript扩展，旨在使动态网页和Web应用的开发更加容易。 Bootstrap是GitHub上面被标记为“Starred”次数排名第四多的项目。

简单的说就是一个提供大量样式的玩意，大大简化你的前端开发工作量，直接帮你实现了很多常用样式和组件，直接拿去用就行了。

但是我觉得自己用的油猴脚本样式能看就行了...用`BootStrap`做样式其实是为了给别人看。

### 2.3.2 引入

因为我发现油猴脚本好像无法引入CSS文件，也可能是我没有找对方法，我试了几个方法后发现只有直接向`head`中进行添加`DOM`的方式成功了，即下面的代码：

```js
$("head").append($(`<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css">`));
```

通过这段代码，我们就相当于给网页的`<head>`标签中添加了：

```html
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css">
```

## 2.4 Vue

我编写的那个脚本中还使用到了`Vue`，至于`Vue`的作用是让我可以更方便的制作出交互页面，即图片集中在一起显示的界面就是使用`Vue`来做的。

这点其实使用`jQuery`也能够实现同样的效果，但是我对于`jQuery`的了解也仅仅是停留在能用的地步。

# 3. 最后

这里再给上一个旋转网页图片的油猴脚本，总所周知，在看贴吧或者各种网页的时候，有人发的图片可能是颠倒的，这个时候就需要旋转才方便观看，但是手机端可以将手机旋转， 电脑端总不能把电脑屏幕整个旋转吧。

所以就写了一个旋转图片的脚本，虽然使用过程可能会产生BUG，但是大体功能是实现了。

## 3.1 最后效果

![rotateimage](/images/rotateimage.gif)

代码如下：

```js
// ==UserScript==
// @name         旋转图片
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  try to take over the world!
// <AUTHOR>
// @include      *
// @grant        none
// @require      https://cdn.jsdelivr.net/npm/jquery@3.4.1/dist/jquery.slim.min.js
// ==/UserScript==

(function () {
  'use strict';
  $('head').append($(`
  <!-- CSS部分 -->
  <style>
  .ha-image {
    position: relative;
  }
  .ha-button {
      display: none;
      top: 0;
      left: 0;
      position: absolute;
      z-index: 1;
  }
  .ha-image:hover>.ha-button  {
    display: inline-block;
  }
  </style>`));
  /* 给图片增加一个父元素，方便操作 */
  let divApp = $(`
     <div class="ha-image"></div>
  `);
  /* 按钮部分 */
  let rotateButton = $(`
    <div class="ha-button">
      <button class="leftRotate90">旋转90度</button>
      <button class="rightRotate180">旋转180度</button>
      <button class="rightRotate270">旋转270度</button>
      <button class="reset">还原</button>
    </div>
  `);
  let images = $('img');
  images.wrap(divApp);
  $('.ha-image').append(rotateButton);
  $('.leftRotate90').click(function () {
    let image = $(this).parent().parent().children('img');
    image.css('transform', 'rotate(90deg)');
    return false; //阻止事件冒泡
  });
  $('.rightRotate180').click(function () {
    let image = $(this).parent().parent().children('img');
    image.css('transform', 'rotate(180deg)');
    return false;
  });
  $('.rightRotate270').click(function () {
    let image = $(this).parent().parent().children('img');
    image.css('transform', 'rotate(270deg)');
    return false;
  });
  $('.reset').click(function () {
    let image = $(this).parent().parent().children('img');
    image.css('transform', 'rotate(0)');
    return false;
  });
})();
```

