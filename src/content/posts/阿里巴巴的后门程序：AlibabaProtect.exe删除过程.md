---
title: "阿里巴巴的后门程序：AlibabaProtect.exe删除过程"
date: 2020-8-5 01:53:45
categories:
  - "其它"
tags:
  - "后门程序"
---


# 1. 前言

偶然发现了一个阿里巴巴的后门程序：`AlibabaProtect.exe`。

通过查询了一些资料，显示该应用不仅会占用大量的内存和`CPU`性能，还会占用一部分带宽，并且没有任何的文档说明该应用的具体功能，所以网上对它有很多猜测，说它在后台监视你的一举一动，记录你电脑的使用情况，进行精准的投放广告以及其它细思极恐的事情。

具体网上的分析就不谈了，反正就是流氓程序。

## 1.1 发现

![image-20200805010539250](/images/image-20200805010539250.png)

在任务管理器上面可以查看到这样的一个进程，可以看到占用了非常少的`CPU`和内存，本来这样一个程序放在那里不管也行，但是勾选它点击结束任务没有任何反应！这点就非常恐怖，到底该程序有着什么样的功能，居然无法结束进程。

定位到该程序目录：

![image-20200805012238080](/images/image-20200805012238080.png)

直接尝试删除该文件夹：

![image-20200805012401943](/images/image-20200805012401943.png)

提示**没有获得权限。**

尝试更改该文件夹的只读权限：

![image-20200805012604654](/images/image-20200805012604654.png)

**被拒绝访问。**

# 2. 火绒

因为它是一个服务，所以就想着火绒是否可以快速的结束它。

![image-20200805011009528](/images/image-20200805011009528.png)

找到了该项，设置为禁止启动，然后重启。

**最终结果：无效。**

到这里更加加深了我的恐惧，究竟是什么样的程序，怎么样的启动方式，居然通过常规手段无法禁止它启动。

# 3. 删除方法

方法很简单，简单的让我觉得有点不可思议，因为上面尝试删除的过程那么曲折：

1. 在键盘上按下`win`+`r`键（`win`键在`Alt`键左侧），输入`cmd`。

![image-20200805013816714](/images/image-20200805013816714.png)

2. 在打开的窗口中输入：`sc delete AlibabaProtect`，再点击回车，会得到成功提示（因为我已经删除过了，所以无法截图成功时的提示）。

![image-20200805013902968](/images/image-20200805013902968.png)

3. 重启电脑，进入`C:\Program Files (x86)`目录，删除`AlibabaProtect`整个文件夹，这下终于能顺利删除该文件夹了。
4. 最后打开任务管理器，可以看到`alibaba pc safe service`，这项进程已经消失。

# 4. 最后

据说彻底禁用该程序后会导致阿里系的某些软件出现无法启动的情况，比如：**千牛**。

如果有阿里系的软件需要运行，例如：**旺旺**，该进程可能无法通过文章中的方法进行禁止。

是否禁止还是要根据自身情况来定。
