---
title: "Express学习笔记系列-1.入门体验"
date: 2020-10-12 23:20:12
categories:
  - "Nodejs"
tags:
  - "Express"
---


> Express.js简称Express，是针对Node.js的web应用框架，在MIT许可证下作为自由及开放源代码软件发行。它设计用来建造web应用和API。它已经被称为针对Node.js的服务器框架的事实标准。

# 1. 安装

首先你需要创建一个Node项目，这里就不再重复了。

```bash
npm install express --save

# 如果使用的TypeScript，则还需要引入
npm install @types/express --save
```

# 2. 路由

通过Express创建一个服务器是非常简单的，下面几句代码就可以创建一个Node服务器。

```js
import * as express from "express"; // 导入express包

const app = express(); // 创建实例

/** 配置路由 */
app.get("/", (req, res) => {
  res.send("Hello express");
});

app.listen(3000); // 需要访问的端口号
```

在浏览器中访问`http://localhost:3000/`，即可以看到下面的界面：

![image-20201007160835796](/images/Node/image-20201007160835796.png)

到这里，一个Node服务器就已经开启了。

## 2.1 get请求

### 2.1.1 动态路由

下面可以开启一个动态路由，及使用`:xxx`的形式，可以在`req.params`中获取到那个值。

```js
/** 动态路由 */
app.get("/article/:id", (req, res) => {
  let id = req.params["id"];
  res.send("动态路由" + id);
});
```

这个时候我们访问`http://localhost:3000/article/add`就可以看到下面的界面：

![image-20201007162137393](/images/Node/image-20201007162137393.png)

**注意：设置一个动态路由需要注意顺序的问题。**

```js
/** 动态路由 */
app.get("/article/:id", (req, res) => {
  let id = req.params["id"];
  res.send("动态路由" + id);
});

/** 非动态路由 */
app.get("/article/add", (req, res) => {
  res.send("非动态路由add");
});
```

比如上面的这种情况我们再次访问`http://localhost:3000/article/add`

![image-20201007162253341](/images/Node/image-20201007162253341.png)

会发现依然是访问的动态路由，所以如果动态路由和非动态路由冲突的情况下，非动态路由要放在上面。

```js
/** 非动态路由 */
app.get("/article/add", (req, res) => {
  res.send("非动态路由add");
});

/** 动态路由 */
app.get("/article/:id", (req, res) => {
  let id = req.params["id"];
  res.send("动态路由" + id);
});
```

这个时候我们再一次访问：

![image-20201007162359232](/images/Node/image-20201007162359232.png)



### 2.1.2 get传值

get请求往往有时候会带上参数，那么在Express怎么获取这些参数呢？

```js
app.get("/article", (req, res) => {
  let query = req.query; // 获取参数
  res.send("id：" + query.id);
});
```

然后我们在浏览器上面进行请求`http://localhost:3000/article?id=13`。

![image-20201007163214096](/images/Node/image-20201007163214096.png)

其它请求：

**post请求：**

```js
app.post('url', (req, res) => {});
```

**put请求：**

```js
app.post('url', (req, res) => {});
```

**delete请求：**

```js
app.delete('url', (req, res) => {});
```

关于post请求如何获取传入的参数，这个在后面写到中间件时再进行详解。

# 3. 在Express中使用EJS

关于EJS的用法，可以参考：[高效的嵌入式JavaScript模板引擎-EJS入门](/2020/07/09/高效的嵌入式javascript模板引擎-ejs入门/)。

打脸的是，当时我写下那篇文章时对EJS其实是一种不屑的态度，因为我觉得Vue完全能够替代EJS那为什么还要使用EJS。

事实证明还是我太年轻了。其实EJS是一个非常轻型的嵌入式模板引擎。**主要是用在Node应用上，从npm上每周极高的下载量就可以看出EJS用的是非常多的。**

![image-20201012222429372](/images/Node/image-20201012222429372.png)

## 3.1 使用

首先需要安装EJS：

```bash
npm install ejs
```

由于Express默认继承了EJS，所以在`app.ts`中添加下面的代码：

```js
import * as express from "express";

const app = express();

app.set("view engine", "ejs");

/* 默认找到根目录下的views目录 */
app.get("/", (req, res) => {
  res.render("index", {});
});

app.listen(3000);
```

这个时候在views文件夹下面创建一个index.ejs文件。

![image-20201012223855481](/images/Node/image-20201012223855481.png)

**注意：在没有进行配置的情况下，后缀名只能使用`.ejs`。**

接下来我们看一下怎么讲值传入EJS模板中。

```js
/* 默认找到根目录下的views目录 */
app.get("/", (req, res) => {
  const title = "这是一段标题";
  res.render("index", {
    title, // 这里可以传入变量
  });
});
```

`index.ejs`文件

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Title</title>
</head>
<body>
<h2>Hello EJS</h2>
<p><%=title%></p>
</body>
</html>
```

输出结果：

![image-20201012224403416](/images/Node/image-20201012224403416.png)

可以看到，EJS可以获得ts文件中传入的值。

## 3.2 更改模板文件位置

上面的模板位置都保存在views文件夹里面，一般情况下都推荐保存在views文件夹里，不过也可以通过设置保存在其它模板里面：

```js
app.set("views", __dirname + "/views");
```

其中`__dirname`表示当前目录。

## 3.3 更改后缀名

模板文件默认情况下必须以`.ejs`结尾，如果以其它字符结尾引入会报错，但是可以通过设置让其它后缀名也可以作为模板，比如`.html`。

话不多说，我们来看一下怎么设置吧~

```js
import * as express from "express";
/* 1.引入ejs */
import * as ejs from "ejs";

const app = express();

/* 2.注册html模板引擎 */
app.engine("html", ejs.__express);
/* 3.将模板引擎换成html */
app.set("view engine", "html");
/* 4.修改模板文件后缀名为html */

/* 默认找到根目录下的views目录 */
app.get("/", (req, res) => {
  const title = "这是一段标题";
  res.render("index", {
    title, // 这里可以传入变量
  });
});

app.listen(3000);
```

通过上面的步骤，你可以随意定义后缀名哦，不过为了代码的易读性，还是推荐使用默认的`.ejs`。如果是自己的项目那就随意了~

# 4. 静态托管

在服务器上面往往会存放静态文件，比如图片、视频、音乐等等，这些文件应该放在什么地方才能被外部访问呢？

在Express中，专门有方法向外暴露静态资源文件夹。

```js
app.use(express.static("public"));
```

即将`public`文件夹中的内容向外暴露。

![image-20201012230456938](/images/Node/image-20201012230456938.png)

直接在浏览器中访问就可以访问到`public`文件夹下的资源。

```js
app.use(express.static("public"));
```

你也可以为静态资源目录制定一个挂载路径：

```js
app.use("/static", express.static("public"));
```

这个时候要再想访问静态资源则需要加上`/static`。

![image-20201012230734641](/images/Node/image-20201012230734641.png)

# 5. 最后

呃...每篇文章惯例在最后说点啥，其实在前后端分离的情况下，EJS也不常用，因为后端大部分情况下都是提供大量的接口供前端请求，也就没有EJS什么事情了。

学会上面的这些内容，已经可以简单的搭建自己的后端进行访问了。
