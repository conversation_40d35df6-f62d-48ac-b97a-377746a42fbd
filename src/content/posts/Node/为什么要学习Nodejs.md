---
title: "为什么要学习Nodejs"
date: 2020-9-22 23:11:03
categories:
  - "Nodejs"
tags:
  - "Nodejs"
---


# 1. 前言

**Node.js 是能够在服务器端运行JavaScript 的开放源代码、跨平台 JavaScript 运行环境。**

随着前端的深度和广度日益提高，如果光会用一个Vue，可能自己就限制了自己以后的发展，因为现在哪个前端不会用一个框架。

前端的入门并不难，但是大公司或者高薪一点的前端开发，往往不能只会用一个框架，大部分还需要知道一些原理上的东西，而目前的前端框架包括很多第三方库，都大量的使用到了Nodejs。

所以Nodejs是前端进阶必不可少的一环，很多人可能会认为Nodejs和前端没有什么关系，那是后端的东西，因为Nodejs可以创建后端服务器。

其实并不是这样的，现在很多第三方库，第三方框架，比如Vue，React，都是建立在Nodejs的基础上发展起来的。

大家在使用框架写前端的时候应该都知道每次运行项目，热更新，以及最后的打包项目，都会有一个编译过程，其实这个编译过程就是通过Nodejs完成的。

不光是这个，还有服务器渲染（SSR），也需要通过Nodejs来实现，至于什么是服务器渲染，以及Vue怎么进行服务器渲染，可以看这篇：[Vue服务器渲染优秀项目：Nuxt.js](/2020/06/22/web开发/vue服务器渲染优秀项目nuxtjs/)。

还有现在JavaScript能够写桌面应用，比如代表级产品VScode，前端开发中占比最大的编辑器，也是在Nodejs的基础上开发出来的。

总之，现在前端之所以能发展成一个庞然大物，Nodejs绝对是最大的功臣，所以你还觉得Nodejs对于一个前端来说不重要吗？

## 1.1 特点

Node最主要有以下3个特点：

- 事件驱动
- 非阻塞IO模型（异步）
- 轻量和高效

# 2. 作为中间层

Node一般在一线企业中作为中间层，我们通常说前端和后端，前端负责用户界面，而后端负责提供数据和业务接口。

现在我们在两者间加入一层，前端并不是直接去请求后端业务接口，而是请求到中间层。再由中间层去请求业务接口。

![前端有必要去学Node.js 吗？_项目](/images/Node/84fa3b4d25254cf5b1cd1564d8a62293.jpeg)

**中间层的优点** ：

1. 减轻客户端内存，项目用户体验好。不会像mvvm模式的项目把页面渲染和数据请求都压在客户端，而是在服务端完成。
2. SEO性好，不像mvvm模式页面由js生成，而是在服务器渲染好html 字符，有利于网页被搜索到。
3. 保持了前后端分离的优点和目的，即解放后端，后端可以继续以接口的形式写业务代码。
4. 前端可以操控的范围增多，甚至可以做服务器，数据库层面的优化，比如中间层中常常用nginx，redis来优化项目，应对高并发。

中间层模式是一种开发模式上的进步，但是这种模式成本过高，成本高主要是高在服务器成本上。

一般的前端项目都是通过客户端解析JavaScript，然后渲染界面，而这种形式全靠服务器解析JavaScript文件，然后将解析后的HTML返回给客户端，这大大的提高了服务器的负载，如果你的网站访问量越大，你的服务器成本越高。

所以如果没有一定量级的项目没必要去采用，关键看你预算是否充足。

# 3. 前端项目构建工具

webpack，vue-cli都是前端项目构建工具，我们通过Vue或者React框架进行前端开发时，会由构建工具自动进行编译打包成浏览器能够认识和识别的项目工程文件。

如果没有Node的出现，那现在前后端分离也将变得不可行。

# 4. 中小型网站后端

Node在企业中，很少会用来作为后端，除非是**有一定量级，前后端分离，想要更好的用户体验和更好的SEO**，才会使用Node中间层。

大多数时候Node都是用来做个人项目的后端，真正的后端还是会使用主流的后端语言进行开发。

# 5. 个人观点

如果想要做后端开发推荐还是学习Java，因为Java经过了几十年的发展，生态已经非常健全，很多东西别人已经帮你写好，你只需要引进项目就行了，而使用Node开发后端很多东西都要自己写，并且很多公司的老项目都是Java和PHP。

对于后端开发者来讲，Node并不是那么重要，但是对于前端开发者来讲，Node必然是进阶路上的一环。
