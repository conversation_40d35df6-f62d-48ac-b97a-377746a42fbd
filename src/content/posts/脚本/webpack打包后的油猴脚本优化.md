---
title: "webpack打包后的油猴脚本优化"
date: 2022-3-4 21:17:41
categories:
  - "脚本"
tags:
  - "油猴脚本"
  - "hide"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

这篇文章是紧紧跟着上篇文章：

我的脚本确实编写完毕，但是我发现了一个问题，通过webpack打包后，脚本大小达到了2MB+，这是一个非常大的体积了，而且每次更改脚本后，webpack的编译时间都达到了10多秒，这个时候我就觉得不行呀，不仅仅是因为脚本体积的问题，还有编译时间的问题。

这个时候我又灵光一闪，在编写Vue或者React的时候，我们不是可以让webpack排除某些模块，通过CDN的方式进行引入嘛？

说行动就行动，那么目前来说，我这个脚本中引入的库就有react、react-dom、antd、jQuery，这几个库，于是我就在webpack中添加了下面的信息。

找CDN地址花了一些，最后找到了react中文网站上面的CDN地址：

# antd样式

在我多次修改webpack配置进行打包的时候，我发现体积最大的是antd的css文件，如果打包了该css文件，那么打包后的体积直接从几百kb飙升到2Mb。

所以外部引入antd样式必不可少，但是我通过油猴提供的引入外部资源的方式，引入antd的css样式，怎么都不起作用，我百思不得其解，但是换了一种通过代码动态引入样式的方法，一试，发现有效果，于是我就通过。

# 

使用CDN后，webpack打包的速度也到了1s左右，同时打包出来的脚本体积也在几kb到几十kb。

# 最后

经过这些探索，我已经做好了编写更多提高工作效率的油猴脚本了，相信读到这里，你可能脑子中也冒出了一大堆可以提高效率的主意，那就不要再犹豫，直接动手开始实现吧。

