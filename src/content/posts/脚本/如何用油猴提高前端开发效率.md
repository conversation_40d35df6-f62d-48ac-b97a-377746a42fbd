---
title: "如何用油猴提高前端开发效率"
date: 2022-3-15 16:06:48
categories:
  - "脚本"
tags:
  - "油猴脚本"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

# 1. 起因

时隔一年，油猴脚本系列又来了，起因是那天洗澡的时候，突然灵光一闪，想到平时在前端开发中，有很多工作完全是一些机械的，没有什么难度的活，但是又异常的花时间！其实就是对一些表格字段。

目前为止我还在开发之前的那个百万级ERP项目，项目中的列表非常的多，目前大概有几百个吧，而且每个列表都有非常多的字段，通常情况下都在20个字段左右，而后端写的时候会在对应的接口中返回一大堆字段，你需要从返回的字段中挑选出列表中需要的字段。

可能一个两个还好，但是几十个几百个列表一个字段一个字段的对起来就很耗时耗力，所以这个时候我就有一个想法：有没有那么一种方法，将原型图上列表字段提取出来，然后与swagger中该接口下面的字段注释做一个对比，如果对的上，则生成Antd可以使用的表头代码，如果没有对上，那么就统一将这些没有对上的字段展示出来，然后用肉眼去对，如果依然没有找到这些字段，那么就发给后端，让后端添加上这些字段。

一想到这个可能性，马上就想迫不及待的想要进行实现，这时我回忆起一年前用过油猴脚本做了一些事情，那么这次这个突发奇想是否也可以用油猴脚本进行实现。

# 2. 油猴脚本

我在去年已经写过几篇关于油猴脚本的文章，其中提到过通过Webpack打包，可以开发特别复杂的脚本，甚至可以开发一些工程级的脚本，同时也可以使用npm上的包。

但是由于webpack配置的复杂性，所以我首先是没有考虑使用webpack打包工具的，而是先想到了另一个零配置打包工具：parcel。

但是很遗憾，失败了，不知道为什么通过parcel打包后的js文件直接通过油猴引入外部资源的方法会报错，于是这个时候我还想到了一个打包js文件的工具：Rollup。

遗憾的是，同样失败了，不知道为什么引入油猴后就是会报错，于是这个时候我就放弃抵抗，选择了webpack进行打包，结果不出意料，webpack打包后的js文件引入油猴中没有任何问题。

因为我之前已经这么做过了，还总结了相关的文章：[强大的油猴Tampermonkey脚本开发环境搭建](/2021/02/22/脚本/强大的油猴tampermonkey脚本开发环境搭建/)，所以关于油猴的基础部分就不再赘述。

## 2.1 打包的好处

为什么要通过webpack打包？

因为在编写一些复杂脚本时，我们往往会需要使用到非常多的库，而且也会将脚本进行模块化，分成非常多的文件，同时如果你还想编写一些CSS样式，那么你也可以直接写CSS文件或者Less或者Sass文件，通过webpack可以将这些文件统一起来，打包成一个js，然后通过油猴提供的外部引入方式进行引入。

随着我对React越发的得心应手，要实现上面说的自动比对字段的功能React也是不可少的一环，而我个人非常喜欢使用TypeScript，因为它会给予代码更多的提示，让你在写代码的时候不容易犯一些低级错误。

而使用React的时候就不得不提到Antd，Antd这个UI组件库提供了非常多的方便的组件，不光是解决了一些样式问题，同时它还解决了很多交互层面的东西，比如它的Form表单，也是非常好用的。

综上所述，如果你要开发一个油猴脚本，那么通过webpack进行打包就是一个非常好的选择。

# 3. jQuery

正如我年前的那篇文章所说，jQuery是一个非常值得学习的库，因为对于编写油猴脚本来说，没有什么比用jQuery去提取界面上的信息更方便的了。

由于jQuery的易用性，使用jQuery你可以轻易的从界面上提取到你要的信息，虽然使用正则可以达到差不多的效果，但jQuery代码写起来比正则更快，也更不容易出错。

# 4. 正则

编写脚本，非常多的情况会使用到正则，因为要精确的匹配到对应的字符，正则是你的不二之选，在做一些自动生成代码工具的时候，因为涉及到字符匹配的问题，学会正则就显得非常的重要。

这里就要推荐一个非常好的正则测试网站：[RegExr: 学习、构建和测试正则表达式Test RegEx](https://regexr-cn.com/)，因为有时候你无法判断你写的正则对不对，所以这个时候你就可以先在这个网站上面测试一下，如果测试结果符合你的预期，再将正则表达式复制到你的项目中进行测试，这样编写起正则来就方便的多，使用方法也非常简单，去该网站上面点两下大致就能明白如何使用。

# 5. 爬虫基础

如果你要提取一些界面上的信息，那么你得会一些爬虫基础知识，知道怎么获取到界面上的某些信息。

理论上一个网页上面所有的信息，都能通过正则表达式进行提取，但由于正则表达式在编写的过程中可能会比较容易出错，这个时候jQuery就能有效的帮你提取出这些页面数据。

这里就对爬虫入门时90%的人都会选择的[豆瓣排行榜](https://movie.douban.com/top250)做一个页面信息提取。

# 6. 实例

上面所想的那个脚本我其实已经实现了，但碍于swagger上面有公司项目的接口信息，所以这里就不拿那个脚本作为演示。

那么这里我就拿jQuery爬取到的豆瓣排行榜信息作为演示吧：

主要实现的功能就是，在输入框中输入电影名称，然后将电影相关的信息进行匹配并且展示出来：

## 6.1 触发事件构建

要执行你已经写好的代码，你通常可能需要**一个按钮、一个输入框**或者其它元素进行触发，在本脚本的开发中，我就使用了**Antd的按钮+Antd的模态框+Antd的输入框**这3种组件来搭建脚本。

## 6.2 最终效果

最终的效果像下面这样，代码其实并不难。

![filed](/images/script/如何用油猴提高前端开发效率/filed.gif)

这里贴上最终的代码：

```tsx
import { Button, ConfigProvider, Form, Modal } from "antd";
import "antd/dist/antd.css";
import TextArea from "antd/es/input/TextArea";
import zhCN from "antd/lib/locale/zh_CN";
import * as $ from "jquery";
import { useState } from "react";
import { render } from "react-dom";

interface InfoDataList {
  /** 电影的名称 */
  title: string;
  /** 分数 */
  score: string;
  /** 评价数 */
  number: string;
  /** 简介 */
  info: string;
}

function AppButton() {
  const [infoDataList, setInfoDataList] = useState<InfoDataList[]>([]);

  const [showData, setShowData] = useState<InfoDataList>();

  const [visible, setVisible] = useState(false);

  return (
    <>
      <Button
        onClick={() => {
          /** 整理后的信息 */
          const info: InfoDataList[] = [];

          // 这里是提取界面信息
          $(".info").each(function (this) {
            const infoItem: InfoDataList = {} as InfoDataList;
            // 这里是所有的信息，下面组件提取各种信息
            $(this)
              .find(".title")
              .each(function (this, index) {
                // 只取第一个电影名字
                if (index === 0) infoItem.title = this.innerHTML;
              });

            // 取分数
            $(this)
              .find(".rating_num")
              .each(function (this) {
                infoItem.score = this.innerHTML;
              });

            // 评价数
            $(this)
              .find(".star > span:nth-child(4)")
              .each(function (this) {
                infoItem.number = this.innerHTML;
              });

            // 取信息
            $(this)
              .find(".inq")
              .each(function (this) {
                infoItem.info = this.innerHTML;
              });

            info.push(infoItem);
          });

          setInfoDataList(info);
          // 打开弹窗
          setVisible(true);
        }}
        style={{ position: "fixed", right: "100px", bottom: "100px" }}
      >
        点我
      </Button>
      <Modal
        title="自动对字段"
        visible={visible}
        centered
        destroyOnClose
        width={1000}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Form
          preserve={false}
          onValuesChange={async (value) => {
            // 如果没有值，则不匹配
            if (!value.text) {
              setShowData(undefined);
              return;
            }
            const rgx = RegExp(value.text);

            const data = infoDataList.find((item) => rgx.test(item.title));
            setShowData(data);
          }}
        >
          <Form.Item name="text">
            <TextArea rows={10} />
          </Form.Item>
        </Form>
        {/* 这里是将对的字段展示出来 */}
        <ul>
          <li>电影名：{showData?.title}</li>
          <li>分数：{showData?.score}</li>
          <li>评价数：{showData?.number}</li>
          <li>简介：{showData?.info}</li>
        </ul>
      </Modal>
    </>
  );
}

// 添加一个div作为React的入口文件
$("body").append(`<div id="ccll-app"/>`);

render(
  <ConfigProvider locale={zhCN}>
    <AppButton />
  </ConfigProvider>,
  document.getElementById("ccll-app")
);
```

# 7. webpack配置

下面是我自己搭建并且使用的一套`webpack.config.js`，添加了对`CSS`、`TypeScript`、`Less`的支持，并且还对`JavaScript`做了兼容性处理，可以直接将下面的代码复制过去尝试。

```js
const { resolve } = require("path");

module.exports = {
  entry: ["./index.js"],
  output: {
    path: resolve(__dirname, "build"),
    filename: "index.js",
    publicPath: "/",
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|mjs)$/,
        exclude: /node_modules/,
        use: [
          {
            loader: "babel-loader",
            options: {
              // 预设：指示babel做怎么样的兼容性处理。
              presets: [
                [
                  "@babel/preset-env",
                  {
                    corejs: {
                      version: 3,
                    }, // 按需加载
                    useBuiltIns: "usage",
                  },
                ],
                "@babel/preset-react",
              ],
            },
          },
        ],
      },
      {
        test: /\.tsx?$/,
        use: "ts-loader",
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        // 使用哪些 loader 进行处理
        use: [
          // use 数组中 loader 执行顺序：从右到左，从下到上 依次执行
          // 创建 style 标签，将 js 中的样式资源插入进行，添加到 head 中生效
          "style-loader",
          // 将 css 文件变成 commonjs 模块加载 js 中，里面内容是样式字符串
          "css-loader",
        ],
      },
      {
        test: /\.less$/,
        // 使用哪些 loader 进行处理
        use: [
          // use 数组中 loader 执行顺序：从右到左，从下到上 依次执行
          // 创建 style 标签，将 js 中的样式资源插入进行，添加到 head 中生效
          "style-loader",
          // 将 css 文件变成 commonjs 模块加载 js 中，里面内容是样式字符串
          "css-loader",
          {
            loader: "less-loader",
            options: {
              lessOptions: {
                javascriptEnabled: true,
              },
            },
          },
        ],
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js"],
  },
  mode: "production",
  devtool: "source-map",
};
```

# 8. 最后

本篇文章没有拿我的那个最终实现的脚本进行演示，而是模拟了另一个例子，主要是起到一个抛砖引玉的作用，我想表达的意思就是**油猴被我们前端开发者所忽略了，其实它是一个非常好的能够提升开发效率的工具**。

从文章中可以看到，编写一个脚本需要掌握的知识还是比较多的，尤其是jQuery和正则，当然如果你对原生js熟悉的话，你完全可以使用原生js代码来代替jQuery的那些代码。

就我个人编写油猴脚本的经验来讲，jQuery几乎在每个脚本中都会用到，虽然用到的东西不是特别深，主要是提取界面信息，而正则用到的也非常多。

其实这里还留下了一个问题，就是引入了`React`+`Antd`后，打包的时间往往会达到10s左右，而打包后的代码通常会达到2M左右，下一篇文章就讲如何在油猴中使用CDN引入，让打包时间减少到1s左右，代码体积减少到几十kb左右。

通过这次的灵光乍现，我想到平时在写业务代码时，一些重复的机械的工作都可以交给脚本去实现，提高自己的开发效率，做一个准点下班的程序员。
