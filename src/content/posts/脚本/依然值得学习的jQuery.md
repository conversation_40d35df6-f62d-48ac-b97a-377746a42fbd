---
title: "依然值得学习的jQuery"
date: 2022-1-27 11:23:42
categories:
  - "脚本"
tags:
  - "JQuery"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

jQuery是一套跨浏览器的JavaScript库，用于简化HTML与JavaScript之间的操作。在现代3大前端框架火起来之前，jQuery是曾经最受欢迎的JavaScript库。

对于现代前端开发来说，已经不用再学jQuery了，除非你公司的项目是上古遗留下来的项目，还是用的jQuery进行搭建。

对于刚接触前端的同学来说，推荐HTML+JavaScript+CSS基础学完后就去学习Vue，不推荐将太多的时间花在jQuery上面，因为现在jQuery在公司的应用已经非常少了。

但是如果你想给一些别人的网页上面添加一些功能，那么你可能得学习一下jQuery，因为使用它来操作DOM节点比JavaScript原生直接操作DOM节点要方便太多。

# 1. 应用场景

那么jQuery在现在的应用场景是什么呢？

- 前端项目需要运行在IE8及以下。
- 浏览器脚本。

但值得注意的是，在jQuery2.x版本，就已经移除了对IE 6-8的支持，所以现在很多网站，都依然使用的jQuery1.x版本。

前端项目需要运行在IE8及以下这个需求好像很多公司依然还有，不过一般是一些政府网站的产品，因为现在React和Vue甚至Angular都已经放弃了IE11以下的版本，再想要兼容IE也只能选jQuery这种库，不过也有其它框架可以选择。

使用jQuery来编写浏览器脚本是非常方便的，它可以很轻易的获取到DOM节点，并且可以很轻易的创建新的DOM节点，同时还能很方便的绑定事件。

而且jQuery即引即用，不像现代三大前端框架那样可能还需要通过各种构建工具进行打包。

# 2. 用法

首先jQuery是以美元符`$`进行定义，然后使用选择符查找对应的HTML元素，最后通过调用jQuery的方法对元素执行操作，比如下面的代码：

```javascript
// 隐藏当前元素
$(this).hide()
// 隐藏所有 <p> 元素
$("p").hide()
// 隐藏所有 class="test" 的 <p> 元素
$("p.test").hide()
// 隐藏 id="test" 的元素
$("#test").hide()
```

仔细看上面的代码，当你明白上面的代码，那么恭喜你jQuery已经被你掌握了！

大概就是这么简单，当你知道jQuery的基础用法后，只需要去读一遍文档，了解jQuery提供的方法，那么你即刻就可以开始上手jQuery。

# 3. 过渡

## 3.1 显示隐藏

HTML元素的显示、隐藏是一个非常常见的需求，jQuery不光提供了相关的方法，还提供了过渡效果，让显示隐藏不会显得那么突兀。

- hide：隐藏HTML元素。
- show：显示HTML元素。
- toggle：hide和show的结合，当元素显示时调用则会被隐藏，当被隐藏时调用则会显示。

它们的使用方法一致，`$(selector).toggle(speed,callback);`，括号内有两个可选参数：

- speed：规定隐藏/显示的速度，可以取以下值："slow"、"fast"或毫秒。
- callback：隐藏或显示完成后所执行的函数。

```javascript
$("#button").click(function () {
  $("#box").toggle(500, function () {
    console.log("调用结束");
  });
});
```

![toggle](/images/script/依然值得学习的jQuery/toggle.gif)

当然还有淡入淡出、滑动等等过渡效果，用法都和上面差不多，这里就不再赘述了。

## 3.2 动画

和上面的显示隐藏的方法差不多，只不过它有3个参数`$(selector).animate({params},speed,callback);`

- params（必要）：定义形成动画的 CSS 属性。
- speed（可选）：规定效果的时长。它可以取以下值："slow"、"fast" 或毫秒。
- callback（可选）：动画完成后所执行的函数。

基本用法如下：

```javascript
$("#button").click(function () {
  $(".box").animate({
    width: 200,
    height: 200,
    // CSS属性要写成驼峰的形式
    marginTop: 100,
    marginLeft: 100,
  });
});
```

## 3.3 动画队列

jQuery针对动画提供了队列功能，即你对一个元素编写多个`animate()`调用，jQuery会对这些animate逐一进行调用，比如下面的代码：

```javascript
$("#button").click(function () {
  const box = $("#box")
  box.animate({height:'300px',opacity:'0.4'},"slow");
  box.animate({width:'300px',opacity:'0.8'},"slow");
  box.animate({height:'100px',opacity:'0.4'},"slow");
  box.animate({width:'100px',opacity:'0.8'},"slow");
});
```

运行效果如下：

![animate](/images/script/依然值得学习的jQuery/animate.gif)

## 3.4 停止动画

`stop()`方法适用于所有jQuery效果函数，包括滑动、淡入淡出和自定义动画在它们运行完成之前都可以使用`stop()`进行停止过渡和动画。

`$(selector).stop(stopAll,goToEnd);`

- stopAll：规定是否应该清除动画队列。默认是false，即仅停止活动的动画，允许任何排入队列的动画向后执行。
- goToEnd：参数规定是否立即完成当前动画。默认是 false。

## 3.5 链(Chaining)

Chaining允许我们在一条语句中运行多个jQuery 方法（在相同的元素上），就不用让浏览器多次查找相同的元素，最重要的是可以让代码显得更加简洁。

比如下面的代码：

```javascript
$("#button").click(function () {
  $("#box").toggle(500).toggle(500);
});
```

# 4. 编写脚本

因为油猴的存在，所以使用JavaScript来编写浏览器脚本可以在别人的网页上面自定义非常多的功能。

用jQuery编写脚本才是我推荐学习jQuery的最主要原因，因为现在很少有前端项目需要使用jQuery进行搭建，因为使用jQuery除了频繁操作DOM对于项目搭建不方便之外，而且现代前端框架的生态也非常的丰富，并且他们还有完善的SSR方案。

用jQuery来编写脚本还有一个优点：很多网站上面都引用过jQuery了，所以可以直接用，就算没有引用过，用油猴引用jQuery也不麻烦。

## 4.1 获取内容和属性

该应用场景就是爬虫，使用爬虫获取到相关的HTML页面数据后，可以用jQuery很方便的提取出你想要的那一部分数据。

但是JavaScript爬虫一定要运行在Node环境下，因为浏览器环境涉及到跨域的问题，而Node环境是没有跨域的。

做爬虫的时候推荐使用cheerio来替代jQuery，它几乎实现了jQuery的所有API，所以你学会了jQuery，可以无压力的使用cheerio。

jQuery有下面几种方法获取内容和属性：

- `text()` ：设置或返回所选元素的文本内容。
- `html()` ：设置或返回所选元素的内容（包括 HTML 标记）。
- `val()` ：设置或返回表单字段的值。
- `attr()`：用于获取属性值。

```html
<div id="text">
  标题：<h1>这是测试标题</h1>
</div>
<input type="text" id="input" value="百度" />
<a href="https://www.baidu.com" id="href">百度</a>

<script>
  const text = $("#text");
  console.log("text()：" + text.text());
  console.log("html()：" + text.html());
  console.log("val()：" + $("#input").val());
  console.log("href()：" + $("#href").attr("href"));
</script>
```

最后控制台的打印如下：

![image-20220127102507741](/images/script/依然值得学习的jQuery/image-20220127102507741.png)

## 4.2 设置内容和属性

设置内容的几个方法和获取内容一样，只是在调用方法的时候传入参数，就可以将元素中的内容进行更改，比如：

```html
<h1 id="title">这是测试标题</h1>
<button id="button">改变</button>

<script>
  $("#button").click(function () {
    // 第一种方式为传入字符串
    $("#title").text("Hello World!");

    // 第二种方式为传入一个回调函数
    $("#title").text(
      /**
       * 这是传入的回调函数
       * @param i 被选元素列表中当前元素的下标
       * @param origText 原始（旧的）值
       * @returns {string} 返回值为希望使用的字符串。
       */
      function (i, origText) {
        console.log(i, origText);
        return "Hello World!";
      }
    );
  });
</script>
```

最终效果如下：

![text](/images/script/依然值得学习的jQuery/text.gif)

其它几个属性就不一一进行举例了，

## 4.3 添加元素

添加元素一般有下面几个方法：

- `append()`：在被选元素的结尾插入内容。
- `prepend()`：在被选元素的开头插入内容。
- `after()`：在被选元素之后插入内容。
- `before()`：在被选元素之前插入内容。

```javascript
function appendText() {
  // 使用 HTML 标签创建文本
  const txt1 = "<p>文本-1。</p>";
  // 使用 jQuery 创建文本
  const txt2 = $("<p></p>").text("文本-2。");
  const txt3 = document.createElement("p");
  // 使用 DOM 创建文本 text with DOM
  txt3.innerHTML = "文本-3。";
  // 追加新元素
  $("body").append(txt1, txt2, txt3);
}
```

这个也非常简单，自行尝试一下就会了。

## 4.4 删除元素

- `remove()`：删除被选元素（及其子元素）。
- `empty()`：从被选元素中删除子元素。

调用方法也非常简单：

`$("#div1").remove();`

`$("#div1").empty();`

他们之间的区别就是是否删除被选元素。

## 4.5 操作CSS

- `addClass()`：添加class属性。
- `removeClass()`：在`addClass()`方法中规定多个类。
- `toggleClass()`：在不同的元素中删除指定的class属性。

可以参考下面的例子：

```html
<div class="box"></div>
<button id="button">切换</button>

<script>
  $("#button").click(function () {
    $(".box").toggleClass("box1");
  });
</script>
```

效果如下：

![toggleClass](/images/script/依然值得学习的jQuery/toggleClass.gif)

# 5. 最后

jQuery上手非常简单，同时它提供了非常多的方法，其实我个人觉得基础方法大致看一下，因为现在真的不太经常会用到jQuery了，就算你学过，用的时候还是会忘记具体怎么写，所以需要的时候还得去翻jQuery文档。
