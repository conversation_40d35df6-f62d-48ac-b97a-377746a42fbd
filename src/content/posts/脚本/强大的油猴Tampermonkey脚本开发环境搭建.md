---
title: "强大的油猴Tampermonkey脚本开发环境搭建"
date: 2021-2-22 00:18:14
categories:
  - "脚本"
tags:
  - "油猴脚本"
---


这是关于油猴脚本的第三篇文章，最近都在研究一些关于使用JavaScript来编写一些脚本从而完成一些简单但是繁琐的工作。

如果是在电脑浏览器上 ，要运行一个脚本那么油猴一定是不二选择，只要你会一些JavaScript相关的知识，你就可以开始编写属于你自己的脚本。

前景回顾：

- [强大的油猴Tampermonkey：简单的脚本制作](/2020/05/28/强大的油猴tampermonkey简单的脚本制作/)。
- [制作油猴Tampermonkey脚本需要哪些知识](/2020/07/27/制作油猴tampermonkey脚本需要哪些知识/)。

**本篇文章适合对于webpack和React有一定了解的开发者，不适合新手开发者阅读，因为本文章主要讲解的是如何搭建开发环境。**

---

最近在学习webpack打包技术的时候，突然想到，是否能够使用`babel-loader`将更高级的语法转换为ES5语法，然后运行在油猴上面，我试了一下，发现是可行的。

虽然油猴自带ES6模板，但是它自带的ES6模板居然和它本身的语法检测冲突了？

![image-20210219151714377](/images/script/强大的油猴Tampermonkey脚本开发环境搭建/image-20210219151714377.png)

咱也不知道是什么情况，于是就使用webpack自己搭建一个开发环境吧。

**为什么要使用webpack打包？**

- 因为ES6新增了非常多的新特性，弥补了JavaScript诞生以来的一些缺陷，比如this指针、回调地狱、没有class关键字等等。

- 它可以让ES7的async，await变得可用，甚至连最新的ES11语法都可以使用，也就是说你可以使用任何JavaScript的新特性。
- 它支持模块化编写，如果一个庞大的脚本几千行，将所有内容都写在一个文件中显然后期的维护也会变得非常麻烦，使用webpack打包编写就可以将这些功能分布到不同的文件中，从而使脚本维护变得简单。
- 可以随心所欲的引入其它第三方库，因为都是运行在浏览器上面，所以也不会像小程序那样有很多限制和兼容性问题。

# 1. 开始

使用webpack将代码转换成ES5语法其实非常简单，只需要先安装一下包：

```shell
npm install webpack webpack-cli babel-loader @babel/core @babel/preset-env --save-dev
```

在`webpack.config.js`中进行配置处理js的loader。

```js
{
  test: /\.js$/,
  exclude: /node_modules/,
  use: [
    {
      loader: "babel-loader",
      options: {
        // 预设：指示babel做怎么样的兼容性处理。
        presets: [
          [
            "@babel/preset-env",
            {
              corejs: {
                version: 3,
              },
              // 按需加载
              useBuiltIns: "usage",
            },
          ],
        ],
      },
    },
  ],
},
```

配置完毕后再到`package.json`文件中添加浏览器版本适配信息：

```json
"browserslist": {
  "production": [
    ">0.2%",
    "not dead",
    "not op_mini all"
  ],
  "development": [
    "last 1 chrome version",
    "last 1 firefox version",
    "last 1 safari version"
  ]
},
```

到这里，你可以随意的在你的JavaScript文件中使用最新的js语法，babel会自动将它进行转换。

# 2. 使用React语法

当一个JavaScript脚本需要涉及到创建比较复杂的界面时，使用第三方框架来编写界面是非常不错的选择，比如：React，Vue。

这些框架编写界面远远比使用原生JavaScript创建界面或者使用jQuery创建界面要简单太多。

并且使用React还可以很方便的给DOM编写CSS样式。当然前提是你已经对React有一定的了解。

首先需要安装几个包：

```shell
npm install react react-dom
npm install @babel/preset-react -D
```

- **react、react-dom**：这两个包是React相关包。
- **@babel/preset-react**：转换jsx语法。

接着在webpack中进行配置：

```js
{
  test: /\.(js|jsx|mjs)$/,
  exclude: /node_modules/,
  use: [
    {
      loader: "babel-loader",
      options: {
        // 预设：指示babel做怎么样的兼容性处理。
        presets: [
          [
            "@babel/preset-env",
            {
              corejs: {
                version: 3,
              },
              // 按需加载
              useBuiltIns: "usage",
            },
          ],
          "@babel/preset-react",
        ],
      },
    },
  ],
},
```

到这里就配置完毕，可以在脚本中使用React相关的语法。

入口文件：

```js
import React from "react";
import App from "./components/App";
import $ from "jquery";
import { render } from "react-dom";

// 添加一个div作为React的入口文件
$("body").append(`<div id="ccll-app"/>`);

render(<App />, document.getElementById("ccll-app"));
```

# 3. 编写脚本

逐步摸索了几个小时，我发现不仅可以使用**Bootstrap**这类的UI库，甚至连React的**Material-UI**都能够使用，也就是说你**通过webpack打包来进行开发脚本的话，你可以往网页上面嵌入任意的内容，就和你平时开发React一样。**

光说不练假把式，这里就演示一下我练手的Instagram脚本，实现了点击图片查看原图、图片批量下载。

该脚本在页面上面新增两个按钮，分别是：

- 图片查看开关
- 图片批量下载

![image-20210221183546970](/images/script/强大的油猴Tampermonkey脚本开发环境搭建/image-20210221183546970.png)

点击**图片批量下载**后，会弹出一个模态框，如下图所示，查看需要下载的图片，不过经过我的测试，最多貌似只能同时下载12张图片。

![image-20210221184229028](/images/script/强大的油猴Tampermonkey脚本开发环境搭建/image-20210221184229028.png)

能够引入UI框架还有一个好处，你不用再去为样式而发愁，直接引用框架，随便写写感觉就很高大上。

使用jQuery来操作网页上已经存在的元素，使用React配合UI来搭建辅助界面，可以让你在当前网页上面随意发挥，看不惯哪儿还可以直接修改元素的样式、内容等。

# 4. 最后

因为该文章是我先搭好了开发环境后又隔了一天，自己尝试制作完成一个脚本的情况下又跑回来完善了这篇文章。

你如果要用React或者Vue这种框架webpack环境已经帮你搭好了并且经过很多个版本的迭代和修复，几乎已经不需要你再去修改什么。

但是像上面这种油猴开发环境就仁者见仁智者见智，需要什么就添加什么，比如我需要使用sass，我就增加一个**sass-loader**，我需要使用ts，我就增加一个**ts-loader**，这些都是看自己心情。

在没有学习webpack之前，我是肯定不会想到js的脚本可以如此强大，学习了webpack后让我能够随心所欲的修改任意的网页（当然仅本地有效），不得不说webpack还是没有白学。

因为是编写脚本的关系，其实大多数情况下根本不需要去创建界面，所以仅使用jQuery就已经够用了，因为在脚本中引入React会导致脚本的体积变得异常的庞大，什么都不写打包出来的脚本都有100多KB。

不过脚本是存在于本地然后运行，所以理论上脚本的大小其实没有太大的影响，实现功能就可以了，这一点和Web开发不大一样。

在Web开发中，如果一个文件太大，客户的网速又不好的情况下，会极大的影响用户体验，而脚本的大小就不被这些限制。

