---
title: "小程序的各种限制：这儿不行，那儿不行"
date: 2025-03-31 21:06:35
---


上一篇文章[小程序开发框架全解析：从分包到资源优化的实战指南](/2025/03/27/小程序/小程序开发框架全解析从分包到资源优化的实战指南/)中，我介绍了小程序的开发框架，本篇文章我主要说一下我开发小程序遇到的各种限制：这儿不行，那儿不行。

我开发小程序使用的是uni-app vue2版本，我们目前所研发的项目都是使用Vue2版本，因为我们当时天真的以为Vue3版本还不稳定，不适合用于生产环境，并且Vue3+TypeScript招人的成本会高很多，所以我们迟迟没有使用Vue3技术栈。

但是今年年后，我被派到另一个项目组进行支援，接手他们项目的时候发现其它部门早早的用上了Vue3+TypeScript技术栈，支援结束后，我们部门又接手了另一个部门的项目，发现他们部门也开始使用了最新的技术栈。

所以我的意思是Vue3的技术栈已经非常成熟了，如果你们公司还没有使用Vue3技术栈，那么我建议你们可以考虑一下。

## 1. ::v-deep

如果你要开发除了微信小程序以外的其他小程序，那么我建议你避免使用sass的`::v-deep`，因为我之前开发小程序的时候，发现uni-app编译为抖音小程序的时候，抖音小程序无法正确的识别这种语法，如果你看过uview的源码，那么你会发现uview中几乎每个组件都支持customStyle属性，这个属性让你不需要使用sass的`::v-deep`，从而修改组件的样式。

## 2. <view> 标签

我推荐开发小程序的时候，尽量都使用<view>标签，不要追求什么语义化，如果你使用了相关的插件，那么你就尽量全部使用<div>标签，因为我发现其它标签在打包成多端小程序的时候，会有样式上面的问题。

我们开发小程序的时候一般是以微信优先，使用其它标签可能在微信小程序中显示正常，但是在其它小程序中显示可能会出现异常。

## 3. input textarea

慎用textarea，因为它在打包成其它小程序的时候会有各种各样的兼容问题。比如：样式不一致，在手机上面按住删除按钮时光标会乱跳等问题。

## 4. 权限限制

微信小程序现在的权限限制越来越严格，如果你需要获取用户的地理位置、剪贴板等等，你最好都看一下隐私政策中是否有相关的权限需要填报，否则开发环境下可能没问题，但是生产环境会出现调用不成功的情况。

最新的微信小程序开发者文档已经明确要求，在使用敏感API前，必须在用户隐私保护指引中说明，并且获得用户同意。2023年后，微信对获取位置、剪贴板、相册等权限的管控更加严格，甚至在某些场景下需要用户二次确认。

## 5. DOM问题  

小程序中是没有DOM的，所以你不要想着使用DOM操作，比如：document.getElementById、document.querySelector等等，你只能使用小程序提供的方法来获取DOM的属性，比如宽高、位置等。

但是你无法通过操作DOM来实现一些功能，比如：你无法动态创建一个DOM，然后插入到页面中。

这一限制在所有小程序平台都存在，使用uni-app等框架时，可以利用其提供的跨平台API如`createSelectorQuery`来代替DOM操作。记住，小程序不是浏览器环境，没有window和document对象。

## 6. 全局组件问题

由于小程序使用的并不是单页面应用，所以全局组件非常不好实现，我们目前使用的登录弹窗都是需要使用的页面进行单独引入，如果你的产品有一个非常复杂的全局组件需要实现，那么我推荐尽早放弃，因为我们实测的结果是，这种全局组件的状态非常难以管理。

## 7. Tailwind css

如果你要使用Tailwind css那么必须配合[postcss-loader](https://github.com/postcss/postcss-loader)使用，并且如果你使用的是Vue cli4，那么我推荐使用unocss，而不是Tailwind css，因为Tailwind css 3 版本无法用在postcss7上面，而Vue cli4刚好内嵌的就是Postcss7。

如果你非要使用Tailwind那么你就只能使用Tailwind cli的方式，单独运行Tailwind编译器，但这种方式缺点非常大，一是无法使用完全体Tailwind语法，比如@apply，二是编译器时常会报错。

### 7.1 不要使用Tailwind 4

小程序本身对于CSS的兼容性就非常差，CSS中带`\`都需要第三方插件进行支持，更别说Tailwind 4使用了更多的CSS新特性，并且我推荐现阶段在生产项目都不要使用Tailwind 4。

其一是对于2023年前的Chrome浏览器，有非常多的兼容性问题，其二是运行Tailwind 4需要某些Rust环境，新入职的同事并不能只安装一个Node环境就可以成功的启动项目。

## 8. 支付

每个小程序的支付都需要后端单独去对接，而且微信小程序的支付功能必须要企业认证，个人认证的小程序是无法使用支付功能的。

所以如果你小程序想要使用支付功能，那你就必须要通过企业认证。

## 9. 小程序认证

以微信为例，每次小程序的认证费用是30元，并且这个费用如果一旦认证失败是不会退还的，十分的霸王条约，即便认证成功，认证有效期也仅仅只有一年，到了第二年你还需要继续认证。

如果不进行认证，那么其它用户除了扫码二维码或者收到人家的分享，除此之外是无法直接在微信中搜索到你的小程序。

## 10. 网络请求限制

小程序的网络请求有严格的域名限制，所有的请求域名必须在小程序管理后台进行配置。微信小程序允许配置最多20个域名，其中包括普通域名、socket域名、上传域名等。

开发环境下可以勾选"不校验合法域名"选项，但上线后必须使用已配置的域名。对于需要请求多个第三方服务的复杂应用来说，这个限制可能会造成不少麻烦。一个解决方案是使用自己的服务器作为中转，统一域名入口。

## 11. 小程序动画限制

小程序的动画能力受到一定限制，复杂的动画效果可能在不同机型上表现不一致。原生动画API相对简单，难以实现复杂的交互动画。

对于需要精细动画控制的场景，可以考虑：
1. 使用CSS动画代替JS动画
2. 利用微信小程序的Canvas实现复杂动画
3. 对于高端机型，可以尝试使用新的Worklet动画（仅支持Skyline渲染引擎）

## 12. 结语

小程序开发过程遇到的问题非常多，比起H5开发，小程序更多的是很多H5的写法都不能使用，但是小程序有一个巨大的好处，就是小程序的官方提供了很多的工具方法，比如定位、获取屏幕尺寸等。

随着各家小程序平台的不断发展，限制与能力都在不断变化。虽然有各种各样的限制，但小程序依然是触达用户的重要渠道。作为开发者，我们需要在这些限制下找到最佳的开发方案，发挥小程序平台的优势。