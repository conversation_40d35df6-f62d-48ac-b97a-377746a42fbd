---
title: "小程序开发框架全解析：从选型到打包的实战指南"
date: 2024-07-14 22:04:25
categories:
  - "小程序"
tags:
  - "小程序"
---


# 1. 前言

消失了快2年半时间，因为两年半之前新入职了一个公司，然后因为有很多事情要忙，虽然后面学习了很多新技能，但是一直都没有空闲功夫重新开始写文章，最近有了时间。

因为这两年半大部分时间都在开发小程序，从微信到抖音到百度到支付宝，遇到了很多的问题，所以对于小程序开发这块还是有非常多的经验的，我就准备写这一系列的小程序开发文章，来好好的说一下如何开发一个小程序，以及存在的问题。

## 1.1 为什么开发小程序

小程序拥有很多优势，最大的优势就是**不需要用户下载**，直接点击短信或者H5链接就可以直接打开小程序进行使用，从产品的角度来说就是降低了下载APP这个路径上面的用户损失。

对于用户来说，现阶段很多用户都习惯了小程序的便利性，不用下载就可以直接打开，并且有类似于原生APP般的体验。

连我周围的很多程序员，在App和小程序之间都会优先使用小程序。

但对开发者来说，做小程序就是一个折磨的事情，你会发现在做的时候会遇到各种兼容问题，尤其是你需要打包成不同平台的小程序时，对于某一个问题真是完全没有头绪。

但不管怎么说，开发小程序已经是现在的web前端岗位从业者避不开的一环。

# 2. 框架选择

一般来讲，现在的前端从业者在Vue、React两个技术栈上面肯定会一个，而在初级前端或者中级前端这个阶段，使用Vue的开发者是最多的，因为Vue技术栈不光拥有完整的中文文挡、华裔研发，还拥有完整的生态、平滑的学习曲线。

就我过去的经验来说，选择Vue会极大的**降低企业的招人成本**，意思就是会Vue比会React或者其它框架的开发者多的多，但也因为Vue会的人太多，所以是非常内卷的，如果不掌握更深层次的东西，工资低，跳槽的时候也很难找到合适的工作。

## 2.1 为什么不选择原生？

在小程序界，最有代表性的就是微信小程序，现在很多小程序规范都是由微信定的，那么为什么不直接使用微信官方的开发方式去开发小程序，而要去使用Uni-app、Taro这种第三方框架呢？

第一个原因就是额外的学习成本，在企业中开发小程序往往都对应着多个平台，如果使用小程序提供的原生开发，那么每个平台都需要单独去学习并且进行开发，无论是学习成本还是维护成本都非常的高。

而选择第三方框架，只需要使用Vue、React等常见的前端技术栈，至于小程序是否能够识别对应的代码，那就交给第三方框架的维护者进行处理，这样就可以使用一套代码，开发多个平台的小程序，学习成本以及维护成本都会大幅降低。

但是！由于各个小程序语法上面的区别，所以使用框架开发小程序时，每个端几乎都有各种不同的兼容性问题，而这些问题处理起来是非常的麻烦以及棘手，有时候完全只能凭借自己的经验进行猜测问题的原因，甚至有些兼容性问题是无法进行处理的，完全就是小程序平台本身的缺陷！

### 2.1.1 浅谈兼容性

我所在的公司目前使用Uni-App开发的小程序会打包到4个平台，分别是微信、抖音、百度、支付宝。

对于这4个平台的小程序，我个人认为的坑爹排名：支付宝>抖音>百度>微信。

其中支付宝需要处理的兼容问题是最多的，我认为阿里在国内也是拥有数一数二的前端团队，但支付宝小程序的开发居然让我如此难受。

抖音小程序的团队就是想要的太多，在今年早些时候，抖音上面用户支付的订单，必须经过抖音订单中心进行处理，并且只有通过抖音处理过的订单，你才能获得这部分收益。

这个流程就是 用户下单 -> 后端创建订单 -> 用户成功支付 -> 后端汇报给抖音 -> 抖音生产订单 -> 小程序内部获取订单号 -> **强制弹窗让用户开启订单** -> 订单完成 -> 账户收款。

不知道这个流程大家看出问题没有，也就是抖音在中间强行插入了一个用户确认开启订单的弹窗，如果用户不确认，即使完成了订单，那么这笔帐也不会打到公司的账户上。

而我们内部经过讨论后，还是觉得获得收益比用户体验更为重要，即用户一旦有未确认开启的订单，我们就会疯狂弹窗直到用户确认订单，如果用户不确认，这个弹窗就会一直弹出。

所以如果你要做抖音小程序，并且有支付功能，那么后端就一定要去对接抖音的订单中心。

## 2.2 用户接受度

无论从开发体验还是使用人数，微信小程序都是遥遥领先，我们公司的运营团队多次想推广抖音和支付宝，但每次效果都十分不理想，现在大部分的时间都只投放微信小程序，但是也不将其它小程序停掉，搞得每个版本都需要处理各种小程序的兼容问题。

## 2.3 包体积问题

微信小程序的开发体验是最好的，使用Uni编译后的微信小程序代码几乎不存在兼容性的问题。

但是！2024年了！都2024年了！微信小程序的主包依然只支持2M！由于公司的小程序已经迭代1年多了，为了主包不超过2M，我们做了非常多的努力。

我们从以下几个方面进行了改进：

1. 除了tabBar页面，其它页面全部放入分包中，因为tabBar页面必须要放入主包中。
2. 编写Webpack、Babel插件，将小程序中的所有图片资源，在编译时上传到服务器中，并且将编译后的代码中的图片地址替换成服务器中的图片地址。
3. 尽量减少使用第三方包，在项目中，我们除非必要时刻，否则不会引入第三方包。
4. 时间处理使用`Day.js`！不要使用`Moment.js`！因为`Moment.js`打包进入项目中会占100kb以上的体积。
5. 多和产品Battle，时刻强调主包中不能再加功能了，不然无法发布，对于要加入主包中的功能要学会拒绝。

如果你顺利的处理了微信小程序的包体积问题，那么其它平台的小程序都不会有包体积问题，因为只有微信的主包才限制了2M，抖音的主包限制4M，百度和支付宝似乎不限制主包大小。

## 2.4 广告

对于小程序来说，是可以接入广告的，然后带来收益，而对于主体为 个人 的小程序，广告几乎是唯一变现的方式，因为个人小程序是无法开通支付功能的。

# 3. 框架

## 3.1 Uni-App

我公司全部使用的是Vue技术栈，所以优先选择了Uni-App，至于版本，推荐使用Vue2的版本，因为Vue3的版本目前存在很多问题，并且可能无法使用uView ui框架。

但Vue2存在一个问题，那就是它的打包框架是Webpack 4的版本，目前webpack 5已经出来好几年了，相关的技术都十分成熟了。

这里就需要说到Vue的一个问题，那就是**高度封装**，Vue官方团队将Webpack内嵌进了vue cli中，这样做虽然会大大降低框架的入门难度，但也会带来一个巨大的隐患，那就是不自由。

也就是我如果想要升级Webpack 5，那么我就只能升级vue cli，而好巧不巧的是Uni这个框架的Vue2版本对于新版的vue cli又不兼容，对于升级vue cli我做了很多尝试，各种文档都试过了，总是有意料之外的报错，最后只能放弃。

那么为什么好端端的框架，我对Webpack 5有那么强大的执念呢？其实不是我对Webpack有执念，我是对于PostCSS有执念，前面讲到了Vue高度封装的问题，除了封装了Webpack外，它还将PostCSS也封装进了vue cli中，而在vue cli 4中，PostCSS被限定了版本为7，如果想要使用PostCSS 8，那么只能升级vue cli。

至于我为什么需要PostCSS 8，那时因为Tailwind 3.x版本需要PostCSS 8，而3.x对比2.x版本，它多了一个自定义变量的功能。

对于Tailwind css的强大之处，在后面的文章中会详细说明。

## 3.2 Taro

由于我只在自己开发小程序时使用了Taro框架，而且只会打包成微信小程序，所以在使用Taro的时候我发现体验是非常的好，因为Taro 3它改变了自己的框架架构，可以使用几乎所有React的高级特性，不会存在太大的兼容问题，所以我在开发自己小程序的时候选择了Taro。

从我自己开发的小程序的体验上来讲，Taro的开发体验完全接近于React的开发体验，高级语法可用，TypeScript支持好，打包后无明显的兼容问题，后面我看了Taro团队发表的[架构文章](https://mp.weixin.qq.com/s?__biz=MzU3NDkzMTI3MA==&mid=2247483770&idx=1&sn=ba2cdea5256e1c4e7bb513aa4c837834)，我觉得Taro团队的理念非常好，他们在React和代码渲染成小程序的DOM过程中，还加了一层DOM层：react-reconciler，实现DOM渲染问题。

由于各个小程序的差异，React的虚拟DOM并不能直接绘制在屏幕上，传统的做法是将Vue或者React代码直接编译成为小程序代码，那么框架团队就需要负责转化代码这一步，这也是为什么在Uni中，十分多的Vue高级特性都无法进行使用的原因。

而Taro团队在Taro 3的时候就重构了架构。直接不管代码转化这一步，他们开发了react-reconciler这个包，将react的代码通过该包处理后能够直接在小程序上面渲染。

这时，他们根本就不关心怎么将React代码转化成小程序代码，他们只需要维护那个react-reconciler包，将React的代码能够直接在小程序上面渲染，这样不仅可以随时使用最新的React版本，还能大幅度减少兼容性工作。

但存在性能问题，以前的框架是直接转化成小程序代码，而新的架构是将React代码直接在小程序上面渲染，如果你做过SEO项目就会明白，一个是打包时编译，一个是运行时编译，所以通过Taro 3开发的小程序性能上会比以前的架构差一些。

但其实我使用过Taro 3后，并没有发现性能不足的情况，我个人觉得开发体验提升的这一大截远远比那一点性能损失更为重要。

# 4. 打包上传

如果你只开发一个微信小程序，那么只需要使用命令打包，然后在编辑器上面上传代码到微信平台即可。

但是如果你要开发多个平台的小程序，那么你就需要分别打包，然后打开各自小程序的编辑器，挨个上传代码，这一套下来如果像我一样需要打包到4个平台，然后将测试二维码发送给测试，那半个小时就过去了。

一次手动打包4个小程序平台感觉还好，但是在测试的过程中每修改一个问题，就需要同时打包4个平台的小程序，是一个十分消耗耐心的过程。

幸运的是，每一个小程序都提供了cli npm包，让开发者能够直接不打开小程序平台编辑器，直接使用js代码就能将小程序上传到平台。

那么它们的npm包分别是：

- 微信小程序：[miniprogram-ci
](https://www.npmjs.com/package/miniprogram-ci)
- 支付宝小程序：[minidev](https://www.npmjs.com/package/minidev/v/1.7.1-rc.2)
- 百度小程序：[swan-toolkit](https://www.npmjs.com/package/swan-toolkit)
- 头条小程序：[tt-ide-cli](https://www.npmjs.com/package/tt-ide-cli)

除了微信小程序外，使用cli包上传成功后，都会直接返回测试二维码地址，你只需要保存下来就可以发送给测试，而微信小程序的测试二维码就只能到微信小程序后台中进行获取（也可能是我没有找到获取二维码的api）。

# 5. 结语

小程序由于用户体验良好、链路短等原因，所以现在很多公司都会研发小程序，甚至会作为C端的主要平台。

但虽然小程序的用户体验是非常好的，但对于开发者的来讲，开发体验是非常差的，摸不着头脑的兼容问题、残废般的Vue、React语法、小程序平台的各种霸王条款，但不得不承认，作为一个Web开发者，小程序已经是必备技能之一了。
