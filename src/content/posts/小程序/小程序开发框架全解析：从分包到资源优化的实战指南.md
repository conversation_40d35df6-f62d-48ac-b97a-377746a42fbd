---
title: "小程序开发框架全解析：从分包到资源优化的实战指南"
date: 2025-03-27 22:38:17
---


上一篇文章 [小程序开发框架全解析：从选型到打包的实战指南](/2024/07/14/小程序/小程序开发框架全解析从选型到打包的实战指南/) 中，我介绍了小程序的开发框架，本篇文章我将继续分享一些在实际开发过程中遇到的关键问题和优化策略。

## 1. 小程序的分包策略

小程序采用的是按需加载机制：初次启动时只会加载主包内容，只有当用户访问分包页面时，才会动态加载对应的分包资源。这种机制可以有效减少小程序的启动时间，提升用户体验。

如果使用小程序的默认tabbar，那么tabbar中的页面必须放在主包中，否则小程序将无法正常编译。这是微信小程序框架的硬性规定，需要在开发初期就考虑到。

目前微信小程序的主包限制是最严格的，主包大小上限为2MB（2048KB）。如果超过这个限制，小程序将无法上传和发布。

就我个人目前经验来说，我推荐能放入分包的页面统统都放入分包，也就是说除了如果是用的默认tabbar，那么除了tabbar中的页面都建议放入分包中，如果是自定义tabbar，那么除了首页都放入分包中，这样有一个好处，随着小程序的不断迭代，主包的代码会越来越大，如果一开始没有做好规划，那么后期极有可能出现主包超过2M的大小，导致无法上传。

### 1.1 分包

我推荐只使用一个分包，因为各个分包之间如果你想要使用同一个组件，那么你只能将组件放在主包中，这样就会导致主包的代码越来越大，而如果你只使用一个分包，那么你就可以将组件放入分包中。

我就吃过这个亏，当时小程序建立时，没有做好分包规划，建立了大量的分包，随着项目的迭代，导致有些公共组件不得不放入主包中，导致主包持续被优化到极致，到现在已经无法再承受更多的代码。

### 1.2 为什么要一开始就要做好分包规划

因为投放那边会投放某些小程序的页面，并且有时候H5或者APP会跳转到对应的小程序页面，所以后期小程序的页面路径是不能轻易更改的，你一旦更改了，那么投放的页面就无法访问了，所以前期做好分包规划是非常重要的。

## 2. 图片

由于小程序的包体积限制，所以我们在开发小程序的时候图片都是放在服务器的，然后通过网络请求获取图片资源。

实现这个功能有两种方法：

1. 让运维或者自己手动将图片放在服务器，然后在项目中通过网络请求获取图片资源
2. 编写打包框架对应的插件，检测到本地图片资源后，将图片上传到服务器，编译后的文件直接替换成网络请求的图片资源

### 2.1 方法一

方法一手动上传图片的实现非常简单，也是大多数小程序开发者的做法，但是这种方法的缺点是，图片资源需要手动上传到服务器，并且还需要记录图片的网络地址，如果图片资源很多，那么操作起来是非常麻烦的。

而且我们在UI验收的时候，经常会遇到需要替换图片的情况，那么就需要再次手动上传图片，并且替换图片的网络地址，非常麻烦，也十分不灵活。

### 2.2 方法二

如果你用的是Webpack框架，那么你就需要编写Webpack的插件，如果是Vite框架，那么你就需要编写Vite的插件。

这种方法实现起来就需要你对这些打包框架有一定的了解，并且需要有一定的编程能力，因为涉及到一些比较复杂的代码编写，这里就不展开了。

我们目前就是使用了第二种方式，这里大概讲一下我的实现过程，在框架编译的过程中，如果检测到了本地图片路径，那么就会将图片上传到服务器，并且将图片的网络地址替换为上传后的网络地址。

为了实现这个功能，那么你就必须要做一个缓存文件，缓存本地图片地址对应的网络地址，这样才不会导致图片资源被重复上传。

我们最开始尝试了本地地址对应网络地址的形式：

```json
{
    "@/assets/images/logo.png": "https://example.com/logo.png"
}
```

但是这种方式存在一个问题，那就是你替换了对应的图片，但是名字这些都不改，那么这张图片就不会再被上传，你必须得改一个新的图片名，或者删除缓存文件中这张图片对应的缓存。

```json
{
    "./logo.png": "https://example.com/logo.png"
}
```

这种相对路径引入的图片方式，图片路径可能会重复。

后面经过思考，我决定使用图片的Hash值作为缓存文件的key，这样就可以避免上述问题，一旦更改了图片，那么Hash值就会发生变化，图片就会被重新上传，这种形式也可以避免使用相对路径引入图片时，图片路径可能会重复的问题。

## 3. 请求封装

最后来说说请求封装，我们熟悉使用的axios，在小程序中是不能使用的，如果非要使用axios，那么你就需要使用第三方包。

不过我们是直接封装了小程序原始的请求方式，然后添加了请求拦截器和响应拦截器，这样就可以在请求拦截器中对请求进行处理，在响应拦截器中对响应进行处理。

因为请求头设置以及错误处理等，都必须要进行封装。

## 4. 结语

小程序的包体积大小是一个让人非常头疼的问题，并且小程序的流畅度是远远低于APP的，我们C端产品有小程序和APP版本，我体验APP的时候就感觉明显比小程序流畅太多了。

但由于APP的开发成本高，需要下载才能使用，所以小程序就成为了我们C端产品的主要推广渠道。

下一篇文章就说一说小程序的两个不行：这里不行、那里不行。