---
title: "unity3D复刻保卫萝卜"
date: 2020-05-04
categories:
  - "游戏制作"
tags:
  - "游戏设计"
  - "unity3d"
---

> 上次那个快乐鸟-html版似乎反响还不错，看来大家对游戏制作还蛮感兴趣的...那么这次我要搬出我前年用unity3D复刻的保卫萝卜。
>
> 因为年份太过于久远了，所以具体代码就不进行分析了，主要是我忘了（狗头），研究前端后就再也没有碰过unity3D。

视频在这里：

[unity3D复刻保卫萝卜](https://www.bilibili.com/video/BV1HW411d7DMwww.bilibili.com)

当时参照了siki学院的思路，然后自己独立进行一些加工将它完成了，如果有兴趣的朋友可以直接在siki学院上面搜索一下...

大概说一下自己当时独立加工的点的实现方式：

![img](/images/v2-678267ad3758d59e1021cd1fd19056b3_720w.png)

# 地图编辑器

将屏幕分为 8*12 个格子，判断鼠标点击时最近的格子，通过视频上面也可以看出即使是没有点在格子的正中部分，依然可以将那一格进行点亮。当地图编辑完毕时点击保存，就将该地图信息，记录在CSV中，通过CSV进行导入。

CSV有个最大的好处就是可以通过Excel打开，如果是用json存取数据，就没有CSV展现的数据那么清晰。

# 核心算法

通过计算出每个格子中心点的坐标，让怪物沿着已经设定好的路径点通过 Translat 进行移动，如果当怪物离某个路径点的距离小于某值，则让它朝着下一个点进行移动，如果已经达到终点，则将怪物的渲染关闭。搜寻怪物逻辑即将所有怪物放在一个数组中，先遍历这个数组，然后判断怪物是否在塔的攻击范围中，如果在塔的攻击范围中，则将炮塔的目标设置为当前怪物。

# 对象池

unity生成和销毁实例跟前端生成Dom一样，是十分消耗性能的，所以这就涉及到实例的复用。意思就是当怪物“死亡”时，其实不进行销毁，而仅仅是将怪物的渲染关闭，如果下次需要使用，则对这些事例重新进行初始化。就跟前端将`display`设置为`none`一样。

整个项目的实现难度并不难，但是里面包含的点非常多，包括UI，动画，对象池，数据的本地存取等，很适合作为初学者项目。
