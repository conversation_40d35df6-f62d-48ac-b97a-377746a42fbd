---
title: "个人博客评论系统：Valine"
date: 2020-06-24
categories:
  - "blog"
tags:
  - "个人博客"
---


之前我也写过一篇个人博客的评论系统：Gitalk。

https://zhuanlan.zhihu.com/p/141078552

但是这个评论系统有一个不太好的地方，它需要使用GitHub账号才能够进行评论，这就涉及到很多人不想暴露自己的隐私或者没有GitHub账号就无法进行评论。

所以当我在重新搭建我的个人博客时，找了一款新的评论系统：Valine。

https://valine.js.org/

它可以实现不用进行登录就可以使用留言功能。

![img](/images/v2-9f9e697dd96073a019396aac5f531096_720w.png)

# 快速开始

首先需要一个LeanCloud账号：

https://leancloud.cn/dashboard/login.html#/signup

注册时需要进行实名认证。

![img](/images/v2-f66fe2f66f86046bf32f382c78517fef_720w.png)

应用创建好以后，进入刚刚创建的应用，选择左下角的设置>应用Key，然后就能看到你的APP ID和APP Key了：

![img](/images/v2-806cbb9bc64a5463238e541e711a7764_720w.png)

接下来直接参考

https://valine.js.org/quickstart.htmlvaline.js.org

进行配置就可以了。

# 最后

由于各种实名认证，最后我放弃将它集成在我的博客上面，有兴趣的朋友可以直接按照官方文档进行集成，还是非常简单方便的。
