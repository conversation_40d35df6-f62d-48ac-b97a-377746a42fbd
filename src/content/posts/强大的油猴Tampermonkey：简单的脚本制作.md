---
title: "强大的油猴Tampermonkey：简单的脚本制作"
date: 2020-05-28
categories:
  - "JavaScript"
tags:
  - "油猴脚本"
  - "浏览器插件"
---


# 1. 油猴是什么？

可以让你在网页上使用脚本，同时它拥有非常多的其他用户开放出来提供下载的脚本，如果你会JavaScript，你甚至可以自行编写脚本实现你想要实现的事情。

# 2. 油猴和Chrome扩展应用

Chrome扩展应用本质上是`HTML+CSS+JavaScript`而油猴脚本仅仅是一个`JavaScript`文件，而且谷歌对于扩展应用的审核比较严格，甚至还需要缴纳审核费用。而油猴脚本其实是简化的Chrome插件，它没有上面的那些限制。

# 3. 安装

如果是能科学上网，就直接进入到Chrome应用商店安装。

# 4. 入门

如何下载第三方脚本在这里就不提了，这里只说一下如何创建一个新的油猴脚本文件。

![img](/images/v2-4ae5c4cc1df050759e13bfbd48764dcd_720w.jpg)

点击后可以看到下面的代码：

```js
// ==UserScript==
// @name         New Userscript  //脚本名字
// @namespace    http://tampermonkey.net/ //脚本命名空间
// @version      0.1 //版本
// @description  try to take over the world! //描诉
// <AUTHOR> //作者名字
// @match        https://www.tampermonkey.net/index.php?ext=dhdg //应用在哪个网站上
// @grant        none //添加油猴的API
// ==/UserScript==

(function() {
    'use strict'; // 严格模式

    // 从这里开始写代码
})();
```

我们先来编写一个脚本练练手，脚本的目标是当访问bilibili主页时，会弹出"Hello World!"

```js
// ==UserScript==
// @name         Hello World!
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  try to take over the world!
// <AUTHOR>
// @match        https://www.bilibili.com/
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    alert("Hello World!");
})();
```

![img](/images/v2-77fdbe51956f0aa735764caebb2055d7_720w.jpg)

# 5. 进阶

经过上面的简单的测试后，下面来看一下如何用油猴脚本将B站打开的网页图片放在一个方框中进行显示，然后点击可以直接进行预览。

![img](/images/v2-134f4d56dc979fcc5d4dadb7775f4f38_b.webp)

最终效果

# 6. 准备

- 引入jQuery
- 引入Bootstrap
- 引入Vue

需要准备以上的3样东西，jQuery是用来操作DOM，Vue是用来创建页面，而Bootstrap是用来丰富样式。这里这3个库的使用方法就省略了，如果有兴趣可以看一下相关的文章。

```js
// ==UserScript==
// @name         B站图片爬取
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  try to take over the world!
// <AUTHOR>
// @match        https://www.bilibili.com/*
// @grant        none
// @require      https://cdn.jsdelivr.net/npm/jquery@3.4.1/dist/jquery.slim.min.js
// @require      https://cdn.jsdelivr.net/npm/vue
// ==/UserScript==

(function () {
  "use strict";
  $("head").append($(`<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css">`));
})();
```

引入了这三个库后就可以为所欲为了。

# 7. 外部引入脚本

由于油猴编辑器太难用，如果在外部编辑器编辑好了再复制到油猴脚本里面，这个过程就比较繁琐，所以我们可以**让油猴插件引用外部文件。**

在扩展里面找到油猴插件点击`详细信息-允许访问文件网址`

![img](/images/v2-e79a68dae8f50f075a296eb75dc423dc_720w.jpg)

![img](/images/v2-0228dbdc2cad802583147263f0d0891f_720w.jpg)

剩下的在脚本中编写：

```js
// @require      file://E:\study\tampermonkey\test.js(脚本的地址)
```

到这一步就万事俱备，只差代码了！由于代码的编写过程与本篇幅无关，所以直接贴上最后的代码。

```js
// ==UserScript==
// @name         B站图片爬取
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  try to take over the world!
// <AUTHOR>
// @match        https://www.bilibili.com/*
// @grant        none
// @require      https://cdn.jsdelivr.net/npm/jquery@3.4.1/dist/jquery.slim.min.js
// @require      https://cdn.jsdelivr.net/npm/vue
// ==/UserScript==

(function () {
  "use strict";
  $("head").append($(`<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css">`));
  var divApp = $(
    `
<div style="position:fixed; z-index:1000;top: 80vh;left: 5vw" id="myPlan">
  <div v-if="showMassage" style="position: fixed;z-index: -1;width: 100vw;height: 100vh;top: 0;right:0;background-color:rgba(0,0,0,0.5);"></div>
  <button class="btn btn-primary" @click="showM">提取图片</button>
  <div v-if="showMassage"
       class="container"
       style="position:fixed;background-color:rgba(0,0,0,0.5);;display: flex;flex-wrap: wrap;overflow: auto;width: 600px;height: 500px;top: 50%;left: 50%;transform: translate(-50%, -50%);">
       <div  v-for="(image,index) in images"  :key="index">

       <a :href="image.src" target="frame1"><img :src="image.src" class="rounded float-left" style="min-width: 100px;margin: 10px;height: 80px" alt=""></a>
</div>
</li>
</div>
</div>
</div>`
  );
  $("#app").append(divApp);

  /*Vue操作*/
  var app = new Vue({
    el: "#myPlan",
    data: {
      showMassage: false,
      images: []
    }, methods: {
      showM() {
        this.showMassage = !this.showMassage;
        this.images = $("img");
      }
    }
  });

})();
```

# 8. 总结

Bootstrap其实不引入也可以，没有怎么用到，Tampermonkey如果会用的话，还是非常好用的，甚至还可以当做爬虫使用，油猴对于前端来说使用起来几乎没有任何门槛，但是对于没有编程经验的人如果想要自行编写脚本，那还是具有一定的难度。

