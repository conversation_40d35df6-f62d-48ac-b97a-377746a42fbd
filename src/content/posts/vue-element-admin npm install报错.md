---
title: "vue-element-admin npm install报错"
date: 2020-05-04
categories:
  - "JavaScript"
tags:
  - "JavaScript"
  - "Vue"
---

> 最近看到[vue-element-admin](http://panjiachen.github.io/vue-element-admin)后台模板不错，于是准备研究一下，结果进去就吃了个闭门羹，npm install各种报错。

# 我遇到的错误

`enoent undefined ls-remote -h -t ssh://**************/sohee-lee7/Squire.git`

![img](/images/v2-daea995e8644bf9c9e37fc65e754b916_720w.jpg)


```shell
npm ERR! code ENOENT
npm ERR! syscall spawn git
npm ERR! path git
npm ERR! errno -4058
npm ERR! enoent Error while executing:
npm ERR! enoent undefined ls-remote -h -t ssh://**************/sohee-lee7/Squire.git
npm ERR! enoent
npm ERR! enoent
npm ERR! enoent spawn git ENOENT
npm ERR! enoent This is related to npm not being able to find a file.
npm ERR! enoent
```
## 解决方法

需要安装git，并且配置环境变量`C:\Program Files\Git\bin`（具体根据自身电脑上的git安装目录确定）。

# 官方提到的错误

![img](/images/v2-7964f6c2416a7cccd1fc956b971fbe52_720w.jpg)


```shell
npm ERR! code ELIFECYCLE
npm ERR! errno 1
npm ERR! node-sass@4.14.0 postinstall: `node scripts/build.js`
npm ERR! Exit status 1
npm ERR!
npm ERR! Failed at the node-sass@4.14.0 postinstall script.
npm ERR! This is probably not a problem with npm. There is likely additional logging output above.

npm ERR! A complete log of this run can be found in:
npm ERR!     C:\Users\<USER>\AppData\Roaming\npm-cache\_logs\2020-05-04T08_05_47_402Z-debug.log
```

*   官方提出的错误，node-sass经常性报错，经尝试使用淘宝源可以成功安装

*   之前在自己的项目中使用node-sass的时候使用yarn也能成功进行安装

永久更换淘宝源

```shell
npm config set registry https://registry.npm.taobao.org
```

