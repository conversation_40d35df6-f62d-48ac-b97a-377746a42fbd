---
title: "提升写代码效率：快速通过DOM元素打开对应的Vue文件"
date: 2021-4-4 23:10:51
categories:
  - "Vue"
tags:
  - "Vue"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

在Vue项目的编写中，你是否遇到接手别人项目后根本找不到一个界面对应的文件的尴尬？

你是否在写一个项目后模块太多，导致更改某个界面时找不到该模块对应的文件（即使这些都是你自己编写的）？

我就时常会遇到这样的问题，有时候找一个模块对应的文件需要花上1分钟，但是看了本篇文章后你就会习得点击页面上对应的元素，一键在编辑器中打开它对应的Vue文件。

我们通常是使用浏览器自带的`Console`进行调试，但是`Console`里面呈现的DOM树已经是被编译后的，你根本无法得知该DOM元素是位于Vue项目中的哪个文件，只能全凭猜测，或者根据界面路由进行查找，但其实还有更加方便的方式，那就是`Vue.js devtools`工具。

那么话不多说，我们直接来看一下`Vue.js devtools`工具应该怎么用：

# 1. 安装

相信作为一个合格的程序员，你一定可以去到外面的世界，那么直接在Chrome应用商城里面搜索`Vue.js devtools`。

![image-20210328125847157](/images/Vue/提升写代码效率：快速通过DOM元素打开对应的Vue文件/image-20210328125847157.png)

有两个，下面那个beta是针对`Vue3.x`的项目，上面那个是针对`Vue2.x`的项目，当然是推荐两个都安装上啦~

安装完毕后，直接启动Vue项目，打开控制台，会看到下面的两个Vue（当然有可能只有一个）：

![image-20210328130939456](/images/Vue/提升写代码效率：快速通过DOM元素打开对应的Vue文件/image-20210328130939456.png)

一个是`Vue3版本`一个是`Vue2版本`，两个都点击一下，如果看到有DOM元素，那么就是对应的工具：

![image-20210328131123031](/images/Vue/提升写代码效率：快速通过DOM元素打开对应的Vue文件/image-20210328131123031.png)

当然这是直接通过**Vue脚手架**搭建（`vue create xxx`）的项目，即使用的官方webpack的配置，相信很多朋友手中的项目可能是通过`vue init webpack xxx`搭建的老项目，它直接将所有的webpack配置都暴露出来（如果你是一个小团队没有精通webpack配置的人千万别这么做！因为一旦通过这种方式创建的项目，webpack配置就只有手动进行更新，而`vue create xxx`这种方式Vue官方会更新webpack的配置）。

那么如果你是通过`vue init webpack xxx`这种方式搭建的项目，你可能打开控制台看不见上面的Vue标题栏，你要做的就是强制开启`Vue.js devtools`工具，开启的方式官方已经提供的非常明确了：

```js
// 在new Vue之前添加下面代码
Vue.config.devtools = process.env.NODE_ENV === 'development'

// 在new Vue之后添加下面代码
window.__VUE_DEVTOOLS_GLOBAL_HOOK__.Vue = app.constructor

// 在Vuex ./store.js 的入口文件中添加下面代码
Vue.config.devtools = process.env.NODE_ENV === 'development'
```

再打开扩展设置里面的允许访问文件网址：

![image-20210328142456221](/images/Vue/提升写代码效率：快速通过DOM元素打开对应的Vue文件/image-20210328142456221.png)

最后再到项目页面多刷新几次并且多尝试打开几次控制台，应该就可以看见Vue菜单了。

# 2. 使用

使用的方法非常简单，跟平时我们通过Chrome开发者工具选择DOM元素一样，`Vue.js devtools`也有选择元素的方式：

![select-dom](/images/Vue/提升写代码效率：快速通过DOM元素打开对应的Vue文件/select-dom.gif)

可以看到，通过`Vue.js devtools`选中的元素会直接指向该元素所在的组件的名字：`<HelloWorld />`，然后在项目中寻找对于的模块就可以了。

# 3. 直接跳转到对应文件

虽然上面那种方法可以直接找到组件名，但是当项目一大，组件很多的时候通过这种方式也不太好找到对应的文件，所以`Vue.js devtools`还提供了一个功能：

当选中组件时，有一个`Open in editor`选项

![image-20210328143253928](/images/Vue/提升写代码效率：快速通过DOM元素打开对应的Vue文件/image-20210328143253928.png)

点击它可以直接在编辑器中打开对应的文件。

但是！使用`vue init webpack xxx`这种方式创建的项目可能点击后没有反应，仅仅是在控制台中输出一段话，所以还需要安装：

```shell
npm install launch-editor-middleware -d
```

然后编辑webpack.dev.conf.js文件：

```javascript
// 导入launch-editor-middleware包
const openInEditor = require('launch-editor-middleware')

devServer: {
  ....
  before (app) {
    /**
    * 这里打开的是Visual Studio Code 
    * code是在系统中配置的环境变量名称，必须要配置好编辑器的环境变量才有用
    * 注意 webstorm 一般要填写 webstorm64
    */
    app.use('/__open-in-editor', openInEditor('code'))
  }
}
```

重启项目，你就可以愉快的编写代码了。

# 4. 最后

非常不推荐使用`vue init webpack xxx`这种方式创建项目，因为你使用这种方式创建的项目webpack配置不会随着官方发布新包而升级，如果是很久之前的老项目使用这种方法创建的，那一些便利的功能只有手动配置了。

当然`Vue.js devtools`的功能还不止这些，它还可以监控`vuex`的状态变化，查看组件属性等等功能，总之是一个开发Vue项目的利器！

提高写代码效率你才有更多的时间划水，你有更多时间划水你才能去学习更多新的东西，你学习了新的东西你才能更好的跳槽涨工资！

