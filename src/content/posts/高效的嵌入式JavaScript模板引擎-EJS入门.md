---
title: "高效的嵌入式JavaScript模板引擎-EJS入门"
date: 2020-7-9 20:09:35
categories:
  - "JavaScript"
tags:
  - "JavaScript"
  - "模板引擎"
---


# 1. EJS是什么？

> “E” 代表什么？可以表示 “可嵌入（Embedded）”，也可以是“高效（Effective）”、“优雅（Elegant）”或者是“简单（Easy）”。EJS 是一套简单的模板语言，帮你利用普通的 JavaScript 代码生成 HTML 页面。EJS 没有如何组织内容的教条；也没有再造一套迭代和控制流语法；有的只是普通的 JavaScript 代码而已。

因为最近我使用的`Hexo`博客框架使用到了`EJS`，所以如果想要实现一些自定义的功能，就需要了解一下`EJS`。

`Hexo`的文章在这里：[一个简单易用的制作博客的框架：Hexo](/2020/05/19/一个简单易用的制作博客的框架hexo/)

但是最近我发现还有一个使用`Vue`驱动的静态网站生成器，叫做`VuePress`，特色是可以在`Markdown`中使用`Vue`组件，又可以使用`Vue`来开发自定义主题，考虑什么时候研究一下。

# 2. 安装

## 2.1 npm安装

利用 NPM 安装 EJS 很简单。

```powershell
npm install ejs
```

## 2.2 直接引入

从这里下载 [最新的浏览器版本](https://github.com/mde/ejs/releases/latest)，然后引入页面即可。

```html
<script src="ejs.js"></script>
```

# 3. 使用

因为是学习的原因，我并没有考虑过用`EJS`搭建项目，所以我这里就选择**直接引入**。`html`文件样式如下：

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>测试ejs</title>
</head>
<body>
<!-- ejs渲染的容器 -->
<div id="app"></div>

<!-- 引入ejs -->
<script src="./ejs.min.js"></script>
<script>
  /* 书写ejs */
  let people = ['geddy', 'neil', 'alex'],
    html = ejs.render('<%= people.join(", "); %>', {people: people});
  /* 将写好的ejs进行渲染 */
  document.getElementById('app').innerHTML = html;
</script>
</body>
</html>
```

跟`Vue`、`React`一样，需要一个容器，好让`EJS`渲染到`html`上面。

## 3.1 express

后面打脸的事情发生了，因为我发现如果是在浏览器上使用`EJS`，`ejs.renderFile`和`include`无法正常工作。

所以我们来搭建一个简单的基于`express`的后端。

首先我们创建一个`package.json`文件，创建方法为通过`CMD`运行：

```powershell
npm init
```

然后一路回车，就可以看到文件夹中多出了一个文件`package.json`。

安装`ejs`和`express`。

```powershell
npm install ejs express nodemon -D
```

然后在`package.json`同级目录下创建`index.js`。

直接引入的代码可以改成下面的这个样子：

```js
const express = require('express');
const ejs = require('ejs');
const app = express();

/* 路由 */
app.get('/', (req, res) => {
  let people = ['geddy', 'neil', 'alex'],
    html = ejs.render('<%= people.join(", "); %>', {people: people});
  res.send(html);
});

/* 监听端口 */
app.listen(8080, function () {
  console.log('开启服务成功！');
});
```

最后使用`nodemon index.js`运行。

那么在浏览器上面输入`localhost:8080`就可以看到渲染后的界面了。

# 4. 方法

下面的代码都在`express`搭建的后端环境上运行。

## 4.1 template

编译字符串得到模板函数。

```js
let template = ejs.compile(str, options);
template(data); // => 输出渲染后的 HTML 字符串
```

- `str`：需要解析的字符串模板
- `data`：数据
- `option`：配置选项

例子：

```js
/* 书写ejs */
let html = ejs.compile('<%=123 %>')();
/* 将写好的ejs进行渲染 */
res.send(html);
```

## 4.2 render

直接渲染字符串并生成`HTML`

```js
ejs.render(str, data, options); // => 输出渲染后的 HTML 字符串
```

- `str`：需要解析的字符串模板
- `data`：数据
- `option`：配置选项

例子：

```js
/* 书写ejs */
let people = ['geddy', 'neil', 'alex'],
  html = ejs.render('<%= people.join(", "); %>', {people: people});
/* 将写好的ejs进行渲染 */
res.send(html);
```

## 4.3 renderFile

解析文件生成`HTML`

```js
ejs.renderFile(filename, data, options, function(err, str){
    // str => 输出渲染后的 HTML 字符串
});
```

- `str`：需要解析的字符串模板
- `data`：数据
- `option`：配置选项

例子：

```js
let people = ['geddy', 'neil', 'alex'],
  html = ejs.renderFile('./test.ejs', (err, str) => {
    res.send(str);
  });
```

## 4.4 参数

上面3个方法中的`options`可以选择的参数如下：

- `cache` 缓存编译后的函数，需要指定 `filename`。
- `filename` 被 `cache` 参数用做键值，同时也用于 include 语句。
- `context` 函数执行时的上下文环境。
- `compileDebug` 当值为 `false` 时不编译调试语句。
- `client` 返回独立的编译后的函数。
- `delimiter` 放在角括号中的字符，用于标记标签的开与闭。
- `debug` 将生成的函数体输出。
- `_with` 是否使用 `with() {}` 结构。如果值为 `false`，所有局部数据将存储在 `locals` 对象上。
- `localsName` 如果不使用 `with` ，localsName 将作为存储局部变量的对象的名称。默认名称是 `locals`。
- `rmWhitespace` 删除所有可安全删除的空白字符，包括开始与结尾处的空格。对于所有标签来说，它提供了一个更安全版本的 `-%>` 标签（在一行的中间并不会剔除标签后面的换行符)。
- `escape` 为 `<%=` 结构设置对应的转义（escape）函数。它被用于输出结果以及在生成的客户端函数中通过 `.toString()` 输出。(默认转义 `XML`)。
- `outputFunctionName` 设置为代表函数名的字符串（例如 `'echo'` 或 `'print'`）时，将输出脚本标签之间应该输出的内容。
- `async` 当值为 `true` 时，EJS 将使用异步函数进行渲染。（依赖于 JS 运行环境对 `async/await` 是否支持）。

# 5. 标签含义

- `<%` '脚本' 标签，用于流程控制，无输出。
- `<%_` 删除其前面的空格符
- `<%=` 输出数据到模板（输出是转义 HTML 标签）
- `<%-` 输出非转义的数据到模板
- `<%#` 注释标签，不执行、不输出内容
- `<%%` 输出字符串 '<%'
- `%>` 一般结束标签
- `-%>` 删除紧随其后的换行符
- `_%>` 将结束标签后面的空格符删除

# 6. 引入其它文件

通过 `include` 指令将相对于模板路径中的模板片段包含进来。(需要提供 '`filename`' 参数。) 

因为该项需要使用到`fs`所以只有在`Node`环境中才能生效，也就是说需要搭建一个`Node`后端服务器。

```js
let people = ['geddy', 'neil', 'alex'],
  html = ejs.render(`<%- include('test.ejs') %>`, {filename: 'test.ejs'});
```

# 7. 自定义分隔符

可针对单个模板或全局使用自定义分隔符：

```js
let ejs = require('ejs'),
    users = ['geddy', 'neil', 'alex'];

// 单个模板文件
ejs.render('<?= users.join(" | "); ?>', {users: users},
    {delimiter: '?'});
// => 'geddy | neil | alex'

// 全局
ejs.delimiter = '$';
ejs.render('<$= users.join(" | "); $>', {users: users});
// => 'geddy | neil | alex'
```

# 8. 缓存

EJS 附带了一个基本的进程内缓存，用于缓在渲染模板过程中所生成的临时 JavaScript 函数。 通过 Node 的 `lru-cache` 库可以很容易地加入 LRU 缓存：

```javascript
let ejs = require('ejs'),
    LRU = require('lru-cache');
ejs.cache = LRU(100); // 具有 100 条内容限制的 LRU 缓存
```

如果要清除 EJS 缓存，调用 `ejs.clearCache` 即可。如果你正在使用的是 LRU 缓存并且需要设置不同的限额，则只需要将 `ejs.cache` 重置为 一个新的 LRU 实例即可。

# 9. 自定义文件加载器

默认的文件加载器是 `fs.readFileSync`，如果你想要的自定义它, 设置`ejs.fileLoader` 即可。

```javascript
let ejs = require('ejs');
let myFileLoader = function (filePath) {
  return 'myFileLoader: ' + fs.readFileSync(filePath);
};

ejs.fileLoader = myFileLoad;
```

使用此功能，您可以在读取模板之前对其进行预处理。

# 10. 布局（Layouts）

EJS 并未对块（blocks）提供专门的支持，但是可以通过 包含页眉和页脚来实现布局，如下所示：

```javascript
<%- include('header'); -%>
<h1>
  Title
</h1>
<p>
  My page
</p>
<%- include('footer'); -%>
```

# 11. 总结

总的来说这篇文章几乎就是参考了官方的文档，只是添加了一些使用方法，官方文档上某些用法讲的不是太明确。

`EJS`上手还是非常简单的，但是有些高级用法就比较难了，因为`EJS`对于我来说不算很实用，所以就不过多的进行研究了，如果有用到，再研究也不迟。

在`Nodejs`搭建的后端中可能会用到`EJS`，但是前端项目一般不会使用`EJS`。

`EJS`最方便的地方就是在于将项目给别人使用的时候，人家不用过多的去了解你的代码，直接修改配置文件就可以达到自己想要的效果。比如说`Hexo`中的配置都集中在`_config.yml`这个文件中，你根本不需要去一行一行的浏览源代码，就可以实现修改，达到你想要的效果。

# 12. 参考资料

[EJS官方中文文档](https://ejs.bootcss.com/)
