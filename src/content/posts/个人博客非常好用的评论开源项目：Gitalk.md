---
title: "个人博客非常好用的评论开源项目：Gitalk"
date: 2020-05-17
categories:
  - "blog"
tags:
  - "个人博客"
  - "开源项目"
  - "独立博客"
---


我的个人博客想要开通一个留言或者评论功能。因为我对后端并不是太了解，所以如果让我用Java开发一个留言板的功能，也许能够做到，不过就是太花时间，而且可能会有很多BUG，于是最近我发现了一个简单好用，并且拥有着高颜值的现成的开源项目Gitalk。

# 地址

[gitalk/gitalkgithub](https://github.com/gitalk/gitalk)

# 特性

- 使用 GitHub 登录
- 支持多语言 [en, zh-CN, zh-TW, es-ES, fr, ru]
- 支持个人或组织
- 无干扰模式（设置 distractionFreeMode 为 true 开启）
- 快捷键提交评论 （cmd|ctrl + enter）

其实至于如何使用Gitalk文档上面已经有了很详细的说明，然而我根据官网的文档集成Gitalk的过程并不是太顺利，所以这篇文章就描诉一下我遇到的问题，以及最后是如何解决的。

# 安装

两种方式

- 直接引入

```html
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.css">
<script src="https://cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.min.js"></script>

<!-- or -->

<link rel="stylesheet" href="https://unpkg.com/gitalk/dist/gitalk.css">
<script src="https://unpkg.com/gitalk/dist/gitalk.min.js"></script>
```

- npm 安装

```ps1con
npm i --save gitalk
```



```js
import 'gitalk/dist/gitalk.css'
import Gitalk from 'gitalk'
```

# 使用

首先，您需要选择一个公共GitHub存储库（已存在或创建一个新的github存储库）用于存储评论。

然后需要创建 **GitHub Application**，如果没有可以点击下面的网址进行注册：

https://github.com/settings/applications/new

![img](/images/v2-1573c22526a5460dad8c09ba62ebdfcd_720w.jpg)

`Authorization callback URL` 填写当前使用插件页面的域名。

注册成功后会跳转到一个界面，记录下下图中所示的两项：

![img](/images/v2-360a2a23d70528d9b90d583dc630f634_720w.jpg)

如果说手快关闭了界面，可以点击右上角的头像`Settings-Developer settings-OAuth Apps` 就能找到刚才创建的应用。

最后, 您可以选择如下的其中一种方式应用到页面：

## 方式1

添加一个容器：

```text
<div id="gitalk-container"></div>
```

用下面的 Javascript 代码来生成 Gitalk插件：

```js
var gitalk = new Gitalk({
  clientID: '刚才记录的client ID',
  clientSecret: '刚才记录的client Secret',
  repo: '博客域名',
  owner: '管理者的github账号',
  admin: '管理者的github账号',
  id: location.pathname,      // 如果要每篇文章都使用独立评论 需要改为id: location.hash
                              // 或者id: md5(location.hash)注意md5包需要单独引入
  distractionFreeMode: false  // 无干扰模式
})

gitalk.render('gitalk-container')
```

## 方式2：在React使用

使用以下代码引入Gitalk组件

```text
import GitalkComponent from "gitalk/dist/gitalk-component";
```

按以下方式在React中使用Gitalk组件

```xml
<GitalkComponent options={{
  clientID: "...",
  // ...
  // 设置项
}} />
```

方式3：在Vue中使用

我的项目是Vue搭建的，可是官方并没有告诉我们在Vue中的使用方法

不过还是可以用方法1的方式自己封装出一个组件

```js
<div id="gitalk-container"></div>

mounted() {
  const gitalk = new Gitalk({
    clientID: '刚才记录的client ID',
    clientSecret: '刚才记录的client Secret',
    repo: '主页域名',
    owner: '管理者的github账号',
    admin: '管理者的github账号',
    id: location.pathname,      // 如果要每篇文章都使用独立评论 需要改为 location.hash
                                // 或者md5(location.hash)注意md5包需要单独引入
    distractionFreeMode: false  // 无干扰模式
  });
  gitalk.render("gitalk-container");
}
```

# 测试

项目没有部署上线在本地测试时，点击登陆会直接跳到主页，并且会报错，经过几番折腾，我发现必须部署上线后才能使用GitHub登陆。

# repo填写错误

![img](/images/v2-278e409865b9e53524e26e966521f64e_720w.jpg)

上图为我的repo填写错误而报错：`Error:Not Found.`

后来经过测试，我改成了我的`GitHub pages`域名后，测试正常。

# 初始化

![img](/images/v2-12cd22a815ef4fdf65e26cffb2dbe52c_720w.jpg)

如果每篇文章使用了不同的评论页，就需要登陆设置的管理员GitHub账号，每篇文章都要点击一下进行初始化。如果觉得手动一篇一篇的点击初始化太麻烦，可以看看网络上已经有的自动初始化脚本的文章。
