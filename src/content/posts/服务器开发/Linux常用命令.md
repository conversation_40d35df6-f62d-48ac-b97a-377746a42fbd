---
title: "Linux常用命令"
date: 2020-9-25 22:43:03
categories:
  - "服务器开发"
tags:
  - "linux"
---


对于普通的电脑用户，接触的最多的是Windows，对Linux的了解甚少，而作为一个开发者，Linux是一个不得不接触的东西。

相比于其它的操作系统，Linux的占用内存更低，现在的VPS的内存越高，价格几乎也是成倍增长，一个Windows系统，占用的内存远远高于Linux。

同时Linux开源，如果有什么漏洞，会有各路大神帮忙修复，所以Linux往往更加安全。

同时Linux的稳定性也高于其它系统，一个Linux服务器，可以几十天不重启都不会宕机。

Linux一般用于服务器，对于家用电脑来说，还是推荐学习成本更低的Windows，对于开发者而言，Mac也是一个非常优秀的选择。

本篇文章用来收集Linux中的常用命令。

# 基础指令

```bash
shutdown -h now    # 表示立即关机

shutdown -h 1      # 表示 1 分钟后关机

shutdown -r now    # 立即重启

halt               # 关机

reboot             # 重启

sync               # 把内存的数据同步到磁盘，当我们关机或者重启时，都应该先执行以下 sync 指令，把内存的数据写入磁盘，防止数据丢失。

man [命令或配置文件]  # 获得帮助信息

help 命令           # 获得 shell 内置命令的帮助信息
```

## 文件目录

```bash
pwd                # 显示当前工作目录的绝对路径)

ls                 # 显示当前文件夹所有文件和目录

ll                 # 显示当前文件夹所有文件和目录

ll -a              # 显示当前文件夹所有文件和目录，包括隐藏文件

cd	[参数]          # 切换到指定目录

cd ~               # 回到自己家目录

cd ..              # 回到上一级目录

mkdir              # 创建文件目录

mkdir -p           # 创建多级目录 

rmdir              # 指令删除空目录

rmdir              # 删除的是空目录，如果目录下有内容时无法删除的。

rm -rf             # 如果需要删除非空目录

touch              # 文件名称 创建空文件

cp                 # 复制文件

cp -r              # 递归复制整个文件夹

rm                 # 指令移除【删除】文件或目录

rm -r              # 递归删除整个文件夹

rm -f              # 强制删除不提示

mv                 # 移动文件与目录或重命名
```

## 浏览文件

```bash
cat                # 查看文件内容，是以只读的方式打开。

cat -n             # 显示行号

cat                # 只能浏览文件，而不能修改文件，为了浏览方便，一般会带上管道命令 | more

cat  文件名 | more  # [分页浏览]

more               # 指令是一个基于 VI 编辑器的文本过滤器，它以全屏幕的方式按页显示文本文件的内容。more 指令中内置了若干快捷键。

less               # 指令用来分屏查看文件内容，它的功能与 more 指令类似，但是比 more 指令更加强大，支持各种显示终端。less 指令在显示文件内容时，并不是一次将整个文件加载之后才显示，而是根据显示需要加载内容，对于显示大型文件具有较高的效率。

echo               # 输出内容到控制台。

head               # 用于显示文件的开头部分内容，默认情况下 head 指令显示文件的前 10 行内容

head -n 5 文件	  # (功能描述：查看文件头 5 行内容，5 可以是任意行数)

tail               # 用于输出文件中尾部的内容，默认情况下 tail 指令显示文件的后 10 行内容

tail -n 5  5 文件   # 查看文件尾部 5 行内容，5 可以是任意行数

tail -f            # 实时监控文件有没有变化，如果有变化就会看见

ln -s \[原文件或目录][软链接名]   # （功能描述：给原文件创建一个软链接）<br>当我们使用 pwd 指令查看目录时，仍然看到的是软链接所在目录。

history            # 查看已经执行过历史命令

history 数字        # 查看已经执行过n个历史命令
```

## 文件写入

```bash
>                   # 会将原来的文件的内容覆盖

>>                  # 追加：不会覆盖原来文件的内容，而是追加到文件的尾部。
```

## 时间日期

```bash
date               # 指令-显示当前日期

date               # 显示当前时间

date +%Y           # 显示当前年份

date +%m           # 显示当前月份

date +%d           # 显示当前是哪一天

date "+%Y-%m-%d %H:%M:%S"  # 显示年月日时分秒

date -s            # 字符串时间 设置系统时间

cal [选项]	      # 不加选项，显示本月日历
```

## 搜索查找

```bash
find               # 指令将从指定目录向下递归地遍历其各个子目录，将满足条件的文件或者目录显示在终端。

find [搜索范围]	[选项]

locate             # 指令可以快速定位文件路径。locate 指令利用事先建立的系统中所有文件名称及路径的locate 数据库实现快速定位给定的文件。Locate 指令无需遍历整个文件系统，查询速度较快。为了保证查询结果的准确度，管理员必须定期更新 locate 时刻,由于 locate 指令基于数据库进行查询，所以第一次运行前，必须使用 updatedb 指令创建 locate 数据库。

grep 指令和 管道符号 |

grep [选项] 查找内容 源文件 # 过滤查找，管道符，“|”，表示将前一个命令的处理结果输出传递给后面的命令处理。
     -n       # 显示匹配行及行号
     -i       # 忽略字母大小写
```

## 压缩和解压

```bash
gzip 文件	          # 压缩文件，只能将文件压缩为*.gz 文件
gunzip 文件.gz	  # 解压缩文件命令
使用 gzip  对文件进行压缩后，不会保留原来的文件。`

zip                # 用于压缩文件， unzip 用于解压的，这个在项目打包发布中很有用的

zip [选项] XXX.zip  # 将要压缩的内容（功能描述：压缩文件和目录的命令）
     -r            # 递归压缩，即压缩目录
     
unzip	[选项] XXX.zip  # 解压缩文件
  -d<目录>          # 指定解压后文件的存放目录

tar [选项] XXX.tar.gz	打包的内容	# 打包目录，压缩后的文件格式.tar.gz
    -c       # 产生.tar打包文件
    -v       # 显示详细信息
    -f       # 指定压缩后的文件名
    -z       # 打包同时压维
    -x      #  解包.tar文件
```

# 用户管理

## 添加用户

```bash
useradd	[选项] 用户名
# - 当创建用户成功后，会自动的创建和用户同名的家目录
# - 也可以通过useradd-d指定目录新的用户名，给新创建的用户指定家目录

passwd 用户       # 指定或者修改密码

userdel XXX      # 删除用户  不删除家目录

userdel -r XXX   # 删除用户 删除家目录
# 在删除用户时，我们一般不会将家目录删除。

id 用户名         # 查询用户

su - 指令         # 切换用户
# - 从权限高的用户切换到权限低的用户，不需要输入密码，反之需要。
# - 当需要返回到原来用户时，使用 exit 指令
```

## 用户组

```bash
groupadd 组名

groupdel               # 删除组

useradd	-g 用户组 用户名 # 增加用户时直接加上组

usermod	-g 用户组 用户名 # 修改用户组

/etc/passwd 文件       # 用户（user）的配置文件，记录用户的各种信息
```

## 文件目录

```bash
ls	-ahl              # 查看文件的所有者

chown 用户名 文件名     # 修改文件所有者

chgrp 组名 文件名       # 修改文件所在的组

# 通过 chmod 指令，可以修改文件或者目录的权限
# u:所有者 g:所有组	o:其他人 a:所有人(u、g、o 的总和)
   chmod u=rwx,g=rx,o=x	文件目录名
   chmod o+w	文件目录名
   chmod a-x	文件目录名

chown newowner file   # 改变文件的所有者

chown newowner:newgroup	file	# 改变用户的所有者和所有组
     -R	               # 如果是目录 则使其下所有子文件或目录递归生效
     
chgrp newgroup file	   # 改变文件的所有组
```

## 任务调度

```bash
crontab [选项]  
    -e        # 编辑crontab定时任务
    -l        # 查询crontab任务
    -r        # 删除当前用户所有的crontab任务

conrtab –r    # 终止任务调度。

crontab –l    # 列出当前有那些任务调度

service crond restart  # 重启任务调度
```

# 硬盘分区

```bash
df -h    # 查询系统整体磁盘使用情况

du -h /目录   # 查询指定目录的磁盘占用情况，默认为当前目录
   -s        # 指定目录占用大小汇总
   -h        # 带计量单位
   -a        # 含文件

--max-depth=1	# 子目录深度

-c           # 列出明细的同时，增加汇总值

ps –aux|grep xxx   # 看看有没有 sshd 服务

- VSZ         # 进程占用的虚拟内存大小（单位：KB）
- RSS         # 进程占用的物理内存大小（单位：KB）
- TT          # 终端名称,缩写 .
- STAT        # 进程状态，其中 S-睡眠，s-表示该进程是会话的先导进程，N-表示进程拥有比普通优先级更低的优先级，R-正在运行，D-短期等待，Z-僵死进程，T-被跟踪或者被停止等等
- STARTED     # 进程的启动时间
- TIME：CPU   # 时间，即进程使用 CPU 的总时间
- COMMAND     # 启动进程所用的命令和参数，如果过长会被截断显示
```

