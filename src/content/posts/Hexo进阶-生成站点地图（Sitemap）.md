---
title: "Hexo进阶-生成站点地图（Sitemap）"
date: 2020-06-27 17:26:43
categories:
  - "SEO"
tags:
  - "站点地图"
  - "Hexo"
  - "SEO"
---


在之前的文章中，介绍了如何通过Hexo搭建一个个人博客：

[一个简单易用的制作博客的框架：Hexo](/2020/05/19/一个简单易用的制作博客的框架hexo/)

<br/>当时我仅仅是觉得`Hexo`搭建一个博客非常的方便，但是还是不如`Vue`或者`React`的适配性强，直到后面我开始接触`SEO`，才发现`Vue`和`React`都是客户端渲染，对`SEO`非常不友好，反观`Hexo`，拥有清晰的页面结构，纯静态界面，对`SEO`那是相当的友好。

正因为这样，我用`Hexo`搭建了博客后，这几天一直在学习`Hexo`以及SEO相关的知识。

# 1. 站点地图（Sitemap）

> 站点地图描述了一个网站的架构。 它可以是一个任意形式的文档，用作网页设计的设计工具，也可以是列出网站中所有页面的一个网页，通常采用分级形式。这有助于访问者以及搜索引擎的爬虫找到网站中的页面。

站点地图为`SEO`带来的好处。 

1. 为搜索引擎爬虫提供可以浏览整个网站的链接；
2. 为搜索引擎爬虫提供一些链接，指向动态页面或者采用其他方法比较难以到达的页面；
3. 如果访问者试图访问网站所在域内并不存在的URL，那么这个访问者就会被转到“无法找到文件”的错误页面，而网站地图可以作为该页面的“准”内容。

**说白了就是让搜索引擎的爬虫，尽可能多的收录你站点上的页面，页面收录的越多，你的网站的流量就会越大。**

# 2. Hexo如何生成Sitemap

## 2.1 Google 版本

进入到根目录下，打开CMD，运行下面的命令：

```
npm install hexo-generator-sitemap --save
```

## 2.2 Baidu 版本

进入到根目录下，打开CMD，运行下面的命令：

```
npm install hexo-generator-baidu-sitemap --save
```

## 2.3 生成站点地图

安装结束后，在`_config.yml`中找到`url`，改成你自己的域名。 

```xml
# URL
## If your site is put in a subdirectory, 
set url as 'http://yoursite.com/child' and root as '/child/'
url: 改成你自己的域名
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true # Set to false to remove trailing 'index.html' from permalinks
  trailing_html: true # Set to false to remove trailing '.html' from permalinks
```

更改完成后，每次进行打包的时候，会自动在`public`文件夹下生成`sitemap.xml`和`baidusitemap.xml`分别用于Google和百度。

# 3. 查看

将页面提交到服务器后，通过域名`/sitemap.xml`或者域名`/baidusitemap.xml`可以进行访问`sitemap`。 

最后到Google或百度对应的站长工具进行提交`sitemap`就可以了。
