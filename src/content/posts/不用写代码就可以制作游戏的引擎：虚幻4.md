---
title: "不用写代码就可以制作游戏的引擎：虚幻4"
date: 2020-05-19
categories:
  - "游戏制作"
tags:
  - "虚幻引擎"
---


最近两年虚幻4引擎名声大振，由于之前使用过Unity3D和Cocos Creator这两个游戏引擎。鉴于看到网上很多人说虚幻4是非常适合不会编程的人制作游戏的一款引擎，于是就准备尝试一番。

# 下载

https://www.unrealengine.com/zh-CN/?sessionInvalidated=true

需要到虚幻4官网上面下载`Epic Games Launcher`，然后再通过这个启动器安装虚幻4引擎，安装的过程还是很顺利的，唯一不顺利的是虚幻4引擎也太大了吧，光下载包都快达到12G，而硬盘需要预留的空间接近40G。

# 配置要求

虚幻4非常吃配置，如果玩绝地求生都不太流畅的电脑，还是不要轻易尝试虚幻4引擎，这可能也是虚幻4这么多年，在国内一直被Unity和Cocos压一头的原因之一吧。

# 商店

![img](/images/v2-cbe1573fb42382c50134cbb01556e986_720w.jpg)

虚幻4提供了一个商店功能，这一点对比于Unity简直好了太多了，Unity独自一人想要制作游戏时基本很难找到整套资源。

看了一下Epic上面的资源，免费的资源还是比较少的，收费的资源占大多数，如果是一个人制作游戏玩玩的话免费的资源就够了，如果是想要做商用，可能就需要花钱进行购买资源，或者有自己的美术团队。

# 模板

![img](/images/v2-a441e743a0deaaaebbbb57b06df94321_720w.jpg)

在创建工程时，UE4会提供众多模板，几乎包含了现在的主流游戏类别，如果没有你心仪的模板，可以直接创建空白模板。

![img](/images/v2-1862aa257a14ab66107b2d9e115159bb_720w.jpg)

对于新人来说，肯定是选择创建**蓝图**项目，**初学者内容包**也建议选上，当你的项目创建之后，会给你很多基础的材质纹理和特效。

# 导入库

从商店里面购买的资源都放在了库里面。需要主动点击添加到工程，**由于当前4.25版本才更新不久，所以很多免费的资源只适配到4.24，这里推荐还是下载4.24版本的UE4。**

![img](/images/v2-7ed7dfef3f6c0ee8400ea5c612b0907b_720w.jpg)

添加文件后，在内容管理处找到他们的场景文件，双击后经过漫长的导入，第一次应用时可能等了快10分钟吧。

![img](/images/v2-df828d200b8be1d2beab3b8c9d66f8b4_720w.jpg)

导入场景完成后，点击上面的运行，就可以进入游戏界面。由于之间创建的是第一人称游戏的模板，所以自带了射击功能。

![img](/images/v2-75eaa95318b30129878e597991041856_b.webp)

# 蓝图

一般一个游戏的开发除了模型和音乐资源以及UI外，最重要的就是交互，如果没有交互，那就单单是一个静态场景，在Unity3D中需要自己编写`C#`脚本进行实现对于角色的控制，而在UE4中，它提供了一个蓝图系统，可以让制作游戏的新人学习成本大幅度下降，你可以将重心放在游戏内容和关卡设计上，而不用再去考虑怎么通过代码实现某项功能。

蓝图里几乎涵盖了游戏中所有可能用到的交互的事件，比如控制角色行走，造成伤害事件，这些如果是放在其它引擎中要自己写代码实现。

唯一的缺点就是...蓝图提供的API实在是太多了，如果真的要完全靠蓝图来做游戏，那需要比较熟悉这些API，并且根据蓝图制作出的游戏，性能上肯定比不过用C++脚本写出来的游戏。

![img](/images/v2-d64760a9a40d852cb570ed1fb4f7f5ba_b.webp)

# 总结

![img](/images/v2-2ffaa4ecaf9ced492c5467701a3095d2_720w.jpg)

整个体验下来，就我自己感觉而言，相对于Unity3D引擎，优缺点挺明显的。

优点：

- 自带商场里模型众多，独立制作游戏时不用担心翻遍全网而找不到心仪的资源。
- 蓝图系统非常强大，可以实现不编写代码，依靠蓝图就能做出一个完整的游戏。
- 官方学习文档十分丰富。

缺点：

- 非常消耗硬盘空间，安装时需要预留快40G的磁盘空间。
- 对于电脑配置要求较高，如果配置不好，可能在测试游戏时会非常卡顿。
- 自带商场里免费资源还是比较少的，大部分收费资源都不便宜。
- 国内使用UE4较少，如果遇到问题可能光百度还无法解决。
