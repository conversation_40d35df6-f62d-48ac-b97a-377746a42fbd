---
title: "VPS的各种线路到底有什么区别"
date: 2020-7-21 17:36:40
categories:
  - "VPS"
tags:
  - "虚拟服务器"
---


学习前端时总会有想将自己制作的网页向别人展示的时候，方法有很多种，但是大致就分为以下两种。

一种是通过`GitHub Pages`向别人展示，参考文章：[部署到Github-Pages上的博客，自定义域名，和免费域名如何申请](/2020/05/04/部署到github-pages上的博客自定义域名和免费域名如何申请/)。

一种就是通过VPS进行部署自己的网页：[学习前端如何将自己的页面部署到服务器](/2020/07/03/web开发/学习前端如何将自己的页面部署到服务器/)。

那么如何挑选VPS呢？

**注：本篇文章是根据网络信息以及个人理解整合而来。不一定完全正确。**

# 1. VPS是什么？

> 虚拟专用服务器（英语：Virtual private server，缩写为VPS），是将一台服务器分割成多个虚拟专用服务器的服务。实现VPS的技术分为容器技术和虚拟机技术。在容器或虚拟机中，每个VPS都可分配独立公网IP地址、独立操作系统、实现不同VPS间磁盘空间、内存、CPU资源、进程和系统配置的隔离，为用户和应用程序模拟出“独占”使用计算资源的体验。

影响一个网站的访问速度，最重要的就是访问VPS的速度，其次才是网站的优化。

现在的VPS一般分为两类，一类是国内的VPS，例如：腾讯云，阿里云，京东云等。一类是国外的VPS厂商，例如：搬瓦工，vultr，Linode等、

# 2. 国内的VPS

## 2.1 带宽

下面以阿里云的虚拟服务器为例：

![image-20200721190432941](/images/image-20200721190432941.png)

一般可以根据自己的需求进行选择VPS，也可以选择比较便宜的作为学习用的VPS，作为建站的VPS来讲，一般会关注VPS的内存和带宽，比如图上的1M的带宽的VPS，则在实际使用中，传输的速度也仅仅只有130KB/s左右，对于国内的VPS来讲，大带宽的往往非常贵。

腾讯云100M带宽：

![image-20200721190648293](/images/image-20200721190648293.png)

上图为腾讯云100M带宽的VPS，价格已经高达7737元每月。

## 2.2 备案

国内的VPS如果要进行域名解析你还需要进行备案，即你有一个域名，比如`xxx.baidu.com`，如果你想要通过这个域名访问你的网站，你就需要进行备案，即向相关部门提交你的个人资料，以及说明你的建站用途，然后由他们进行审核，这个审核时间一般在2周左右。

不过对于阿里云来说，你备案花了多少时间，他会在你备案成功后返还你备案所用的时间。

# 3. 国外的VPS

一个国外的VPS，最重要的就是它的去程和回程走的线路。

因为出口线路的承载量是有限的，如果很多人同时访问国外的网站，而出口线路的承载量已经固定，这个时候就会出现卡顿、丢包高。就比如在举行一个大型活动的时候，有上万人一起使用网络，这个时候可能会出现手机有信号，网络也有信号，但是就是打不开APP或者打开速度非常缓慢。

## 3.1 中国电信

### 3.1.1 163骨干网

163骨干网（ChinaNet）又可以称为CN1，中国电信的出口线路之一，承担了 85% 的网络流量，如果在晚高峰访问自己搭建的网站发现速度慢，丢包高，那么很大的原因是走的这条线路，因为使用的人太多。

在高峰时段，路由出海前的最后一跳，根据优先级，策略性地人为丢包，以减轻对主网的负担(QOS)，这让普通电信用户糟糕的外网访问质量雪上加霜。

#### 特点

1. 一般会经过**市级 → 省级 → 国际出口 → 境外接入点 AS（自治路由协议） 号为 4134 的路由节点，这些路由节点的 IP 地址开头一律是 “202.97.* . *”，全程不会经过 CN2 网络节点。**
2. 163 网络国内之间互相访问基本上不存在性能瓶颈，只有在国际出口才会发生拥堵。
3. 在国际访问网络的质量统计上，163 网络全天的丢包率在5% ~ 10%左右，如果在夜晚间高峰期（UTC+8 18:00 至 23:00 时），丢包率可达到15%以上。
4. 在晚高峰或者某些场景下，会出现绕路的情况，比如访问新加坡的VPS，可能是中国 → 美国 → 新加坡的访问形式。

### 3.1.2 CN2 GT线路

CN2 GT（Global Transfer 又称半程 CN2 ）。

#### 特点

1. CN2 GT 产品在**从市级 → 省级 → 国际出口这一段走的是 163 网络，国际出口 → 境外接入点这一段汇入 CN2 网络，返程同理，偶尔可能会走联通的国际网络。**
2. CN2 GT 网络的数据包即使在国际间传送几乎不会出现丢包的情况，但依然非常容易在国内这一段拥堵时，出现被中国电信舍弃数据包的情况，CN2 GT 网络全天的丢包率在 4% ~ 6%左右，如果在夜晚间高峰期（UTC+8 18:00 至 23:00 时），丢包率可达到 8% 以上。
3. 但是某些地区在晚高峰期间访问可能会获得不差于CN2 GIA的体验，主要还是根据自身网络环境而定。

下面是一个CN2 GT线路的路由跟踪，可以看到在广州出口的时，才走上了59.43开头的线路，在到达广州之前一直走的骨干网。

![image-20200721202944112](/images/image-20200721202944112.png)

### 3.1.3 CN2 GIA线路

CN2 GIA（Global Internet Access 又称纯 CN2/全程 CN2），目前最好线路！**如果经济实力能够承担，强烈推荐选择这个线路的VPS。**

#### 特点

1. **从市级 → 省级 → 国际出口 → 境外接入点的过程中 全程走AS号为 4809 的路由节点，这些路由节点的 IP 地址开头一律是 “59.43. 几.几”，全程不会经过 163 网络节点。**
2. 丢包率在0.1%以下。
3. 如果是3网CN2 GIA线路则中国电信/中国联通/中国移动三网用户的去回访问，均会在省级并入到 CN2 网络，**适用性最强**。
4. 因为网络流量小，所以很容易被DDoS攻击。
5. CDIA =CN2 三网双程GIA。 DIA=单网双程CN2 GIA。

#### 测试

下面是一个CN2 GIA线路的路由跟踪，可以看到在西安的时候，就已经走上了59.43开头的线路。

![image-20200721203305977](/images/image-20200721203305977.png)

## 3.2 中国联通

- 跟中国电信一样出海或归来必定会通过北京/上海/广州。
- 出/回走 AS4837（联通 169 网络）/AS9929（A网）路由。
- 晚高峰时期骨干网选择性丢包的力度比中国电信低很多。
- **如果经常有访问国外网站的需求，推荐选择中国联通。**

### 特点

1. 中国联通普通用户**从市级 → 省级 → 国际出口 → 境外接入点的过程中，全程走 AS 号为 4837 的路由节点，这些路由节点的 IP 地址开头一律是 “219.158.* . *” ，全程不会经过 A网（AS9929）。**
2. 经过的路由数多，延迟高于CN2 GIA。
3. 169 网络全天的丢包率在 1% ~ 3%左右，如果在夜晚间高峰期（UTC+8 18:00 至 23:00 时），丢包率可达到 4% 以上。

## 3.3 中国移动

- 以前的铁通合并而来，在早些年的网络访问质量奇差，通过手机用户赠宽带等方式，中国移动迅速积累了一大批固网宽带用户。
- 中国移动进出国际网络，在国内经过的绝大部分流量，均由 AS9808 网络承载，旧铁通的 AS9314 网络几乎已被放弃
- 广州移动承担了大部分中国移动网络进出口的流量，如中美、中国东南亚等地区。
- 上海移动仅提供分散广州移动出口流量的职能，且流经上海移动的流量，会转交给国内其他运营商（如联通）进行国际通信。
- 北京移动主要承担与欧洲地区进出口流量的通信（直连，非绕美）。
- 暂未确定在高峰期是否会有策略性丢包，以减少对骨干网的负载。

### 特点

1. 中国移动的**进出口跳跃性很大，国内部分必走 AS9808 路由（221.176.* /221.183 . *），在部分地区访问部分 VPS，会并入到铁通 AS9314/电信 163 网络/联通 169 网络的路由节点。**
2. 对于位于香港的服务器，VPS 商如果和移动有合作，则全程走中国移动线路，否则走PCCW。
3. 通过 AS9808 网络访问外网全天的丢包率在 1% ~ 3%左右，如果在夜晚间高峰期（UTC+8 18:00 至 23:00 时），丢包率可达到3%以上。
4. 回程和去程的路径很少恶意绕路。

## 3.4 其它

### 香港PCCW线路

三网优化线路，体验比CN2 GIA还要好，但是价格实在是太贵了，是CN2 GIA的数倍，推荐追求极致体验的用户选择。

### 日本地区VPS

日本地区根本就没有 CN2 GIA 产品，所以**中国电信用户在任何时候都绝不应该选择任何一家日本地区的 VPS**。我尝试过Vultr上的日本地区的VPS，走的是NTT线路，线路质量奇差，丢包率非常高，白天的访问速度就非常感人。

### 韩国地区VPS

韩国地区的VPS市场竞争不充分，可以购买的本地商家不多，不值得选择。

### 总结

一个网站的好坏主要是看打开速度，没有任何用户愿意忍受几十秒的加载时间，对于VPS来说，没有任何事情是加钱解决不了的。

**同时切记：海外国人运营，支持支付宝付款，价格奇低，非一线大厂，NTT 线路，犄角旮旯地区，符合上述六条规则中的三条，打死都别碰，碰就是交学费！！！（钱的教训...）**

# 4. 总结

国内VPS与国外VPS的对比：

|         | 优点                      | 缺点                                   |
| ------- | ------------------------- | -------------------------------------- |
| 国内VPS | 访问速度快，能跑满VPS带宽 | 同价位带宽小，需要备案                 |
| 国外VPS | 同价位带宽大，不需要备案  | 访问速度有时候会非常不稳定，IP可能被墙 |

最后再说一遍：**海外国人运营，支持支付宝付款，价格奇低，非一线大厂，NTT 线路，犄角旮旯地区，符合上述六条规则中的三条，打死都别碰，碰就是交学费！！！**
