---
title: "Node.js的替代品—Deno初体验"
date: 2020-05-27
categories:
  - "JavaScript"
tags:
  - "Node.js"
  - "JavaScript"
---


# Deno是什么

它内置了 V8 引擎，用来解释 JavaScript。同时，也内置了 tsc 引擎，解释 TypeScript。它使用 Rust 语言开发，由于 Rust 原生支持 WebAssembly，所以它也能直接运行 WebAssembly，说简单点就是Node.js的一个替代品。

[Deno — A secure runtime for JavaScript and TypeScript.deno.land](https://deno.land/)

# 为什么会出现Deno

1. `Node.js`自己的模块格式`CommonJS`与`ES`模块不兼容。
2. `node_modules`和的`package.json`在项目越来越大后变得难以管理。
3. `Node.js`并不完善的功能，导致外部工具层出不穷。

由于上面这些原因，Ryan Dahl决定放弃Node.js，从头写一个替代品，彻底解决这些问题。

deno这个名字就是来自Node的字母重新组合（Node = no + de）表示"拆除 Node.js"（de = destroy, no = Node.js）。

# 特征

- 使用TypeScript或者JavaScript
- 支持ES模块
- 安全控制（权限管理）
- 全局await
- 去中心化Packages
- 内置测试
- 标准库
- 浏览器兼容的API
- Modern Js
- 执行Wasm二进制文件

# 安装

开发工具选择：VScode

![img](/images/v2-6178dcee1166f01b959813c45bb4f7dd_720w.jpg)

官网提供了很多种安装方法，这里只说一下我是怎么安装的。

使用WIN（即Ctrl右边的键）+R组合键打开运行窗口，输入powershell，回车即可。

然后在新弹出的窗口中输入下面的命令。

```powershell
iwr https://deno.land/x/install/install.ps1 -useb | iex
```

![img](/images/v2-bd054d423981c16c17ba7d68e5e45214_720w.jpg)

如果看到这个界面，就说明已经安装成功。

# 去中心化packages

用过`Node.js`后都知道，如果随着项目的增长，`package.json`文件会变得越来越臃肿，而且运行`npm install`时还会有几率出现未知的错误。

Deno为了解决这个痛点，同时还支持了ES模块，所以可以直接通过`import XXX from (URL地址) `的形式来导入包。

# 标准库模块

Deno官方提供的模块可以直接在官网上面进行查看。

https://deno.land/std

# 安全控制

**在读取脚本的时候必须使用参数，显式打开权限，不然会报错**，这一点跟Node.js完全不同，可能才入手Deno时会很不习惯。如：`deno run --allow-read index.ts`

```text
--allow-read：打开读权限，可以指定可读的目录，比如--allow-read=/temp。
--allow-write：打开写权限。
--allow-net=google.com：允许网络通信，可以指定可请求的域，比如--allow-net=google.com。
--allow-env：允许读取环境变量。
```

# TypeScript

不管你愿不愿意，TypeScript的广泛应用的确是未来前端语言的趋势，现在Vue3.0也使用了TypeScript，而且如果你拥有一种后端语言的基础，上手TypeScript是非常简单的，所以还不赶紧学一波TypeScript。

# 全局await

await不用再捆绑在async函数中，可以直接在全局进行使用。

```js
const encoder = new TextEncoder();
const greetText = encoder.encode("Hello World");
await Deno.writeFile("Hello.txt", greetText);
```

上面的代码完成了一个文件写入的操作，可以看到，就算不在async函数中，同样可以使用await。

# 创建简单服务器

根据官方的提示，我们用Deno来创建一个简单的服务器。

```js
import { serve } from "https://deno.land/std/http/server.ts";
const s = serve({ port: 8000 });
console.log("http://localhost:8000/");
for await (const req of s) {
  req.respond({ body: "Hello World\n" });
}
```

运行

```powershell
deno run --allow-net index.ts
```

记得一定要加上`--allow-net`权限，不然会直接报错。

运行成功后通过浏览器打开`localhost:8000` 可以看到`Hello World`说明我们的服务器已经打开成功。

# 总结

Deno从体验上来说非常不错，自身就支持`TypeScript`解析，而且解决了`package`这个痛点，但是现在作用于生产环境还是太早，因为它的社区还相对不成熟，肯定比不过经过了多年沉淀的`Node.js`，也许多年后会替代`Node.js`，但肯定不是现在。

# 资料参考

https://www.ruanyifeng.com/blog/2020/01/deno-intro.html


