---
title: "TypeScript-数据类型，函数的声明和重载"
date: 2020-8-11 16:01:08
categories:
  - "web开发"
tags:
  - "TypeScript"
---


# 1. 前言

## 1.1 TypeScript是什么？

TypeScript是一种开源的编程语言，该语言项目由微软进行维护和管理。TypeScript不仅包含JavaScript的语法，而且还提供了静态类型检查以及使用看起来像基于类的面向对象编程语法操作Prototype。

注意：其实TypeScript最终会编译成为JavaScript再进行运行，所以即使有时候TypeScript在编译中进行报错，但是代码依然能够正常运行。

换句话说TypeScript是JavaScript的严格超集，任何现有的JavaScript程序都是合法的TypeScript程序。

## 1.2 为什么要学习TypeScript？

因为JavaScript是一种弱类型的语言，并且在ES6之前都没有引入类（Class）的概念，而且JavaScript没有实现接口这一项重要的功能，而随着前端项目的深度和广度日益扩大，使用JavaScript做中大型项目和团队协作开发时就可能会出现很多潜在的BUG以及代码耦合性强的问题，所以这个时候TypeScript的出现就是为了解决这些问题。

到目前为止，已经有非常多的前端团队开始使用TypeScript，并且现在的前端主流框架Vue3.0已经开始使用TypeScript进行编写，所以学习TypeScript已经是一件刻不容缓的事情。

## 1.3 怎么学习TypeScript？

如果你已经有一项后端语言（C#，Java等）的基础，那么学习TypeScript是一件非常容易的事情，当然，即使你没有后端语言的基础，学习TypeScript也并非是一件困难的事情。

推荐直接查看[官方文档](https://www.typescriptlang.org/)。

# 2. 开始使用

推荐使用前端最流行的编辑器vscode，虽然我个人更喜欢使用webstorm。

## 2.1 安装

```shell
npm install -g typescript
```

TypeScript文件的后缀名为`.ts`

## 2.2 编译

```shell
tsc index.ts
```

### 2.2.1 自动编译

```shell
tsc --init
```

打开生成的`tsconfig.json`

找到`outDir`，取消注释。

![image-20200811000443934](/images/TypeScript/image-20200811000443934.png)

修改为：`"outDir": "src/js", `或者其它你喜欢的目录。

`终端` - `运行任务` -`tsc: 监视`

设置完毕后会看到下面的文件目录：

![image-20200811002358534](/images/TypeScript/image-20200811002358534.png)

附：webpack中可以使用ts-loader。

# 3. 数据类型

`TypeScript`中为了使编写的代码更规范，更有利于维护，增加了类型校验

- 布尔类型（`boolean`）
- 数字类型（`number`）
- 字符串类型（`string`）
- 数组类型（`array`）
- 元组类型（`tuple`）
- 枚举类型（`enum`）
- 任意类型（`any`）
- `null`和`undefined`
- `void`类型：没有任何类型，通常用来表示函数的返回值。
- `never`类型：表示的是那些永不存在的值的类型。

## 3.1 变量声明

变量的声明方式和JavaScript差不多，不过唯一不同的就是需要在后面加上数据类型，例如：

```typescript
let isDone: boolean = false; // boolean类型

// 数字类型 支持：二进制，八进制，十进制，十六进制
let decLiteral: number = 6;
let hexLiteral: number = 0xf00d;
let binaryLiteral: number = 0b1010;
let octalLiteral: number = 0o744;

// 字符串类型
let name: string = "bob";

// 数组类型
let list: number[] = [1, 2, 3];
let list: Array<number> = [1, 2, 3];

// 元组类型
let x: [string, number];
x = ['hello', 10]; // OK

// 枚举类型
enum Color {Red, Green, Blue}
let c: Color = Color.Green;

// void：函数无返回值
function warnUser(): void {
    console.log("This is my warning message");
}
```

## 3.2 元组 tuple

元组类型允许表示一个已知元素数量和类型的数组，各元素的类型不必相同。 比如，你可以定义一对值分别为 `string`和`number`类型的元组。

```typescript
// 声明元组
let x: [string, number];
// 初始化
x = ['hello', 10]; // 编译正确
// 错误的初始化
x = [10, 'hello']; // 报错
```

## 3.3 枚举 enum

使用枚举我们可以定义一些带名字的常量。 使用枚举可以清晰地表达意图或创建一组有区别的用例。 TypeScript支持数字的和基于字符串的枚举。

```typescript
enum Direction {
  up,
  down,
  left,
  right,
}
```

如果在不指定枚举的值的情况下，`Up`使用初始化为 `0`。 其余的成员会从 `0`开始自动增长。 换句话说， `Direction.Up`的值为 `0`， `Down`为 `1`， `Left`为 `2`， `Right`为 `3`。

### 3.3.1 为什么要使用枚举？

枚举的具体使用过程在这里就不细说了，这里就说一下为什么要使用枚举。

如果上面的枚举写成普通的形式，即为：

```typescript
let up:number = 0;
let down:number = 1;
let left:number = 2;
let right:number = 3;
```

其实通过其它的方式也可以实现类似于枚举的效果，但是使用枚举会更加方便快捷而且不容易出错，推荐能够使用枚举的地方尽量使用枚举。

## 3.4 never

`never`类型表示的是那些永不存在的值的类型。

```typescript
// 返回never的函数必须存在无法达到的终点
function error(message: string): never {
    throw new Error(message);
}

// 推断的返回值类型为never
function fail() {
    return error("Something failed");
}

// 返回never的函数必须存在无法达到的终点
function infiniteLoop(): never {
    while (true) {
    }
}
```

# 4. 函数

TypeScript和JavaScript一样，可以创建有名字的函数以及匿名函数。

## 4.1 函数的声明

跟大多数后端语言一样，TypeScript中声明函数时**需要指定函数的返回值，以及参数的类型**，例如：

```typescript
function add(x: number, y: number): number {
    return x + y;
}

let myAdd = function(x: number, y: number): number { return x + y; };
```

还可以使用箭头函数`=>`进行函数声明：

```typescript
let myAdd: (x: number, y: number) => number =
    function(x: number, y: number): number { return x + y; };
```

其实即使不声明函数的返回值和参数类型，编译后也能运行，但是TypeScript编译器会报错，不过既然使用了TypeScript，那还是需要按照它的规范来编写代码。

可以看到，TypeScript中声明函数比JavaScript声明函数需要多写很多代码，但是多些这些代码其实是有好处的，尤其是在大型的项目中，比如有`add()`这个函数，在JavaScript中可以传递任何类型的参数进去，甚至`string`类型，这可能就会导致程序出错。

而在TypeScript中就指定了只能传递`number`类型，出错的几率就会大大降低。这就是为什么近些年前端的中大型项目都使用TypeScript进行开发。

## 4.2 参数

### 4.2.1 可选参数

JavaScript里，每个参数都是可选的，可传可不传。 没传参的时候，它的值就是undefined。 在TypeScript里我们可以在参数名旁使用 `?`实现可选参数的功能。

可选参数必须跟在必须参数后面。 如果上例我们想让firstname是可选的，那么就必须调整它们的位置，把firstname放在后面。

```typescript
function myName(firstName: string, lastName?: string): void {
  console.log(firstName + lastName);
}
```

### 4.2.2 默认参数

我们在设置参数时，也可以给参数一个默认值，如果没有传参，那么该参数的值就是我们给定的默认值。

```typescript
function myName(firstName: string, lastName: string = "张三"): void {
  console.log(firstName + lastName);
}
```

与普通可选参数不同的是，**带默认值的参数不需要放在必须参数的后面**。 如果带默认值的参数出现在必须参数前面，用户必须明确的传入 `undefined`值来获得默认值。

### 4.2.3 剩余参数

如果你想要操作多个参数，或者你不知道该函数会传入多少个参数，在TypeScript里，你可以把所有参数收集到一个变量里：

```typescript
function buildName(firstName: string, ...restOfName: string[]) {
  return firstName + " " + restOfName.join(" ");
}

let employeeName = buildName("Joseph", "Samuel", "Lucas", "MacKinzie");
```

剩余参数会被当做个数不限的可选参数。 可以一个都没有，同样也可以有任意个。

## 4.3 重载

重载就是拥有很多个同名函数，但是会根据传入不同的参数而返回不同类型的数据。

例如下面的函数：

```typescript
function getInfo(name:string):string;
function getInfo(age:number):string;
function getInfo(str:any):any {
    if(typeof str === "string"){
        return "name" +str;
    }else {
        return "年龄"+str
    }
}

console.log(getInfo(false));//如果传入非上面两种数据类型会报错
```

虽然调用函数名称都是`getInfo()`但是根据传入的参数，会得到不同的结果，因为重载算是一个很重要的功能，所以如果有机会的话，会单独对重载进行一次比较深度的研究。

# 5. 总结

本篇文章只是起到抛砖引玉的作用，也是为了记录学习TypeScript的过程，所以很多细节都没有写到，如果需要系统学习，建议还是参照[TypeScript文档](https://www.tslang.cn/docs/handbook/functions.html)，或者网上的学习视频。

总之，有了JavaScript的基础，TypeScript也仅仅是在JavaScript的基础上新加了一些东西，所以学习起来也并不困难。
