---
title: "IOS系统越狱后的自用插件分享"
date: 2020-06-11
categories:
  - "IOS"
tags:
  - "越狱"
  - "IOS"
---


# 为什么要越狱？

众所周知，IOS系统的封闭性要远远高于安卓，虽然现在安卓的很多衍生系统都已经不支持Root了。

正因为IOS的封闭性，所以IOS比起安卓来讲要安全很多，不会因为在滑动APP界面时点到广告就开始下载软件（以前我在用安卓时，那个年代流量还只有不到1G/月时，点到贴吧上面的广告瞬间开始下载软件，花掉不少流量...这个故事告诉我们，4G的速度还是挺快的）。

总之，越狱是为了实现IOS不具备的某些功能，但是**越狱同时会造成安全性降低，同时越狱后微信和支付宝不支持指纹支付！**

**注意：某些插件我并不是用的作者源而是从第三方源（比如Cydia贴吧源、多米诺骨牌源）下载，大家酌情参考。**

**我的系统环境：IOS 12.4**

# 1. Aerial Rus

源地址：http://rejail.earial/

切换状态栏图标的颜色，因为切换后显得特别的花哨，所以这个插件已经被我放弃使用。

## 2. AppStore快速切换账号

源地址：http://apt.cydiaba.cn/  Cydia贴吧源

![img](/images/v2-76c2bf8c74fe3d3f881b55332eed6583_720w.png)

有时候会有多个苹果ID需要来回切换，这显得很麻烦，安装该插件后，就会在AppStore里的个人信息处多出一个切换账号的按钮，曾经你登陆过的账号都可以快速进行切换，省去输入账号密码的烦恼。 

## 3. AppStore++

源地址：http://cokepokes.github.io/

![img](/images/v2-e245957d4ff1075c5ef47eda6d357ba4_720w.png)

可以下载APP过去的版本，实用方法是按住右边的下载或者打开按钮，就会弹出来一个对话框。可以选择升级、降级、手动安装、禁止更新。

## 4. NiceCaller 电话助手

源地址：http://apt.htv123.com/

![img](/images/v2-8a856a64392b05ab0a2cc09895b279b6_720w.png)

如果你被骚扰电话所困扰，该插件是你不二的选择，说起来，我最初越狱的目的就是为了找到一款可以拦截骚扰电话的插件，结果找了很多插件都无法正常进行拦截，直到使用了该插件，该插件需要配合腾讯手机管家使用。

当对方电话被腾讯手机管家标记过后，该插件会自动拒绝他们的电话，甚至还有拒接所有陌生电话的功能。

## 5. KillX Pro Rus

源地址：http://rejail.ru/

![img](/images/v2-3c1b74b750379a404e424acf523dca8c_720w.gif)

一键清理后台程序，安装后在后台界面向下滑动或者点击X，可以关闭掉所有打开的程序，非常实用的一个插件。

## 6. DLEasy Rus

源地址：http://rejail.ru/

**越狱后的神器之一。**可以说是必装插件。可以下载YouTube视频，Instagram的图片等等。

同时还有一个附加功能：可以直接屏蔽YouTube客户端的广告，甚至还能打开YouTube的后台播放功能。当然，经济条件允许还是请购买YouTube会员。

## 7. Prysm 控制中心美化

源地址：http://apt.wxhbts.com/  多米诺骨牌源

![img](/images/v2-f2f177b3929ca43334dd2877e4231370_720w.png)

顾名思义，就是美化控制中心的插件，我个人是不太习惯捣鼓这些样式上的东西。

## 8. Liberty Lite

源地址：http://rejail.ru/

屏蔽越狱检测，因为有些软件在越狱环境下无法使用或者某些功能无法使用，比如支付宝的指纹支付，就可以使用这个软件屏蔽支付宝的越狱检测。

注意：**使用该插件可能有封号的风险，贴吧中不少人使用这个插件屏蔽微信后导致微信号被封。**

## 9. Selector Rus

源地址：http://rejail.ru/

![img](/images/v2-be2dbf6ec1bdea185711837ffa92835e_720w.png)

选中后会弹出翻译选项。

## 10. CCVPN

源地址：http://apt.thebigboss.org/repofiles/cydia/  (BigBoss)

![img](/images/v2-430f36fe097ad16abd6f868e0f7b8adf_720w.png)

在控制中心会出现一个开启或者关闭VPN的按钮，可以方便的开启或者关闭最后一次使用的VPN。

## 11. Flex 3

源地址：http://apt.thebigboss.org/repofiles/cydia/   (BigBoss)

![img](/images/v2-9402913985b9988d7db509cc76c84d2e_720w.png)

IOS越狱后的神器级插件，可以做到破解软件VIP，修改游戏数据，去除APP广告等等功能，插件内就提供了很多大家编辑上传的脚本，但是需要注意的是，**由于APP在不停的更新，所以这些脚本不一定有用。**

## 12. FLEXible

源地址：http://apt.thebigboss.org/repofiles/cydia/   (BigBoss)

![img](/images/v2-4a544a9fcc438aed7c65b5f9ff1cb720_720w.png)

配合Flex 3的神器之一，可以抓取界面元素，定位到控制元素的函数，从而使用Flex 3禁止该元素的启动。

## 13. ios12状态栏（仿IOS10 For 4.7）

源地址：http://apt.wxhbts.com/

![img](/images/v2-4e1305d1258ac992a3eaa1ae61b619b2_720w.png)

将ios12状态栏的信号图标变成小圆点，虽然通过替换系统文件也可以做到，但是该插件实现了一键替换。

注意：**使用有风险！请酌情使用。**

## 14. ReProvision

源地址：http://repo.incendo.ws/

![img](/images/v2-ab3673749ab55ccee107cc405a97ef77_720w.png)

自动签名程序，作者之前宣布不再维护该项目，但是在某一天大家发现0.5.2~EOL这个版本又可以开始自签了。

## 15. RealCC

源地址：http://apt.thebigboss.org/repofiles/cydia/   (BigBoss)

![img](/images/v2-d3b0a4af60f8fd73a2a7a32068c3e383_720w.gif)

可以直接在控制中心关闭掉WiFi和蓝牙，不再像没有越狱之前仅仅是断开WiFi和蓝牙，本质上是没有关闭。
