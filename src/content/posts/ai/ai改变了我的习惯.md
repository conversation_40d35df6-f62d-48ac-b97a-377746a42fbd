---
title: "AI 如何融入并改变了我的编程与生活习惯"
date: 2025-04-20 20:55:33
categories:
  - "AI"
tags:
  - "AI"
---


从今年接触Cursor后，这两个月里面我疯狂的学习了关于各种大模型的使用，大模型能干什么，以及怎么更好的去驱动大模型，生成我想要的内容。

## 1. 我常用的大模型

目前我使用最多的模型是 Cursor 内置的 Claude 和 DeepSeek，以及 Google AI Studio 中的 Gemini 2.5 Pro。

### 1.1 为什么没有使用 ChatGPT？

因为我已经付费订阅了 Cursor，而我当前最主要的 AI 应用场景是编程辅助，所以对 ChatGPT 的需求相对较小。

### 1.2 那么为什么我会使用 Gemini？

主要原因是 Google AI Studio 提供了 Gemini 2.5 Pro 每日免费的调用额度（官方宣称 25 次，但我实际使用中几乎未遇到限制）。更重要的是，AI Studio 允许用户精细调整关键参数，例如是否启用网络搜索、AI 的创造性（temperature）等，这为特定任务提供了更高的灵活性。Gemini 2.5 Pro 本身也具备强大的能力，例如出色的推理和代码生成能力，以及处理长达 100 万 token 上下文的能力，非常适合分析复杂问题和大型代码库。

至于 Grok，我曾尝试使用，但经常遭遇次数限制，尤其是在使用深度搜索功能时。此外，我观察到 Grok 的搜索结果似乎更侧重英文信息源，对于中文内容的搜索有时与我的预期存在偏差。

### 1.3 DeepSeek V3

选择 DeepSeek V3 的主要原因是在 Cursor 中免费可用，且效果相当不错。一些公开的基准测试和对比显示，DeepSeek V3 在特定任务（如某些编程和推理问题）上表现出了与 GPT-4o 等顶级模型竞争的潜力，尤其考虑到其相对较低的训练成本，这显示了开源模型强大的追赶势头。虽然 Claude 这类模型有每月限额的快速请求（我个人从未用完额度），但对于一些相对简单的编程问题或文本任务，我更倾向于直接使用 DeepSeek V3，以保留付费模型的额度用于更复杂的场景。

## 2. 我使用大模型做了什么

### 2.1 辅助编程

编程是我使用大模型最频繁的场景。我现在将许多繁琐或需要模板化代码的工作交给 AI 完成，例如：

- 快速生成组件代码（如轮播组件）
- 清理文件中的冗余代码或进行重构
- 辅助提取可复用组件
- 快速理解新项目结构（例如，询问“项目中支付相关的代码在哪里？”）
- 定位潜在的代码问题或 Bug

对于接手新项目而言，利用 AI 快速熟悉代码库非常高效。例如，想了解支付模块，只需向 AI 提问，它通常能快速定位相关文件和关键逻辑。

### 2.2 辅助写作

今年以来，我的文章写作流程也融入了 AI。初稿完成后，我会让 AI 协助检查错别字、修正潜在的语法或事实性错误，并根据需要拓展一些信息（当然，这些信息需要我进行验证）。这显著提高了我的写作效率和内容的准确性。

### 2.3 解答疑问

除了专业领域，我还会向 AI 提出各种各样的问题，比如：

还有很多问题我会问AI，比如：

- 在电梯坠落到底部的一瞬间，如果跳起来，是否能活下来？
- 黑洞是否在宇宙中很常见？
- 人类是否永远也飞不出银河系？
- ...

AI 通常能提供有趣且信息量丰富的回答，满足我的好奇心。

### 2.4 出行规划助手

我还尝试使用 AI 制定出行计划。只需提供时间、地点、偏好的出行方式和活动兴趣，AI 便能生成一份初步的行程攻略。

然而，实际使用中我发现，特别是在涉及小众或信息更新不够及时的景点时，AI 容易出现“幻觉”（Hallucination）。例如，我询问关于成都彭州的“中坝森林公园”时，Gemini 一度将其定位到了绵阳江油市。有时 AI 甚至会推荐已经关闭的景点或虚构不存在的活动。

因此，如果计划依赖 AI 进行旅行规划，强烈建议将 AI 的建议作为初步参考，并务必通过地图服务（如高德地图、Google Maps）、官方网站或近期用户评价等多方渠道进行交叉验证。接入地图服务的 MCP（Multi-agent Collaboration Platform）或许能让 AI 基于实时 POI（Point of Interest）信息生成更可靠的攻略，减少幻觉。

## 3. 如何更好地使用 AI

每个 AI 模型实例都可以看作一个独立的“个体”，即使是同一个模型，面对相同的问题也可能给出不同的答案。因此，要让 AI 更精准地满足需求，编写高质量的 Prompt（提示词）至关重要。

### 3.1 Prompt 编写要点

以请求 AI 编写代码为例，Prompt 需要尽可能详细和清晰。不能简单地说“帮我写一个轮播组件”，而应明确：

- **技术栈**：使用什么框架或库（React, Vue, Vanilla JS 等）？
- **具体需求**：轮播的内容是什么？是否需要自动播放、指示器、导航箭头？样式要求？
- **上下文**：代码应插入到哪个文件的哪个位置？是否需要考虑现有代码风格？
- **约束条件**：代码格式、注释规范、性能要求、错误处理、可访问性等。

Prompt 越具体、约束越明确，AI 生成的代码质量通常越高。

现在 GitHub 等社区已有许多优秀的 Prompt 集合或约束词库可供参考。Cursor 0.49 版本后也内置了 `/Generate Cursor Rules` 命令，可以方便地生成一些常用的编程约束，简化了 Prompt 的编写。

## 4. AI 还能做什么？

### 4.1 AI 情感伴侣

在我了解的过程中，AI 还衍生出了一些更具争议或新奇的用途，例如作为情感伴侣。

- 怎么跟AI谈念爱？
- 跟AI谈念爱有什么用？
- 谁在跟AI谈念爱？

后面经过了解，如果你给予AI角色，那么AI会根据你给定的角色，跟你进行对话，它会给你极高的情绪价值，让你在聊天中获得快乐。

AI甚至能扮演小奶狗等不同的你需要的角色。

如果你想要尝试跟AI谈念爱，那么推荐直接使用ChatGPT，它几乎是不二之选。因为本身就有非常多的开发者根据ChatGPT的API，开发了非常多的AI角色，你只需要选择一个你喜欢的角色，然后跟AI进行对话，就可以开始你的恋爱之旅。

但是如果你**不小心删除了那个对话框**，你可能就会失去一个恋人。

### 4.2 个人顾问？谨慎参考！

理论上，高级大模型似乎可以扮演个人顾问的角色，例如提供保险、法律或投资方面的建议。面对复杂的保险条款或法律文件，用户可以将文本输入给 AI，让其分析潜在风险、解释术语或比较不同选项。

**但是，这方面存在极高的风险！** AI 的回答完全基于其训练数据，可能包含过时信息、偏见甚至是完全错误的内容。AI 并不具备真正的专业资质和法律责任能力。

**因此，AI 提供的信息*绝对只能作为初步参考，绝不能替代*合格的专业人士（如持牌律师、理财顾问、保险经纪人）的意见。** 在涉及重要决策（尤其是财务、法律、健康等方面）时，必须咨询专业人士。AI 的回答往往缺乏对个人具体情况的深入了解，可能忽略关键细节，导致建议不适用甚至有害。此外，将敏感个人信息（如财务报表、病历）输入 AI 也存在数据隐私和安全的风险。AI 公司通常也会在服务条款中明确免责声明，强调其输出不构成专业建议。

## 5. 最后

经过一段时间的探索和使用，特别是随着各大模型推理能力的不断提升，AI 的实用性日益增强。它不再局限于简单的问答，而是能够辅助处理复杂数据分析、自动化繁琐工作，甚至参与到创造性的编程任务中。

AI 已经以各种方式融入了我的学习、工作和生活习惯。拥抱这项技术的同时，保持批判性思维，了解其能力边界和潜在风险，将是我们在 AI 时代持续学习的重要一课。