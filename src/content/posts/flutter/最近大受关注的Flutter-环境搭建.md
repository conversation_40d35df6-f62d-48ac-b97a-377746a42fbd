---
title: "最近大受关注的Flutter-环境搭建"
date: 2021-5-9 13:12:35
categories:
  - "Flutter"
tags:
  - "Flutter"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

Flutter正式发布在17年，可以说是非常新的一个技术，但是由于Google投入了大量人力，以及双端高度一致的表现，使得该项技术关注度迅速超过React Native，成为现在最受关注的大前端技术之一，到目前为止Flutter已经在GitHub上面拥有**120K Start**，已经超越比它早发布两年的React Native。

本来我计划在1月份就开始学习Flutter，结果由于各种原因搁置了，最近随着Flutter 2的发布，Flutter又受到了非常大的关注，所以我决定也乘此机会学习一波。

就我个人的经验来讲，学习一个前端框架有下面几个步骤（Flutter也被归在大前端中）：

- 了解框架语法和UI组件。
- 了解组件之间通信方式。
- 了解框架中的路由用法。
- 了解如何进行网络请求。

基本上了解了上面4个知识点，你用一个框架来开发一些中小型的应用是完全没有什么问题的。

Flutter对于前端开发者来讲最不友好的就是它使用的开发语言是Dart，相信大部分前端开发者都没有使用过TypeScript进行开发项目，在我帮助公司招人的时候，从6k~14k要价的开发者，没有一个人使用过TypeScript开发过项目，一问都是了解过，但是没有用过。

而Dart最初和TypeScript是竞争关系，但是后面由于种种原因输给了TypeScript，后面就逐渐被遗忘，仅几年由于Flutter的出现，又让Dart这门语言重新进入了开发者的视线。

# 1. 为什么是Flutter

跨端技术非常多，为什么选择Flutter？

1. Flutter使双端开发的表现几乎完全一致，不用再担心IOS这边显示为这样，一切换到Android发现有一部分显示异常。
2. Flutter更加接近原生的性能。
3. Flutter搭建开发环境更加轻松，开发者可以很轻易的在VSCode或Android Studio上面搭建Flutter开发环境。

谷歌对于Flutter非常重视，投入了大量的研发人员，又在近期发布了Flutter 2，让Flutter可以使用相同的代码库构建五种操作系统构建原生应用: iOS、Android、Windows、macOS和Linux；以及为 Chrome、Firefox、Safari 和Edge等浏览器打造web体验。Flutter甚至可以嵌入到汽车、电视和智能家电，为环境计算提供最普适、可移植的体验。

# 2. 环境安装

比起方便的Nodejs，Flutter的环境安装稍微难一些，它需要安装**Java环境**、**Android Studio**、**Flutter SDK**这3样东西。

其中还涉及到配置环境变量，一旦哪一步没对，可能就会卡住。

在Flutter 2发布后现在可以**在浏览器上面进行调试Flutter应用**，省去了手机模拟器安装失败等一系列问题。

## 2.1 Java环境

直接到https://www.oracle.com/hk/java/technologies/javase-downloads.html这里进行下载。最好是高版本的，不过现在很多公司依然在用Java 8开发项目。

Java环境的安装还是比较简单的，直接无脑下一步就行，安装结束后打开CMD分别运行下面两个指令：

```shell
java

javac
```

如果都有输出那就说明环境安装成功。

## 2.2 Flutter SDK

安装Flutter有两种办法，一种是通过Git进行下载，第二种是直接下载指定版本的Flutter包。

这里介绍通过Git进行安装：

```shell
git clone https://github.com/flutter/flutter.git -b stable
```

从GitHub上的[Flutter repo](https://github.com/flutter/flutter)获取源代码，并根据需要，切换到指定的分支或标签。

拉取完成后再配置一下环境变量，相信作为一个开发者对于环境变量的配置应该非常熟悉：

![image-20210509121704497](/images/Flutter/最近大受关注的Flutter-环境搭建/image-20210509121704497.png)

将flutter文件夹中的`bin`目录配置到环境变量中。

由于国内网络的原因，安装好Flutter SDK后还需要设置一些环境变量，**不然在后续使用过程中可能会出现Flutter下载包卡住没有响应的情况。**

使用社区镜像：

![image-20210509121853461](/images/Flutter/最近大受关注的Flutter-环境搭建/image-20210509121853461.png)

设置下面两个环境变量：

```shell
FLUTTER_STORAGE_BASE_URL: https://storage.flutter-io.cn
PUB_HOSTED_URL: https://pub.flutter-io.cn
```

配置完毕后运行`flutter doctor`，它会自动帮你下载Dart相关的包。

## 2.3 Android Studio

Android Studio是基于IDEA进行开发的，我本身就是一个JetBrains党，所以切换到Android Studio是没有太大的不适应的，唯一不方便的地方就是不支持JetBrains的同步功能，虽然它可以在GitHub上面创建一个仓库专门用来放配置信息，但是我试了很多次，都不能记录你安装的插件，即使你进行了同步，在新的环境下依然还要一个一个的去安装你曾经安装过的插件。而JetBrains的账号同步就可以将你所有安装的插件都进行同步。

直接到[Android Studio官网](https://developer.android.com/studio)进行下载，安装过程也很简单，直接下一步下一步就好。

### 2.3.1 插件

其中装好Android Studio后，有两个必须要安装的插件，`Ctrl+Alt+S`打开设置，找到插件，安装`Flutter`、`Dart`这两个插件，

![image-20210509122808761](/images/Flutter/最近大受关注的Flutter-环境搭建/image-20210509122808761.png)

一开始打开Android Studio默认是英文，所以你可以选择安装一个中文插件：

![image-20210509122912210](/images/Flutter/最近大受关注的Flutter-环境搭建/image-20210509122912210.png)

# 3. 初识

## 3.1 创建项目

在Flutter 2时代，创建项目是非常简单的，直接在指定的文件夹中运行命令：

```shell
flutter create xxx(项目名)
```

稍等片刻，一个Flutter项目就创建好了，接着使用Android Studio打开。

## 3.2 目录结构

可以看到以下的目录信息：

![image-20210509122351546](/images/Flutter/最近大受关注的Flutter-环境搭建/image-20210509122351546.png)

我们需要关注的只有两个`lib`这个文件夹和`pubspec.yaml`这个文件，其中`lib`是放源代码的地方，而`pubspec.yaml`是项目的信息以及相关依赖文件，有点类似于`package.json`。

其它文件夹暂时不用管，这个时候我们双击点开`main.dart`，这是整个Flutter项目的**入口文件**。在Android Studio右上角可以看到下图中的菜单。

![image-20210509123043401](/images/Flutter/最近大受关注的Flutter-环境搭建/image-20210509123043401.png)

Flutter 2已经可以使用Chrome进行运行，选择自己喜欢的运行方式，点击右边的调试按钮，这个时候会弹出一个控制台，稍等片刻，它就会自动打开Chrome，就可以看到如下界面：

![image-20210509123440245](/images/Flutter/最近大受关注的Flutter-环境搭建/image-20210509123440245.png)

注：如果上面的运行步骤遇到问题，那么可以尝试关闭Android Studio，然后找到它的快捷方式，点击右键，选择**以管理员身份运行**，貌似Android Studio不使用管理员身份运行，打开调试可能会失败！

# 4. 最后

比起Web开发来说，Flutter开发环境的搭建还是比较麻烦的，Web开发直接下载一个Nodejs，然后`npm install`一下项目基本就能跑起来了，不过据说对比React Native的环境搭建是要简单非常多的。

对于Flutter，我个人觉得还是非常有必要学习的，因为市面上有非常多的小公司它们舍不得花钱或者没钱请原生开发，因为请原生开发的话IOS端和Android端分别至少要一个人，而使用Flutter一个人就可以搞定两个人的活。虽然现在小公司很多都直接使用uni-app凑合着开发原生项目。

最后需要说的是，我个人是非常不推荐将Flutter作为你找工作的主要手段，因为我在招聘网站上看了一圈，对于Flutter的需求量并不是很大，如果你不在一线城市仅仅学了一个Flutter可能并不好找工作。

我推荐的是将Flutter作为一个混合开发解决方案，比如你是一个前端开发者，如果你的老板**非要你开发一个原生APP**，你要么大胆拒绝，说：这不是我的工作范畴，你找专门的人来做吧！要么使用坑多的uni-app，要么使用Flutter、React Native，或者你直接使用原生来开发。
