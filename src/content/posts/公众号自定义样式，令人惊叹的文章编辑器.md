---
title: "公众号自定义样式，令人惊叹的文章编辑器"
date: 2020-05-09
categories:
  - "微信公众号"
tags:
  - "样式"
---


最近在弄自己的公众号，虽然关注的人就只有我自己...都是眼泪啊！

# **案例**

众所周知，一些比较大的公众号推送的文章都是花里胡哨的。尤其是一些介绍自己产品的公众号，比如说分割线样式：

![img](/images/v2-7996702b242cba19da35ef902f957c98_720w.png)

再比如说这种背景和边框：

![img](/images/v2-2ad064822e44e65f74d2a35d610c93db_720w.png)

再比如：

![img](/images/v2-5bcf323d6ebcd5bb26b1f2e9f80461e0_720w.png)

这种一眼看上去就有很强的视觉冲击的文字，而我自己的文章呢？

![img](/images/v2-48b8452c1274b5502bdae90df7c9a177_720w.png)

我的天，黑白的性冷风，突然感觉low爆了，不过我的文章主要是想以内容为主，如果太过于花哨反而会影响阅读体验。

# **实践**

为了实现上面的那些分割线效果，我还专门去搜索了下这些样式是怎么制作的，结果搜出来的结果都是讲的如何**用编辑器制作样式！然而我想知道的是编辑器具体是怎么制作样式的。**

没有办法搜索不到就只有自己动手研究了，首先用我们强大的Chrome浏览器，来到公众号文章编辑界面，随便从其他的地方复制一个样式：

![img](/images/v2-7ec49146c4134663c96fd9d87b4ddd01_720w.png)

就用它开刀好了，按F12打开开发者模式，选中这玩意。

![img](/images/v2-067e8083ba2e2ec27512c7d6769b9816_720w.png)

发现开发者工具上面有个`section`标签，等会，你难道不是一个普通的文字编辑器？

![img](/images/v2-b0efb1b79920a5f6cfddf318f9324969_720w.png)

还真不是，它居然是用`iframe`从外面引入的。

那么接下来尝试复制一下上面的那个`section`标签试试

![img](/images/v2-b9b48deb59c3e7e578e48b568e924554_720w.png)

![img](/images/v2-ed3e2a322f405140c7c152f4fa47f4a6_720w.png)

变成两个了！谜题解开了，样式的制作就是通过section标签，然后加上**行内样式**，就能制作出这种效果，那么知道原理后，我们自己动手实现上面的那个样式：

```html
 <section>
     <img src="https://mmbiz.qpic.cn/sz_mmbiz_png/YMeJJzp9Dn2IxGfxCmDyY0OLaLbWkAUk37JCybKOFoPx52vfAia1RocLNOX31c0icJgGbUOica990Hox44QI9pSCA/?wx_fmt=png" alt="">
     <section style="background-color: red;height: 40px;line-height: 40px;padding-left: 10px;">
         <p style="margin: 0;">这里是一些文字</p>
     </section>
 </section>
```

再往浏览器上面一抛

![img](/images/v2-69085662ffb4e23561b7fea6316497d2_720w.png)

选中标签右键点击`Edit as HTML`就可以直接对网页上的标签进行编辑了。

![img](/images/v2-1ec352622828e38d089ecf3d3add117b_720w.png)

好了，可以看出样式正确的显示出来了，然后尝试保存后再进去看一下，没问题，样式还在，根据这种方法，就可以实现样式的自定义了。

# **写在最后**

什么？你看到这里然后你说从那个性冷淡风下面开始你就不知道我在讲什么了？你问我iframe，section是啥，还有后面那段代码为什么能改变样式？

这些属于前端开发的内容了，一两句也说不清楚，如果有兴趣可以去学习一下前端开发相关的知识。

研究了这么多，还是推荐直接使用市面上现有的编辑器，毕竟自己制作样式，还是挺麻烦的，而且辛苦制作出的样式，还不一定有别人的好看。

- 135编辑器
- 新媒体管家
