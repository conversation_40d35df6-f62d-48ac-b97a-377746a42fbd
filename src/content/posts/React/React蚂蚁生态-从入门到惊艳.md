---
title: "React蚂蚁生态-从入门到惊艳"
date: 2021-7-19 23:40:22
categories:
  - "React"
tags:
  - "React入门"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

消失了两个月，这段时间公司新接了一个项目，之前的技术栈是`Vue+JavaScript`而新项目换成了`React+TypeScript`，最开始换技术栈的主要原因是为了**解决大量数据表格渲染问题**（即上万条数据），经过很长时间的探索，目前发现Vue上比较成熟的虚拟列表方案对比React来说是非常少的。

而这一段时间，我进行了很大程度的研究，因为没有那么多的人力物力从头开始搭建后台框架，项目又比较急，前一周才提这个事情，后一周就要写一些东西出来，所以我又去找了大量开源的后台管理系统框架，总共找到有20多种。

经过考虑，最后我选择了由蚂蚁开源的一套后台框架[Ant Design Pro](https://beta-pro.ant.design/index-cn)，其实我以前是会React的，但是没有做过比较正经的项目，所以很想有一个机会做一个企业级的项目。

在项目开始之前，我考虑到React是单向数据流，没有Vue那种双向绑定功能，也就是`v-model`，我就比较疑惑如果要写一个表单，那岂不是每次输入后我就需要调用`setState`或者`setxxx`这个hook，那岂不是非常麻烦？每一个表单都需要大量的代码进行实现？这样效率岂不是比Vue低太多了。

事实证明我错了，随着对蚂蚁这一套生态的深入了解，让我每天都在惊叹蚂蚁开源这套东西也太好用了吧！

# 1. Antd

> Antd是基于Ant Design设计体系的React UI组件库，主要用于研发企业级中后台产品。

敢说只要是国内使用React做开发，除了大厂之外用React开发几乎都是使用的这个UI组件库进行开发，它除了提供大量好用的组件之外，还提供了可以展示10万条数据的选择框。它也是使用到了虚拟列表功能。

![image-20210607231622606](/images/React/React蚂蚁生态-从入门到惊艳/image-20210607231622606.png)

Antd的表格也是一大亮点！它提供了`render`方法，可以让你自定义每一列表格，通过它就可以拿到当前列对应的值，如果在Element中想要实现这个功能，你就必须使用插槽，过程是非常繁琐的。

# 2. ProComponents

我最近天天都在吹这东西实在是太好用了，它在Antd之上又封装了一层，比如后台架子。

## 2.1 ProLayout

![image-20210607225457453](/images/React/React蚂蚁生态-从入门到惊艳/image-20210607225457453.png)

ProLayout，**这一个组件就能完成侧边导航栏、顶部栏、页脚等的搭建。**那么你可能会问，一个组件管这么多事情，那是不是很难进行定制。

然而让我更加惊讶的就是它的自定义功能，它提供了大量的属性让你可以自定义几乎所有的东西，比如导航栏、顶部栏这些统统都是能够进行替换的。

## 2.2 ProForm

前文中提到React是单向数据流，创建一个表单是否会非常麻烦，需要大量使用setState？

然而不管是Antd还是ProForm，都将创建表单的重复程度降低了很多个档次，回想一下如果是在Vue中要创建一个表单，那是不是应该在data里面声明一个变量，然后将这个变量通过`v-model`进行双向绑定。

而在ProForm里甚至都不需要声明变量：

```jsx
<ProForm
    onFinish={async (values) => {
        await waitTime(2000);
        console.log(values);
        message.success('提交成功');
    }}
>
    <ProFormText name="mangerName" label="商务经理" />
</ProForm>
```

上面是简化后的代码，你只需要写一个`name`属性，然后啥都不用干，直接点击确定按钮，它会自动进入到`onFinish`这个属性中，在这个属性中你可以得到一个`values`，该`values`就包含了你定义表单的所有值，你只需要将它传递给后端就可以。同时在你点击提交的时候，它会自动给你添加一个提交动画。

![save](/images/React/React蚂蚁生态-从入门到惊艳/save.gif)

表单验证也非常的简单！

```jsx
<ProFormText name="mangerName" label="商务经理" rules={[{required: true}]} />
```

只需要添加上`rules`，它会自动帮你补全提示信息。

总之ProComponents对Antd组件进行了大量的封装，几乎可以解决你搭建管理系统中百分之80的需求。

# 3. Umi

> Umi，中文可发音为乌米，是可扩展的企业级前端应用框架。Umi以路由为基础的，同时支持**配置式路由**和**约定式路由**，保证路由的功能完备，并以此进行功能扩展。然后配以生命周期完善的插件体系，覆盖从源码到构建产物的每个生命周期，支持各种功能扩展和业务需求。

React之前声明路由必须要到各个组件中进行声明，非常不方便管理，虽然不知道什么时候发布了`React Router Config`，让React能够像Vue一样将路由统一配置在一个文件中，方便管理。

Umi解决了React路由的问题，同时它不仅仅解决路由问题，**还提供了服务端渲染（SSR）、快速刷新、按需加载、环境变量等等**方便功能。

同时在`Umi 3.5`时发布了一个新功能：mfsu，它可以大大的提升项目的启动和热更新速度，具体可以看下图中的对比。

![测试](/images/React/React蚂蚁生态-从入门到惊艳/O1CN01HMNHEV1PSJ3N0tm9L_!!6000000001839-2-tps-1234-453.png)

可以看到开起了mfsu功能后，除了首次启动，第二次启动减少了非常多的时间，我自己亲测第二次启动时间一般在4s左右，这会让你在开发前端应用时具有更高的效率。

但是！mfsu功能目前来讲还存在着非常多的问题，比如文件更新后页面偶尔不进行刷新。

# 4. Dva

众所周知，React中复杂状态管理一般是使用Redux，但是使用过Vuex的人应该都知道，相比于Redux，Vuex使用起来简直方便太多，于是蚂蚁在Redux和Redux-saga的基础上进行了一次封装，这就是Dva，Dva具有Vuex一样的易用性。同时还可以作为插件插入到Umi项目中，然而在目前的项目中我是没有引入Dva的，因为后台管理系统几乎很少会需要一个多界面共用的状态，即使有，通过Umi自带的简易数据流也可以很轻松的完成这些状态的传递。

# 5. Ahooks

大神器！其中最为推荐的就是它的`useRequest`这个核心hook！虽然该hook是受到了其它库的启发，但是蚂蚁团队的目标是将它打造的比其它库更为好用。

`useRequest`具有**防抖、节流、并行请求、依赖请求、手动触发、默认请求等等**一大堆的功能！

想想你在Vue中如果要获取一个网络数据那该怎么进行获取？

```js
// 首先在data中声明一个变量 data

axios.get("https://example.com").then(res=>{
    data = res.data.data;
})
```

那么如果你新增了一条数据需要重复进行请求呢？那你就需要将上面的代码封装成为一个方法，用到的时候再进行调用，如果这个时候你还需要防抖或者节流功能呢？你是不是还得再包一层？如果你还需要一个loading变量，用来控制界面是否显示一个loading提示呢？你是不是还得声明一个loading，再在请求之前将loading设为true，然后等待请求完毕后将loading设置为false？

我相信百分之80的人都会这么做，因为我也是这么做的，当然大佬可能会有更加简单的方式实现我上面说的这些。

但是使用了`useRequest`这些就变得非常简单。

```jsx
const { data, error, loading, run } = useRequest(getUsername); // getUsername为需要发起的网络请求
```

好了，就是这么简单，只需要将网络请求放在`useRequest`这个hook中，它会自动给你生成请求成功的值data，请求失败的值error，请求状态loading，重复请求函数run。

还没有完，它在`getUsername`还可以跟一个对象，里面可以放置各种参数：

```jsx
const { data, error, loading, run } = useRequest(getUsername, {debounceInterval: 500}); 
```

`debounceInterval`就代表开启防抖，是不是非常简单！

当然它的功能还不止这么一点，这里只是做一个抛砖引玉的效果，具体功能你可以参考官方文档，我也会在后面的文章中将我觉得常用的hooks整理出来。

# 6. 总结

随着对蚂蚁这一系列React的生态了解，以及对于jsx使用起来越发的得心应手，在这里也不说Vue和React孰好孰坏，只能说比起React来讲，Vue更适合才学习完`HTML+CSS+JavaScript`的朋友上手，并且学习Vue在国内可以得到大量的工作机会，有一大堆小型外包公司都是使用的Vue技术栈，如果你是才转行的人，那么Vue绝对是你的不二选择，至于React，推荐有时间的话一定要进行学习，它会打开一个前端新世界。
