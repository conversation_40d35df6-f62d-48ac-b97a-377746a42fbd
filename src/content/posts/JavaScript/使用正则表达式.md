---
title: "JavaScript-使用正则表达式"
date: 2020-9-10 20:39:49
categories:
  - "JavaScript"
tags:
  - "正则表达式"
---


# 前言

之前有一篇文章讲了正则表达式的类型以及正则表达式的用法：[了不起的正则表达式](/2020/09/10/杂谈/了不起的正则表达式/)。

本篇文章就来讲一讲JavaScript中，怎么使用正则表达式。

# 方法

exec：一个在字符串中执行查找匹配的RegExp方法，它返回一个数组（未匹配到则返回 null）。
test：一个在字符串中测试是否匹配的RegExp方法，它返回 true 或 false。
match：一个在字符串中执行查找匹配的String方法，它返回一个数组，在未匹配到时会返回 null。
matchAll：一个在字符串中执行查找所有匹配的String方法，它返回一个迭代器（iterator）。
search：一个在字符串中测试匹配的String方法，它返回匹配到的位置索引，或者在失败时返回-1。
replace：一个在字符串中执行查找匹配的String方法，并且使用替换字符串替换掉匹配到的子字符串。
split：一个使用正则表达式或者一个固定字符串分隔一个字符串，并将分隔后的子字符串存储到数组中的 String 方法。

# 最后

正则到用时方恨少，平时就可以多练习，因为正则表达式在很多时候都非常有用，比如匹配URL，抓取网页中的一些信息。

尤其是在爬虫领域，正则表达式几乎无所不能，可以抓取你想要的任何页面上的信息。

当然是用爬虫需要谨慎，如果不让抓取的网页，那就别去抓取了，如果要强制抓取，可能会受到一些制裁，严重的甚至还会有牢狱之灾。

# 参考文章

[正则表达式MDN](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide/Regular_Expressions)
