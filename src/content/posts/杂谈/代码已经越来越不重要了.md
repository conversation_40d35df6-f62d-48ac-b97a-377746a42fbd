---
title: "代码已经越来越不重要了"
date: 2025-7-13 22:00:00
categories:
  - "杂谈"
tags:
  - "杂谈"
---


前端周刊：[RenderedWeekly](https://renderedweekly.com) 已经正式上线！每周一的早上10点之前更新，汇集上一周的前端热点、技术文章、开源项目、工具推荐等。虽然刚刚起步，排版和内容还在持续优化中，但我会用心打磨每一期，欢迎大家关注订阅。

---

## AI 编程的普及

从我使用 AI 辅助编程这半年来，我明显的感受到身边的同事已经开始接受 AI 编程了，后端也开始使用 AI 来重构一部分功能，并且由于 AI 编程领域竞争的白热化，很多厂商都提供了非常可观的免费额度。

这些免费的额度在一定程度上降低了 AI 编程的门槛，使得更多的开发者能够尝试和使用 AI 工具来提升自己的工作效率。

现在我的写代码习惯是稍微复杂的功能就交给 AI 来进行编写，然后我再看一下实际效果，如果效果不对，我再进行调整。

这类代码有：

- 实现组件的拖拽
- 对接第三方API
- 实现复杂的业务逻辑交互

大多数复杂的逻辑我都交给了 AI 来编写，因为 AI 写出来的代码经过我半年的审查，我发现只要提示词到位，它写出来的代码非常健壮，往往比自己实现的时候考虑的代码的边界处理更加全面。

但下面这些情况我还是会手动编写：

- 对现有的业务逻辑进行删除或者添加功能
- 页面样式新增和调整
……

在企业开发中是需要根据 UI 设计图来开发页面的，我试过很多次将图片直接发给 AI 让 AI 来生成页面，但效果并不理想，往往生成的页面样式和设计图有很大的差距，还需要我逐行比对，调整样式，这个过程我感觉比我完全编写样式更加费劲，因为你还得阅读 AI 生成的代码，理解它的 DOM 结构，再进行修改。

所以我后面学聪明了，每次构建页面的时候，我会在给 AI 的提示中描述页面的大概逻辑，然后让 AI 不要编写页面 DOM，只编写页面的逻辑和样式，这样我只需要手动编写页面的 DOM 结构和样式，再将逻辑挂载到 DOM 上就可以了。

## 实际版本推进并没有变快

写代码是一个团队协作的过程，往往需要前端、后端的配合才能完成一个版本。

在实际开发中，前后端对接的过程并没有因为 AI 编程而变快，所以我们往往评估工时的时候依然是按照以前没有 AI 编程的方式来评估。

## 复杂的交互需求敢接了

在我们项目中，一般工时的评估是不考虑交互方式的，比如各种动效、拖拽效果等。

因为这些交互方式的实现往往比较繁琐，即便手写出来了，有时候会花费大量的时间去调整和优化。

而使用了 AI 编程后，这些复杂的交互需求 AI 往往能够很快很好地实现，并且不会出现太多的问题，以前如果有类似的交互需求，我们往往都会以“耗费时间长”来拒绝，但现在几乎不会再拒绝类似的需求。

## 重构代码更简单了

以前重构代码往往需要花费大量的时间去理解和修改现有的代码逻辑，而现在使用 AI 编程后，重构代码变得更加简单。AI 可以协助你在短时间内理清楚代码逻辑，并且提供重构建议。

各个 AI 编程工具的差距也体现在这一点上面，由于很多代码是跨越了多个文件的，所以 AI 编程工具需要能够理解整个项目的代码结构，才能更好的进行重构，否则重构出来的代码可能会出现逻辑错误。

所以现在很多人提出了`Context`工程学，即如何让 AI 能够更好的理解上下文，因为让 AI 处理整个项目的代码在现在是不现实的，现在的大模型 token 限制一般在 20k 左右，而一般免费使用的模型的 token 限制则更少，跨越多文件的重构表现并不好。

跨文件理解能力是 AI 编程工具的一个重要指标，也是各个工具之间的差距所在。

## 代码不重要了

因为上面描述的各种原因，让我觉得我写代码的方式已经变成了从“完全亲自写代码”到“分成一块块任务，让 AI 来写代码，我再进行拼凑”。

这种转变让我意识到，代码的价值已经不在于功能实现上面，在以前，对接第三方、实现复杂需求往往需要花费大量的时间和精力去阅读文档、查阅资料、实现具体逻辑。

而现在你只需要给出一个清晰的需求描述，AI 就能够帮你完成大部分的工作，阅读文档、查阅资料的时间大大减少，即便有问题，你也可以只询问核心的问题，了解问题的本质，再修改起来就变得非常的快。

我记得马斯克才刚刚收购 Twitter 的时候，还要求推特的程序员将自己最近30到60天内编写的代码打印出来，以供他和特斯拉的工程师审查。

我在以前居家办公的时候，也被要求每天上报当日的代码量。

而现在，代码量已经不可能再成为衡量开发者工作的重要指标了。

## 重要的是思维方式

比起代码能力，我觉得思维方式才是最重要的，如果还想要自我提升，在 AI 的辅助下节省来的时间并不是用来刷短视频、刷剧的。

省下来的时间可以用来拓展自己的视野，横向发展，比如学习英语、学习财务知识、学习心理知识等。

我个人对于 AI 编程的看法是乐观与悲观并存的。

### 乐观

1. AI 编程的普及可以让更多的人参与到软件开发中来，大大降低了编程的门槛，产品经理、设计师等非技术人员也可以通过 AI 编程工具来实现自己的想法。这将促进更多的创新和创意的实现，推动软件行业的发展。
2. 对于个人开发者来讲，AI 编程可以大大提高工作效率，以前一些费时的工作现在可以通过 AI 快速完成，节省了大量的时间和精力。个人开发者可以将更多的精力放在产品设计、用户体验等更重要的方面。
3. AI 编程可以帮助开发者更好地理解和学习编程语言和框架，AI 可以提供实时的代码建议和错误提示，帮助开发者更快地掌握编程技能。这对于初学者来说尤为重要，可以降低学习曲线。
4. AI 编程可以让开发者节约大量的时间，来开阔自己的视野，学习其他领域的知识。

### 悲观

1. AI 编程的普及可能会导致产品实现速度远高于产品设计速度，可能推动公司进行裁员。
2. 对于不接受 AI 编程的开发者来说，可能会面临被淘汰的风险，因为 AI 编程已经成为一种趋势，越来越多的公司和团队开始使用 AI 编程工具来提高工作效率。
3. 由于技术封锁和模型的限制，大量的开发者可能无法接触到最先进的 AI 编程工具，导致技术水平差距进一步拉大。
4. 由于编码工作变得越来越简单，会出现大量的同质化的产品，导致市场竞争加剧，对于传统的个人开发者来说，可能会面临更大的压力。

## 时代的车轮

如果在半年前，我还想象不到 AI 的应用场景，但是在半年后，我生活的方方面面已经离不开 AI 了。

这直接导致了信息鸿沟的加大，我记得我2年前看过的一个探险UP主，他去了很多乡镇和偏远的山区，那些地方的人们仿佛还过着2000年初的生活。

这让我产生了非常强烈的割裂感，感觉到人所处的环境不同，似乎看到的世界也不一样，信息的获取和处理能力也存在着巨大的差异。

我是一个愿意接受新事物的人，所以我自己会顺应时代的车轮进行前进。