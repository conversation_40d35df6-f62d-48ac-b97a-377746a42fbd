---
title: "2的倍数的每一位相加永远不会等于369"
date: 2021-2-20 21:50:35
categories:
  - "杂谈"
tags:
  - "杂谈"
---


好久不见先水一篇文章...过年期间几乎没有怎么学习，包括年前正在总结的webpack系列都已经暂停。

学习完webpack基础知识后，我决定使用webpack搭建一个油猴脚本开发环境，即可以使用React语法来编写油猴脚本。

---

闲话不多说，进入正文：

今日突然之间又回顾到老高的“宇宙法则369”这期视频，老高提到：**2的倍数每一位相加，即64：`6+4=10`，`1+0=1`，这样一直将数字加到只有个位数，得到的结果永远是1、2、4、8、7、5的循环，永远不会等于3、6、9。**我觉得这个非常有意思，决定验证一下。

这肯定是不能手算，手算不知道要算到什么时候，直接使用JavaScript来完成计算吧。

代码如下：

```js
let math = 1;
let allArray = [];

// 当math大于int的最大值，停止计算
while (math <= Number.MAX_SAFE_INTEGER) {
  allArray.push(add(math));
  math *= 2;
}
// 打印最后的结果
console.log(allArray);

/**
 * 一直加到小于10
 * @param {number} count
 * @return {number}
 */
function add(count) {
  // 拆分
  let array = count.toString().split("");
  let allData = 0;
  array.forEach(sum => {
    allData += parseInt(sum);
  });
  if (allData > 9) {
    // 需要将所得的值return，不然无法得到递归后的值
    return add(allData);
  } else {
    return allData;
  }
}

/*
  最后输出结果
  [
    1, 2, 4, 8, 7, 5, 1, 2, 4, 8, 7, 5,
    1, 2, 4, 8, 7, 5, 1, 2, 4, 8, 7, 5,
    1, 2, 4, 8, 7, 5, 1, 2, 4, 8, 7, 5,
    1, 2, 4, 8, 7, 5, 1, 2, 4, 8, 7, 5,
    1, 2, 4, 8, 7
  ]
*/
```

运行一下可以很清晰的看见真的是按照1、2、4、8、7、5在循环。

上面的例子是一个很简单的递归，并且在JavaScript中`Number.MAX_SAFE_INTEGER`是一个值为 9007199254740991的常量。而JavaScript能够安全存储`-2^53-1到2^53-1`之间的数值（包含边界值）。

**同时调用自身的时候不要忘记加上return，不然无法得到递归后的值。**

参考资料：[Number.MAX_SAFE_INTEGER](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER)。