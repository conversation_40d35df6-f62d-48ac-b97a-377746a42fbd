---
title: "了不起的正则表达式"
date: 2020-9-10 20:39:49
categories:
  - "杂谈"
tags:
  - "正则表达式"
---


# 1. 前言

如果要验证一段字符串是否符合规定的格式，就要用到正则表达式。

比如前端经常做的手机号格式验证，邮箱格式验证，这些都能是正则表达式来搞定，不过这种常用的正则随便一搜，就能搜出一堆，根本不需要自己去写。

但是，如果要使用爬虫抓取，请求拦截等等，这些正则表达式一般都是要根据具体情况进行判断，一般来说都找不到人家编写好的，所以这个时候就需要学习正则。

## 1.1 正则表达式是什么

> 正则表达式（英语：Regular Expression，常简写为regex、regexp或RE），又称正则表示式、正则表示法、规则表达式、常规表示法，是计算机科学的一个概念。正则表达式使用单个字符串来描述、匹配一系列符合某个句法规则的字符串。在很多文本编辑器里，正则表达式通常被用来检索、替换那些符合某个模式的文本。

也就是说，只要需要检索、替换文本中有规律的字符，就需要用到正则表达式。

# 2. 入门

## 2.1 字符组

**字符组**（`[]`）允许匹配一组可能出现的字符。

例如：`[Pp]ython`，可以匹配Python和python。

## 2.2 区间

在字符组中，使用连字符`-`，表示区间。

比如`[0123456789]`可以简写为`[0-9]`，而匹配`a-z`的字符可以写成`[a-z]`。

## 2.3 转义字符

如果要在区间中匹配`-`字符，就需要使用转义字符`\`，例如`[\-]`。

## 2.4 取反

在字符组中，如果匹配不包含数字的字符组，就需要用到取反符号`^`，即：`[^0-9]`。

## 2.5 快捷匹配

学会了上面的内容后，如果要匹配数字就需要使用`[0-9]`，匹配字母就需要使用[A-Za-z]，其实还有更简单的方法可以进行匹配。

正则表达式中有下面的一些快捷方式。

| 实例 | 描述                                                         |
| ---- | ------------------------------------------------------------ |
| `.`  | 匹配除 “`\n`” 之外的任何单个字符。要匹配包括 ‘`\n`’ 在内的任何字符，请使用象 ‘`[.\n]`’ 的模式。 |
| `?`  | 匹配一个字符零次或一次，另一个作用是非贪婪模式               |
| `+`  | 匹配1次或多次                                                |
| `*`  | 匹配0次或多次                                                |
| `\b` | 匹配一个长度为`0`的子串                                      |
| `\d` | 匹配一个数字字符。等价于 `[0-9]`。                           |
| `\D` | 匹配一个非数字字符。等价于 `[^0-9]`。                        |
| `\s` | 匹配任何空白字符，包括空格、制表符、换页符等等。等价于 `[\f\n\r\t\v]`。 |
| `\S` | 匹配任何非空白字符。等价于 `[^\f\n\r\t\v]`。                 |
| `\w` | 匹配包括下划线的任何单词字符。等价于’`[A-Za-z0-9_]`’。       |
| `\W` | 匹配任何非单词字符。等价于 ‘`[^A-Za-z0-9_]`‘。               |

## 2.6 开始和结束

正则表达式中 `^`指定的是一个字符串的开始，`$`指定的是一个字符串的结束。

例如：`^python$`，匹配的即为以p开头，n结尾的字符串。

**注意：在字符组`[]`中使用`^`是取反的操作，不要混淆。**

## 2.7 重复

上面的`+`和`*`虽然也可以匹配多次，但是不能具体指定匹配多少次，比如手机号11位数字，就需要使用到重复匹配符`{}`。

在一个字符组后加上`{N}` 就可以表示在它之前的字符组出现`N`次。

例如：`/d{11}`，表示匹配11位数字。

`\d{3,4}` 既可以匹配`3`个数字也可以匹配`4`个数字，不过当有`4`个数字的时候，优先匹配的是`4`个数字，这是因为正则表达式默认是**贪婪模式**，即尽可能的匹配更多字符，而要使用**非贪婪模式**，我们要在**表达式后面加上 `?`号`\d{3,4}?` **。

如果写成`\d{1,}`表示匹配一个和无数个。

# 3. 进阶	

## 3.1 分组

如果要捕获数据，就需要使用分组`()`。

## 3.2 或者

在分组中，还可以使用或者符号：`|`。

例如要提取文件的后缀名：`(.jpg|.gif|.jpeg|.png)`。

## 3.3 非捕获分组

如果不需要捕获数据，但是又需要使用分组的某些功能，就可以使用到`(?:表达式)`。

## 3.4 分组的回溯引用

使用`\N`可以引用编号为`N`的分组。

例如`(\w)(\w)\2\1`，可以匹配到abba，alla这种样式的词。

## 3.5 正向先行断言

`(?=表达式)`，指在某个位置向右看，**表示所在位置右侧必须能匹配`表达式`。**

## 3.6 反向先行断言

`(?!表达式)`的作用是**保证右边不能出现某字符。**

## 3.7 正向后行断言

`(?<=表达式)`，指在某个位置向左看，**表示所在位置左侧必须能匹配表达式**。

## 3.8 反向后行断言

`(?<!表达式)`，指在某个位置向左看，表示所在位置左侧不能匹配表达式。

**即正向必须匹配某值，反向为不能匹配某值，先行为向右看，而后行为向左看。**

# 4. 总结

可以看到正则表达式其实学起来还是很简单的，但是真的要根据实际情况编写出正确的正则表达式，还需要大量的练习，不然很容易就会造成漏匹配，或者多匹配的情况。

# 5. 网站推荐

[正则在线验证](https://regexr-cn.com/)：一个在线验证正则表达式是否能按照所想进行匹配的网站。

[正则表达式在线测试](https://codejiaonang.com/#/course/regex_chapter1/0/0)：一个正则表达式学习网站，上面一边讲解一边留下练习题，非常值得一学。

[常用的正则规则](https://github.com/any86/any-rule)：工作中经常会用到的一些正则表达式，基本上已经是最优正则，推荐常见的正则比如验证身份证，手机号等直接使用已经被总结的正则，不要自己去手写，当然大神除外。
