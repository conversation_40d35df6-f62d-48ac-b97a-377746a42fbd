---
title: "我选择了Astro作为周刊架构"
date: 2025-06-16 21:00:00
---


前端周刊：[RenderedWeekly](https://renderedweekly.com) 已经正式上线！每周一的早上10点准时更新，汇集上一周的前端热点、技术文章、开源项目、工具推荐等。虽然刚刚起步，排版和内容还在持续优化中，但我会用心打磨每一期，欢迎大家关注订阅。

---

上周末，紧锣密鼓地将周刊搬上了线，至于当时选择框架的时候我在Astro和Next.js之间纠结了很久，最终选择了Astro。

Astro是近期热门的前端框架，它非常适合用来生成以内容做驱动的网站，比如博客、文档、门户网站等。

总之就是内容多，交互不是那么复杂，就可以选择Astro。

## 为什么不是Next.js

Next.js 是一个功能强大的框架，自从React团队宣布不再维护 create-react-app 后，Next.js 就成为了 React 生态中事实上的标准框架。

Next.js 有一个天然的优势，那就是它经过多年的积累，拥有非常庞大的生态，也拥有着前端框架中最多的生态，几乎所有的问题你都可以找到解决方案，并且Next.js直接使用React，所以如果你是React开发者，那么使用Next.js不会有什么难度。

但Next.js它主打的是一个全栈框架，即既可以用来开发前端，也可以用来开发后端，它还可以打包成静态网站进行部署。

但是！

它不是那么适合做内容驱动的网站，比如尤其是博客。

我写博客的时候大部分都用的是Markdown，而 Next.js 不安装其它包的情况下，并不支持Markdown。

而且要把它搭建成博客风格，需要花费大量的时间。

## 为什么不是Docusaurus

Docusaurus 是一个非常流行的静态网站生成器，它非常适合用来构建文档网站。

在上一篇文章中我提到过我想要单独再开一个网站，写一些源码分析的文章，当时我就使用的Docusaurus。

因为Docusaurus是基于React的，所以它天生就支持React，同时它还支持`mdx`，可以在Markdown中使用React组件。

但是我放弃了，Docusaurus自定义起来不是那么方便，它的条条框框太多了。

## Astro

至于为什么选择Astro，最大的原因是它现在非常的火爆，已经在[GitHub](https://github.com/withastro/astro)上获得了51k+的Star，并且它具有高度可定制的特性，它还可以通过插件，在项目中使用React、Vue、Svelte、SolidJS等框架。

它可以很方便地对Markdown进行处理，它的动态路由形式非常的灵活，并且它会将所有的页面都生成静态文件，它的语法也非常简单，一种类原生语法，同时它提供的Layout组件，可以很方便地对各个板块进行布局。

由于它的高度可定制性，所以它做起SEO来非常的方便，它还支持Sitemap，并且它还支持RSS，可以很方便地将RSS订阅集成到网站中。

内容驱动的网站来说，SEO做得好不好，直接决定了网站的流量。

最后，它编译的速度非常快，编译完成后打开页面，点击右键查看源代码几乎看不到多余的代码，这大大地提高了网站的加载速度。

它还提出了一个概念，叫做`Island Architecture`，即岛屿架构，它可以将页面中的内容分为两部分，一部分是静态内容，一部分是动态内容。

静态内容会被直接渲染到HTML中，而动态内容则会被延迟加载，这大大地提高了网站的加载速度。

## 总结

Astro 是一个非常优秀的静态网站生成器，它非常适合用来构建博客、文档、门户网站等。

它的高度可定制性，让它可以很方便地做出各种风格，同时它还提供了非常多的主题和插件，可以满足大部分的需求。