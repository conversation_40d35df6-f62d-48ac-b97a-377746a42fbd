---
title: "AI辅助开发新时代：Cursor让独立开发者的困境不再难解"
date: 2025-03-05 21:32:55
categories:
  - "杂谈"
tags:
  - "独立开发"
---


最近经常刷到完全不懂代码的人，使用Cursor快速开发了一款应用，然后赚到了钱的帖子。但更大的一部分人，开发出了大量的记账、TODO、日记类应用。

其实在AI辅助编程还没出现的时候，很多独立开发者都选择开发这类应用，所以它们也被戏称为：独立开发三件套。

我去年开发了一款炉石卡组小程序，那时还没有接触到Cursor，所以大部分代码都是自己敲出来的，花费了不少时间。后来接入了微信小程序广告，一天的广告收入最高时达到10多块，最低时只有几毛钱。

后来因为数据都是从其他网站爬取的，而那个网站加入了反爬机制，导致我的服务器无法爬取到实时数据。同时，由于每天的收入太低，我失去了继续维护的兴趣，最终放弃了这个项目。

## 独立开发的难点

在开发过程中，我遇到了非常多的困难，其中最大的挑战就是UI设计。

作为一名程序员，UI设计并不是我的强项。在开发过程中，我只能靠着过去积累的一些经验，拼拼凑凑地搭建页面。

而在后端开发上，我当时选择了Python，因为Python有很多现成的数据处理库，可以快速处理数据。但后来我发现，使用Python存在两个巨大的问题：

1. 需要处理的数据稍微复杂一点，响应时间就会非常长，导致用户体验极差。
2. Python服务端占用的内存非常大，达到了1G左右。尽管我写的代码量并不多，服务还时不时会崩溃，导致我不得不经常重启。

对于第一个问题，我使用了大量的缓存机制，但缓存越多，占用的内存就越多。服务器内存是非常昂贵的资源，往往增加1G内存，就要额外花费几十到上百元。

对于第二个问题，我没有找到很好的解决方法，只能时不时刷一下小程序，发现服务器崩溃了就重启一下。

如果现在让我重新开发，我一定会选择Golang作为后端语言，因为Golang的内存占用非常小，性能极强，天生支持高并发，非常适合开发小型服务端。

### 部署

项目的部署必须要有域名和服务器，并且域名一定要进行备案。如果没有ICP备案，微信小程序是无法对其发起请求的。当然，也可以直接使用微信提供的云开发功能，但云开发是按使用量计费的，并且灵活性不如自建服务器。

如果要备案域名，就必须拥有一台国内的云服务器。好消息是，最便宜的云服务器一年仅需100元左右。所以，如果想要备案域名，就需要购买一台国内云服务器，并且域名备案大约需要10天左右的时间。

### 推广

小程序开发完成后，让我非常头疼的是如何推广。当时我的第一选择是去贴吧发布帖子，后来大部分流量都来自于那一篇帖子。但当我尝试在知乎等平台发布内容时，这些网站会将帖子识别为广告并删除。

这其实很正常，因为很多网站需要付费推广。不付费就想在那些平台上推广，等于直接动了别人的蛋糕，所以删帖的速度非常快。

后来我尝试通过用户之间的互相推荐来增加用户量，因此加入了很多分享功能。虽然效果整体不佳，愿意分享的人很少，但只要有一个人愿意分享到群里，往往就能带来十几个新用户。

### 变现

个人主体的小程序无法接入支付功能，所以变现方式只有广告。不得不说，微信小程序的广告系统还是非常不错的——每半个月结算一次，而且会自动将结算金额打入到你的银行卡中。因此，如果小程序拥有大量用户，广告收益是相当可观的。

我为公司开发的另一款小程序接入了开屏广告，每天有约5000名新用户，仅开屏广告一项，每天的收益就有100多元。所以，开发一个用户量大的小程序，广告收益足以养活自己。

## 现在

随着Cursor等AI辅助开发工具的出现，以及Claude等模型的UI审美越来越在线，现在的独立开发已不再像去年那么困难。如果让我重新开发去年那款小程序，开发时间会大大缩短，从而有更多时间去打磨产品，而不是耗费在编写代码上。

因此，最近我也打算再次尝试一些独立开发项目。在AI的辅助下，开发过程应该会变得轻松许多。不过，现在面临的更大挑战是寻找一个好点子，而不是开发本身。