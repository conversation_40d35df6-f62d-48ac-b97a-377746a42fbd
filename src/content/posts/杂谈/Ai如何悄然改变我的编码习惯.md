---
title: "AI如何悄然改变我的编码习惯"
date: 2025-04-07 21:11:23
---


自从今年年初接触 Cursor 后，AI 已经逐渐改变了我的编码习惯，也让我对 AI 辅助编码有了更深的理解。

## ChatGPT

我是最早体验 ChatGPT 的一批用户，当时注册还需要国外手机号，为了注册还颇费了一番功夫。但在接触 Cursor 之前，我对 AI 的潜力并没有深刻的认识，不清楚它究竟能在哪些方面帮助我。

## GitHub Copilot

我也几乎算是第一批使用 GitHub Copilot 的用户。我看了一下我的 GitHub Copilot 订阅记录，我是从 2022 年 8 月开始订阅的，退订时间是 2024 年 5 月。原因是订阅近两年，Copilot 似乎缺乏显著更新，主要停留在基础的代码补全，这让我觉得它对我的价值有限了。

并且去年我接触到了阿里的通义灵码，它虽然在体验上可能稍逊于 Copilot，但关键在于它完全免费，且效果与 Copilot 相差无几，于是我便退订了 Copilot。

在接触 Cursor 之前，我一直是一个非常坚定的 WebStorm 用户，高度依赖其强大的代码补全、提示、重构和跳转等功能。由于我司项目普遍采用 JavaScript + Vue 2 技术栈，VS Code 在这些方面的体验确实不如 WebStorm。

主要是因为 VS Code 的代码跳转功能（尤其是在处理大型或复杂 Vue 2 项目时）体验不佳，有时难以准确溯源，所以我一直坚持使用 WebStorm。

或许正是因为 WebStorm 的闭源特性与 VS Code 的开源模式，VS Code 为第三方开发者提供了广阔的创新空间，Cursor 正是其中的佼佼者。

## Cursor

我接触 Cursor 其实相当晚，就在今年年初。当时我仍在使用 WebStorm，一位同事偶然提及，许多大型科技公司都在使用 Cursor。我当时对 Cursor 宣传的 "Tab-to-edit" 功能非常好奇，于是下载试用。

这次尝试让我大开眼界，从此，我的编码习惯开始被 Cursor 深深影响。

## 关于 Vibe coding

在公司项目中，能够充分利用这种 "Vibe coding" （或称 "Zero-shot coding"，即根据简单指令生成完整代码块）模式的机会并不多，因为公司的业务需求通常高度定制化。

我自己平时使用这种模式的机会也不算多，主要是因为缺乏好的独立项目点子。

## AI 究竟改变了我什么？

虽然基于 VS Code 的 Cursor，在传统的代码提示、补全、重构和跳转等方面，与 WebStorm 相比可能在某些细节体验上仍有差距，但 AI 的加持使其具备了许多 WebStorm 无法企及的能力。

例如，通过 "Tab-to-edit" 可以便捷地修改现有代码。Cursor 的 Agent 模式甚至能自动化许多开发任务，如创建文件、搭建项目骨架、生成组件、页面、服务乃至测试用例。很多时候只需提供清晰的需求或想法，Cursor 就能自动生成相应的代码，显著减少了手动操作。

在探索新项目或理解现有代码库时，只需使用 Cursor 的 "Ask codebase" 功能 (`@codebase`)，然后提出你的问题，例如："项目的权限控制是如何设计的？"、"登录流程是怎样实现的？"、"代码中是如何进行埋点上报的？"等等。Cursor 能分析整个代码库并给出相当详细和准确的回答。根据我的经验，在多数场景下，它都能提供令人满意的答案。

### 优化代码

优化代码也变得异常简单。只需给出合适的指令（Prompt），AI 就能帮你完成。例如："将这个组件的 CSS 类转换为 Tailwind CSS"、"移除文件中的冗余代码"、"将这段逻辑提取为一个独立的 React 组件"。

Cursor 处理这些任务通常既快又准，错误率很低。相比之下，在 WebStorm 中手动完成这些操作，不仅耗时更长，也更容易引入错误。尤其是在清理冗余代码时，AI 往往比手动删除更彻底、更干净。

### 生成代码

在某些场景下，我也会利用 AI 直接生成代码 (即所谓的 "Vibe coding" 模式)。尤其是在开发界面和结构相对固定的后台管理系统时，可以让 AI 参照现有代码风格和模式生成新页面或组件，效率极高。

但这里需要注意：如果参照的代码本身存在问题或不规范之处，AI 生成的代码很可能会继承这些缺陷。同样，如果基础代码的逻辑与新需求不完全匹配，直接生成的代码也可能不适用。

### 辅助写作

现在，许多文档编写工作也可以交给 AI 处理，例如为组件、项目或特定功能撰写介绍文档。AI 通常能根据简单的描述生成结构清晰、内容详实的文档，有时甚至比我自己写的更专业、更全面。

## 总结与思考

最近我一直在深入学习 AI 相关知识，特别是如何更有效地运用 AI 工具。我意识到，即使大家使用相同的 AI 模型，但提问方式、上下文提供、指令清晰度（即 Prompt Engineering）的不同，往往会导致结果产生巨大差异。

因此，我坚信在未来几年，熟练运用 AI 将不再是加分项，而是每一位程序员必备的基础技能，是新的"基本功"。