---
title: "将VSCode打造成为开发神器-插件篇"
date: 2021-1-30 18:16:23
categories:
  - "杂谈"
tags:
  - "VSCode"
---


VSCode现在是世界上最为常用的编辑器之一，为什么被称为编辑器，是因为它不像IDE一样集成了大量开发环境的配置，必须你手动配置很多东西，才能将它打造成为一个趁手的生产工具。

**VSCode最大的优势就在于它是完全免费的，你不需要支付任何费用，就可以得到一个开发各种代码的编辑器，也正因为它具有高拓展性，它可以用来编写Python、C++、C#、GO、Dart等一系列语言。**

你可以通过下面的插件将VSCode打造成为一个趁手的开发神器：

# 1. 编程语言类

下面的几个插件根据情况安装。

**C/C++**

**Dart**

**dart-import**

**Go**

**Go Doc**

**Python**

# 2. 代码风格类

**Beautify**：格式化代码插件，不过现在更推荐使用Prettier。

**Prettier - Code formatter**：当前前端最火的格式化代码插件。

**Better Align**：对齐代码。

**Better Comments**：更好的注释提示插件，推荐使用。

**change-case**：驼峰命名、下划线命名等等命名规则进行相互转化。

**ESLint**：团队开发的时候统一代码风格神器，以防因为代码风格不同而造成代码冲突。

**Guides**：代码块提示。

**SonarLint**：代码质量检测，和ESLint兼容，不会出现相互冲突的规则。

# 3. 代码提示类

## 3.1 Angular

开发Angular需要的插件。

**Angular Snippets (Version 11)**

## 3.2 React

开发React需要的插件。

**ES7 React/Redux/GraphQL/React-Native snippets**

**React Native Tools**

## 3.3 Vue

开发Vue需要的插件。

**Vetur**

**vue**

**Vue 3 Snippets**

**Vue Peek**

**Vue VSCode Snippets**

## 3.4 HTML

**Auto Close Tag**：自动闭合HTML标签。

**Auto Rename Tag**：更改一个HTML标签其对应的另一个标签也会被同时更改。

## 3.5 JavaScript/TypeScript

下面两个插件都为自动引入插件，只需要在文件中输入已经导出的函数名，就会自动添加引入代码！**推荐使用！**

**Auto Import**

**TypeScript Hero**

**JavaScript (ES6) code snippets**：快速生成代码片段。

## 3.6 CSS

**Color Highlight**：颜色高亮插件。

**Color Picker**：颜色选择插件。

**CSS Peek**：可以定位到项目中已经声明过的CSS类。

**px to rem**：将px转化为rem。

**Tailwind CSS IntelliSense**：原子化CSS库Tailwind的代码提示。

## 3.7 Flutter

开发Flutter需要的插件。

**Flutter**

**Awesome Flutter Snippets**

**Flutter Widget Snippets**

## 3.8 小程序

开发小程序需要的插件。

**minapp**

## 3.9 其它

**Bookmarks**：可以在代码中设置书签。

**Bracket Pair Colorizer 2**：将不同的括号显示不同的颜色。

**HTML CSS Support**：完成HTML和CSS。

**HTML Snippets**：HTML片段。

**Image preview**：引入项目中的图片预览。

**Import Cost**：检测导入项目中的包的大小，在优化项目的时候非常有用。

**JavaScript Booster**：将箭头函数和普通函数进行相互转换。

**TODO Highlight**：将注释中的TODO进行高亮。

**Todo Tree**：自动跳转到注释中含有TODO的地方。

## 3.10 慎用系列

**IntelliSense for CSS class names in HTML**：在编写HTML时获得CSS文件中的类名提示。

注：在开启该插件后，VSCode会有一个很长的文件搜索时间，不推荐使用。

**koroFileHeader**：在文件头部生成注释，并且能够一键生成函数JSDOC注释。

注：有时候会觉得自动生成比较烦，所以我暂时没有使用。

**Live Sass Compiler**：将Sass文件转换为CSS文件。

注：在不使用框架的情况下开发项目非常有用，但是使用框架后就不需要将Sass文件转为CSS文件。

**Sass/Less/Stylus/Pug/Jade/Typescript/Javascript Compile Hero Pro**：同上，只是转化的文件类型更多样。

# 4. 编辑器拓展

## 4.1 Cocos-creator

让VSCode能够成为Cocos开发工具，cocos-creator目前主要用来做H5游戏。

**Cocos Debug**

**cocos-creator**

## 4.2 主题

**Atom One Dark Theme**

**One Dark Pro**

**vscode-icons**：将VSCode左侧文件预览图标变得多样化。

## 4.3 语言包

**Chinese (Simplified) Language Pack for Visual Studio Code**：VSCode中文语言包。

## 4.4 工具

**Browser Preview**：使VSCode内嵌一个浏览器窗口。

**Code Runner**：使VSCode具有直接运行各种编程语言的能力。

**Code Spell Checker**：检测项目中的单词是否有拼写上的错误。

**Debugger for Chrome**：能够使VSCode在Chrome上面调试代码。

**Debugger for Firefox**：能够使VSCode在Firefox上面调试代码。

**Docker**：使VSCode具有操控Docker的功能。

**Draw.io Integration**：可以在VSCode中创建流程图。

**Gist**：使VSCode能够快速创建Gist代码片段。

**Git History**：可以快速的查看到Git提交历史。

**gitignore**：快速生成gitignore文件。

**GitLens — Git supercharged**：使VSCode具有Git管理功能。

**Kite AutoComplete AI Code**：根据你写的代码，AI自动推测你接下来可能要写的代码。

**Live Server**：能够启动一个服务器，当代码进行变动时自动刷新浏览器，主要是用于原生开发。

**npm**：检测项目中的`package.json`文件，可以通过该插件快速启动项目。

**npm Intellisense**：自动完成导入模块名称。

**open in browser**：快速在浏览器中打开HTML文件。

**Partial Diff**：快速比较两段代码的不同之处。

**Paste JSON as Code**：自动将JSON文件转化为TypeScript、C#等等各种语言的声明代码。

**Path Intellisense**：引入文件时具有文件地址提示。

**Project Manager**：项目管理，如果有几个经常需要打开的项目，就可以使用该插件，可以非常方便的切换项目。

**Quokka.js**：调试插件。

# 5. 最后

VSCode的一些插件配置还是非常麻烦的，需要你花一定的时间去进行积累才能将VSCode打造成为一个强大的开发工具。

之前我一直很不理解为什么VSCode如此难配置的编辑器会在前端开发中占据如此庞大的市场份额。

现在我明白不仅仅因为它是免费的，最重要的是它能够通过各种插件实现WebStorm这种收费的IDE的各种功能，并且还免去了因为开发不同的语言而需要不同的IDE带来的不便，实现VSCode在手，什么语言都能开发。