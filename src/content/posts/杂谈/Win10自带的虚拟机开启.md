---
title: "Win10自带的虚拟机开启"
date: 2020-10-7 14:37:11
categories:
  - "杂谈"
tags:
  - "虚拟机"
---


对于一个开发者而言，往往会接触到Linux操作系统，如果是在公司中，公司会有自己的服务器，但是如果想在个人学习中使用Linux，那么往往就只有买一个云服务器，或者通过虚拟机创建一个服务器。

一般便宜的云服务器的配置都是比较低的，如果是想要一个高配的云服务器，那还是一笔不小的开销，所以云服务器往往不能满足学习中的各种折腾。

如果你本身的电脑还不错，那么学习的时候完全可以用虚拟机来代替云服务器。

在Win10中，系统已经自带了虚拟机功能，以前往往都是通过VMware等软件来创建一个虚拟机，现在不需要这些软件，可以直接使用Win10已经自带的虚拟机功能。

### 第一步、打开Win10的虚拟机

在小娜窗口中搜索**启用或关闭 Windows 功能**。就是任务栏中的那个放大镜按钮

![image-20201007142118256](/images/other/image-20201007142118256.png)

如果没有这个按钮的话，`右击任务栏空白处-搜索-显示搜索图标`。

![image-20201007141943539](/images/other/image-20201007141943539.png)

然后勾选`Hyper-V`，点击确定，重启电脑。

![image-20201007142349792](/images/other/image-20201007142349792.png)

### 第二步、创建虚拟机

重启电脑会经过一段时间的等待，打开开始菜单找到**Windows管理工具**，选择**Hyper-V管理器**。

![image-20201007142505569](/images/other/image-20201007142505569.png)

点击右边的**快速创建-新建-虚拟机**。

![image-20201007142709586](/images/other/image-20201007142709586.png)

选择**名称和储存位置**。

![image-20201007142826132](/images/other/image-20201007142826132.png)

选择**第一代**。

![image-20201007142921922](/images/other/image-20201007142921922.png)

**分配内存**。

![image-20201007142943462](/images/other/image-20201007142943462.png)

选择**镜像地址**，注意：镜像需要从网上下载。

![image-20201007143020525](/images/other/image-20201007143020525.png)

最后点击**下一步-完成**，一个虚拟机就创建好了。

在主界面就可以看到你的虚拟机了：

![image-20201007143158961](/images/other/image-20201007143158961.png)

最后连接进虚拟机进行初始化，一个虚拟机就创建完毕了。
