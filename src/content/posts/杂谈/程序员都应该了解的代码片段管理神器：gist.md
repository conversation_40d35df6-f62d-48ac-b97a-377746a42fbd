---
title: "程序员都应该了解的代码片段管理神器：gist"
date: 2020-12-30 15:07:48
categories:
  - "杂谈"
tags:
  - "Gist"
---


# 1. 前言

在平时的学习或工作中，有没有那么一些代码让你觉得写的比较好，具有复用性，并且今后的项目可能会用到？

肯定会有，如果没有，说明你代码写的太少了，不知道哪些代码是经常会用到的代码。

**那么这些代码应该保存在哪里比较合适呢？**

起初我是保存在本地的markdown里面，但是我发现一个问题，因为一个程序员不可能老是在同一台电脑上面办公，可能会在公司、家里、甚至其它很多地方写代码，那么保存到本地的markdown就显得不是那么方便。

后来我将代码片段保存在有道云笔记中，但是我又发现了一个问题，打开和查找代码片段相当不方便，到一个新的环境中还要下载有道云笔记，虽然有在线版本，但是依然很不方便。

这时我发现有很多人推荐使用gist，尝试用了一下，非常好用，所以在这里推荐给大家。

# 2. 缺点

**先说gist的缺点：无法直接进行访问，必须要进行科学上网。**

这个缺点实在是太大了，正因为这个缺点，很多人放弃使用gist，如果你不能科学上网，后面的内容就可以不用再看。

# 3. gist详细介绍

说完了gist的缺点后，再说说gist的优点：

1. 易用
2. 和GitHub账号绑定
3. 代码片段可以直接用于网页上
4. 和gist搭配的插件非常多
5. ...

gist的优点可以说是太多太多了，几乎满足一个程序员对保存代码片段的所有需求。

# 4. 使用步骤

gist的使用也非常简单，直接打开官网https://gist.github.com/。

![image-20201230143526517](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230143526517.png)

其中gist会根据你输入的文件后缀名，高亮你输入的代码：

![image-20201230143621041](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230143621041.png)

根据文件名的不同，高亮代码的方式也不一样。

## 4.1 搜索功能

![image-20201230143730493](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230143730493.png)

该搜索是根据你gist仓库的描述进行搜索。

## 4.2 创建

![image-20201230143858223](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230143858223.png)

私人（secret gist）：仅自己能看到或者通过URL进行访问。

公共（public gist）：所有人都可以看到和搜索到你的gist。

## 4.3 分享

![image-20201230144104604](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144104604.png)

gist还提供了大量的功能，比如直接嵌入你的HTML页面中，分享，Clone等功能。

## 4.4 历史修改

![image-20201230144252339](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144252339.png)

每一个gist仓库的每一次修改都会被记录下来，跟Git一样，随时可以查阅到曾经修改了什么地方。

## 4.5 markdown

![image-20201230144416760](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144416760.png)

gist还支持书写markdown，只需要将文件后缀名改为`.md`就可以使用markdown语法进行记录笔记。

**推荐配合：Gist Markdown Preview插件使用**

![image-20201230144529937](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144529937.png)

安装后会在编写markdown时多出一个按钮：

![image-20201230144604407](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144604407.png)

点击它就可以进行预览markdown。

![image-20201230144627987](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144627987.png)

## 4.6 WebStorm配合插件

![image-20201230144730627](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144730627.png)

安装该插件后，在你需要插入代码片段的地方点击右键：

![image-20201230144827325](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144827325.png)

选择Insert Gist。

![image-20201230144911314](https://source.cclliang.com:4433/public/image/other/程序员都应该了解的代码片段管理神器：gist/image-20201230144911314.png)

在弹出来的窗口中选择需要插入的代码片段，点击Insert，可以直接将代码片段插入到当前正在编辑的文件中，非常的方便，可惜的是没有提供搜索功能，如果代码片段太多，找起来还是有难度。

# 5. 最后

gist非常的简单易用，几乎使用过一次就能上手，并且使用过一次后你就会爱上它，因为它简洁、无广告、功能齐全，对于一个程序员来说，可以满足日常编程需求。

但缺点是不能直接进行访问。

