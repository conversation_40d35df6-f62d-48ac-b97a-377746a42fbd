---
title: "新的前端周刊上线了！"
date: 2025-06-15 22:35:15
categories:
  - "杂谈"
tags:
  - "杂谈"
---


新的前端周刊网站[RenderedWeekly（每周渲染）](https://renderedweekly.com)已经上线了，欢迎大家访问。

前几天我一直在思考可以利用AI来做什么，然后突然想到依赖AI的信息整理能力，可以做一款前端周刊，在中文互联网上，前端相关的周刊很少，知名的就那么几个，所以我就想自己做一个。

## 目的

以前我觉得英文不重要，但随着时间推移以及技术的增长，我发现越来越多的技术文章都是英文的，很多工具的更新文档也是英文的。

英文那边的文章不像中文这边一样集中在几个平台上，比如：大多数人都会将文章发布到掘金、知乎上，我以前也是，发布了很多文章到掘金、知乎上，虽然由于当时的水平有限，但还是获得了几十万的总阅读量。

但是随着我的技术增长，对于文章的质量就越来越挑剔，我发现目前很多文章的质量都不高，还有一大批文章都是传播焦虑，我不太喜欢这种感觉，所以我就慢慢地很少再逛这些平台。

## 鸽的两年半

我的更新整整断了两年半，我发现在这个博客中，虽然留言量不是那么多，但是还是有很多朋友在关注我。

其实我的断更是从入职新公司开始的，随着技术的增长，就想写一些更加深入的文章，例如源码理解、原理分析、性能优化、架构设计等，但是这些文章的难度很大，需要花费大量的时间去研究，而且还需要花费大量的时间去写，每次写了不到一半就放弃了。

其间我想开一个新的博客，专门研究React、Vue架构，但是每次还没有怎么开始就放弃了。

连博客的架构都在这些时间内更新了几次，相关的内容却毫无进展。

在鸽的两年半里面，我也被其它的事情分散了注意力，最开始是因为筹备婚礼，然后是学习摄影，后面是学习心理学、英语等，这些事情都让我花费了大量的时间，导致我无法专注于写作。

## 新的开始

由于其它事情分散了我的注意力，所以我对技术的研究也停滞了，突然有一天我发现这样不行，因为人活着是要吃饭的，我不能因为其它的事情导致我吃不上饭。

经常上网的朋友就明白，每天会从互联网上获取到大量的焦虑，哪个公司又在裁人，哪些人又失业了，谁谁谁又找不到工作了，AI现在已经发展到让多少人失业了。

贩卖焦虑总是可以收获到很多流量，也有非常多的人就吃这一套，自己已经焦虑的不行了，还就喜欢看这些文章。

到年初的时候我意识到我应该为我的本职工作做些什么，不能再被其它的事物影响了本质工作。

### 接触 AI

说实话挺惭愧的，从ChatGPT出来的时候，我就申请了账号，但是一直没有意识到AI可以干什么，我所在的公司的研发部是一个流动性很低的部门，大部分都是干了好几年的老员工，在前两个月的时候，我还是部门里面入职最晚的人。

流动性低就导致了思想的不活跃，新的技术、新的思想、新的理念就很难在部门中诞生，直到今年年初机缘巧合之下我被调到了另一个部门帮忙，这时我才发现别的部门已经用上了 TypeScript + Vue 3 的组合了，而在我的理念中Vue 3还不稳定，不能用于生产环境。

直到有一天，Github Copilot 发布了 TabTabTab 功能，我们内部讨论了一下，然后我在网络上面搜索，发现了一款新的编辑器 Cursor。

好吧，前文中也提到了，为了避免焦虑，我已经很长的时间没有去逛国内的技术网站了，所以我年初的时候才接触到 Cursor。

我之前一直是 JetBrains 的忠实用户，但是 Cursor 的 TabTabTab 功能让我非常震惊，因为 Github Copilot 的 TabTabTab 仅仅是发布了一个预览版，而 Cursor 已经可以稳定使用了。

后面一发不可收拾，我发现了AI不仅可以用来编程，还可以用来了解信息，我又花了很长时间去研究AI，了解了各大公司的旗舰模型，它们的优势分别在哪儿。

在快速更新了几篇文章后，我发现我又遇到了瓶颈，我不知道自己该写什么，我甚至都不知道我应该从哪儿获取到前端最新的知识。

## 新的前端周刊

直到前两天，我突然想到了一个主意，我可以在AI的帮助下，每周定期整理出一份前端周刊，这份周刊将会包含前端最新的技术、最新的理念、最新的思想、最新的产品。

于是说干就干，至于周刊的基础架构，我选择了 Astro 作为框架，至于为什么我选择了 Astro，而没有选之前我了解过的Meta的Docusaurus框架，这个我会在 Astro 独立的文章中详细介绍。

然后是周刊的命名，我选择了 RenderedWeekly 这个名字，中文名叫做“每周渲染”，“Render” (渲染) 是前端工作的核心动作，将代码变为用户可见的界面。这个名字的含义是每周都会将前端最新的技术、最新的理念、最新的思想、最新的产品变成用户可见的界面，展示给读者。

但其实随着两天的筹备，我发现准备周刊是一个非常耗时的工作，仅仅第一期，我已经准备了两天的时间，可能是因为万事开头难，我需要搜集大量的信息，然后进行整理，然后进行筛选，然后进行排版，然后进行发布。

## 对于未来的展望

当然是希望可以坚持下去，每周都可以为大家带来一份前端周刊，希望可以为大家带来一些有价值的内容，同时也希望能够在周刊的领域中 RenderedWeekly 能够占有一席之地。

我个人还是非常看好这个周刊的前景，从我给了它一个独立的域名就可以看出来。

## 最后

最后， RenderedWeekly 第一期已经上线了，欢迎大家访问 [RenderedWeekly](https://renderedweekly.com/weekly/1) 查看。
