---
title: "本来就是开发神器的WebStorm配上插件更不得了"
date: 2021-2-26 22:27:23
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


之前的一篇文章介绍了vscode好用的插件，但是我平时用的最多的还是WebStorm，就我个人而言，我还是比较喜欢WebStorm的，其实即便是不装任何插件，它已经足够好用，但是装上插件，让它变得更好用吧！

# 1. 实用插件

## 1.1 [CodeGlance](https://plugins.jetbrains.com/plugin/7275-codeglance)

![Screenshot 1](/images/other/screenshot_16821.png)

推荐指数：`***`

在右侧生成一个代码缩略图，用过vscode的朋友肯定很熟悉了，但其实实用度也并没有太高，很多时候我都没有用到这项功能，反而有时候觉得它还比较碍事，遮挡住代码显示。

## 1.2 [Rainbow Brackets](https://plugins.jetbrains.com/plugin/10080-rainbow-brackets)

![Screenshot 2](/images/other/screenshot_17373.png)

推荐指数：`*****`

实现括号配对，但是在白色主题下显示不明显，一定要使用黑色主题这款插件才会发挥出它的效果。

## 1.3 [Translation](https://plugins.jetbrains.com/plugin/8579-translation)

![Screenshot 1](/images/other/screenshot_17785.png)

推荐指数：`*****`

非常好用的翻译插件，在编写代码的时候很多情况都需要用到翻译功能。

点击状态栏的翻译引擎图标或者使用快捷键 `Ctrl + Shift + S`（Mac OS: `Control + Meta + Y`）可以快速切换翻译引擎，目前有谷歌翻译、有道翻译和百度翻译。

使用该插件，可以直接在WebStorm中内嵌一个翻译界面。

## 1.4 [IDE Features Trainer](https://plugins.jetbrains.com/plugin/8554-ide-features-trainer)

![Screenshot 2](/images/other/screenshot_22871.png)

推荐指数：`***`

会对IDE中的一些快捷键，以及实用功能进行交互式教学，从而达到抛弃官方手册，让你书写代码的效率更上一层楼。

## 1.5 [String Manipulation](https://plugins.jetbrains.com/plugin/2162-string-manipulation)

![Screenshot 2](/images/other/screenshot_16015.png)

推荐指数：`*****`

提供了非常多的处理字符串的功能，比如换成`camelCase`驼峰命名，换成`kebab-case`命名、`PascalCase`命名，还有各种处理字符串、切换大小写、对字符串进行排序等等功能，实用性可以说是非常高。

## 1.6 Chinese (Simplified) Language Pack / 中文语言包

推荐指数：`*****`

**Jetbrains官方出的中文语言包**，以前的英文界面很多功能我都看不懂，所以错失了很多有用的功能。自从用了中文语言包后，虽然最开始从英文变成中文可能会出现找不到选项的情况，但是使用久了是真的香！而且官方还在快速迭代该中文语言包，汉化度越来越完善。

强烈推荐英文不好的朋友使用。

## 1.7 .env files support

推荐指数：`*****`

在使用Webpack进行打包的时候，我们会有一些环境变量，在编写代码的时候，如果安装了该插件，就会提示环境变量文件中所拥有的环境变量。

## 1.8 Comments Highlighter

![Screenshot 1](/images/other/WebStorm好用的插件（二）/screenshot_20553.png)

推荐指数：`****`

丰富代码注释高亮样式，可以突出你代码中比较需要注意的一些文字。

## 1.9 Gist Snippet

![gist](/images/other/WebStorm好用的插件（二）/gist.gif)

推荐指数：`***`

Gist插件，可以快速的在IDE中插入你Gist中的代码片段！

## 1.10 Jenkins Control Plugin

推荐指数：`***`

如果你使用了Jenkins，那么你可以直接通过该插件控制Jenkins打包，当然有些公司的Jenkins一般是由运维或者后端人员进行操作，前端根本不需要自行去配置Jenkins，但是我还是推荐前端开发者学一学Jenkins，因为如果你的个人项目数量比较多，使用Jenkins自动化部署还是非常方便的，可以省下非常多的时间。

关于Jenkins，我后面也会整理一篇我是如何使用Jenkins部署前端项目的文章。

## 1.11 Json Parser

![JsonParser](/images/other/WebStorm好用的插件（二）/JsonParser.gif)

推荐指数：`**`

被Chrome插件**前端助手**完爆的一个功能，推荐直接使用前端助手，该插件最方便的地方就是可以直接在IDE中使用不需要打开浏览器，但是前端人有不打开浏览器的时候嘛...

## 1.12 Redis

推荐指数：`****`

可以直接操作Redis的插件，最主要是方便，目前很多主流的操作数据库的软件很少有支持Redis的，缺点是要付费，但是每次IDE更新（？不确定）就可以重新进行30天的免费试用。

## 1.13 SonarLint

推荐指数：`****`

代码风格检查工具，跟Eslint很相似，有些老项目无法使用Eslint时就可以使用它，新项目也可以和Eslint同时使用，几乎没有什么冲突的规则。

如果你严格按照代码质量检测工具进行编写代码，那么你会编写出一手很漂亮的代码。同时消灭一些浅在BUG，并且以后别人接手你的代码时，如果别人用了代码质量检查工具，不会满篇的警告和错误。

## 1.14 Docker

![image-20201227133414338](/images/other/WebStorm好用的插件（二）/image-20201227133414338.png)

推荐指数：`****`

可以很方便的操作Docker镜像和容器，并且可以看到容器内部的目录结构、挂载数据卷的位置、容器的配置等信息，前端开发者几乎很少会使用到Docker，后端和运维可能用的会比较多一点。

该插件为捆绑安装，可以直接在设置里面的构建处找到。

# 2. 不实用插件，但是酷

## 2.1 [Dmitry Batkovich](https://plugins.jetbrains.com/plugin/8575-nyan-progress-bar)

![image-20201007003944178](/images/other/image-20201007003944178.png)

推荐指数：`****`

将所有进度条变成彩虹条。

## 2.2 [Background Image Plus](https://plugins.jetbrains.com/plugin/8502-background-image-plus)

推荐指数：`***`

可以自定义编辑器的背景图片。

## 2.3 [Material Theme UI](https://plugins.jetbrains.com/plugin/8006-material-theme-ui)

![Screenshot 1](/images/other/screenshot_17526.png)

推荐指数：`***`

修改编辑器UI。

## 2.4 [Power Mode II](https://plugins.jetbrains.com/plugin/8251-power-mode-ii)

![Screenshot 1](/images/other/screenshot_15884.png)

推荐指数：`***`

让你输入代码时拥有很多酷炫的特效。

# 3. AI代码助手介绍

随着机器学习的快速发展，现在在代码领域也有AI辅助你写代码，目前来说，跟IDE的代码提示很相似，但是它会根据你已经输入过的代码进行学习，如果你下次再输入类似的代码，它就会出现提示，帮助你快速补全代码。

## 3.1 Codota AI Autocomplete for Java and JavaScript

![Screenshot 1](/images/other/WebStorm好用的插件（二）/screenshot_19077.png)

推荐指数：`****`

我最开始使用的第一款AI代码助手，优点是提高了编码的效率，缺点是仅仅支持Java和JavaScript，并且该插件受到网络环境的限制，经常性失效，不会出现提示，不过有提示的时候确实挺好用的。

## 3.2 Tabnine AI Autocomplete: JavaScript C Python Ruby Rust Go PHP...

![With Tabnine](/images/other/WebStorm好用的插件（二）/with-tab9-JB-MP.gif)

推荐指数：`***`

同样是上面那个公司的作品，貌似不会受到网络环境的限制，我用的不是很多，几乎支持当前所有的主流编程语言，提示不会出现不生效的情况。

## 3.3 Kite AI Code AutoComplete: Python, Java, Javascript, HTML/CSS, Go, C/C#/C++

![img](/images/other/WebStorm好用的插件（二）/with_without_kite_v1.png)

推荐指数：`***`

我目前正在使用的AI代码助手，不会存在网络的限制，提示也非常的快，缺点也非常明显！提示的太多有时候会和IDE自带的提示冲突，而且有些自带的提示会被挤到很后面去，选择起来并不是很方便，并且它还需要下一个AI引擎，该引擎必须常年打开，占用的内存量还不低。

![image-20201227131026127](/images/other/WebStorm好用的插件（二）/image-20201227131026127.png)

推荐特别依赖代码提示和机器配置不错的朋友使用。
