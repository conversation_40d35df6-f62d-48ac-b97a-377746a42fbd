---
title: "掌握Cursor高级功能：.cursorrules与Notepads详解"
date: 2025-03-22 16:00:00
categories:
  - "杂谈"
tags:
  - "Cursor"
---


这是Cursor系列的第三篇文章，主要深入探讨Cursor的两个强大功能：`.cursorrules`文件和Notepads功能。这些工具能显著提升你的AI辅助编码体验。

## .cursorrules：为AI定制项目规则

`.cursorrules`文件是直接创建在项目根目录下的文件，它可以帮助你精确控制Cursor的AI行为，确保生成的代码始终符合你的项目标准。

它的用途是约束Cursor的AI行为，制定一些规则，让Cursor生成代码的时候始终遵循这些规则，它与设置中的`User Rules`功能一致，唯一不同的是`.cursorrules`文件是只作用在当前项目中的，而`User Rules`中的规则是全局生效的。

`.cursorrules`没有任何固定的格式，你只需要按照自己的需求来制定规则即可，也可以参考网络上的规则，比如[Cursor Rules](https://cursor.directory/rules)这个网站中就有很多规则，你只需要根据你的项目选择的架构，选择对应的规则即可。

使用`.cursorrules`的主要优势包括：
1. **定制AI行为**：根据项目需求调整AI响应，确保代码建议更加准确相关
2. **保持一致性**：定义编码标准和最佳实践，让AI生成的代码符合项目风格指南
3. **上下文感知**：为AI提供项目架构、常用方法或特定库的重要上下文
4. **提高生产力**：通过明确的规则减少手动编辑，加速开发流程
5. **团队协作**：共享的规则文件确保团队成员获得一致的AI辅助

### 创建和使用.cursorrules

`.cursorrules`没有固定格式，你可以根据项目需求自由定义规则。有两种常见的创建方式：

1. **参考现有模板**：你可以从[Cursor Rules](https://cursor.directory/rules)等网站查找适合你项目的规则模板

2. **让AI生成规则**：在项目中创建`.cursorrules`文件，然后使用Agent模式生成项目专属规则

在使用模板时，务必仔细审查规则内容，确保其不会引入不需要的技术栈。例如，以下规则可能会强制AI使用你不需要的UI库：

```
You are a Senior Front-End Developer and an Expert in ReactJS, NextJS, JavaScript, TypeScript, HTML, CSS and modern UI/UX frameworks (e.g., TailwindCSS, Shadcn, Radix). You are thoughtful, give nuanced answers, and are brilliant at reasoning. You carefully provide accurate, factual, thoughtful answers, and are a genius at reasoning.

...other rules...
```

如果你使用了这个规则，那么你在使用Agent模式时，Cursor会优先使用Shadcn、Radix这些UI库，可能你根本就不想使用这些UI库，所以每一个规则文件还是建议你好好读一下，不然会引入很多你不想使用的库。

其实还有另一个方法，直接让Cursor根据项目生成一个规则，比如我总结了一个规则大纲。

```
你是专注于现代 Web 前端开发的专家，擅长使用 

项目简介

技术架构

目录结构

命名规范

组件规范

样式规范

引入规范

注释规范

提交规范
```

在项目中创建一个`.cursorrules`文件，然后复制上面的内容到文件中，然后使用Agent模式，输入`根据项目生成一个规则`，Cursor就会根据你的项目，生成一个规则文件，然后在在生成的规则文件中改一改就行。

## Notepads：项目知识管理与上下文共享

Notepads是Cursor中一个强大的功能，它超越了简单的笔记工具，为AI提供丰富的上下文信息，特别适合复杂项目的知识管理。

### Notepads的主要特点

与传统笔记或`.cursorrules`相比，Notepads具有以下优势：
- **上下文共享**：在不同编辑器界面间无缝共享上下文
- **文件附件**：可以附加文档和参考文件（`.cursorrules`中不可用）
- **动态引用**：使用`@`符号链接到其他资源
- **灵活内容**：以适合你需求的方式组织和结构化信息

### 实际应用场景

Notepads适合记录以下内容：
- 项目架构决策和设计原则
- 开发指南和标准
- 可重用代码模板
- 团队特定的规则和约定
- 常用的Prompt模板

例如，你可以为特定任务创建专门的Notepads：

```md
# API开发指南

## 端点结构
- 遵循RESTful约定
- 基础URL: `/api/v1`
- 资源命名使用复数形式

## 认证
- 基于JWT的认证
- Token格式: Bearer {token}
- 需要刷新令牌机制

## 响应格式
{
  "status": "success|error",
  "data": {},
  "message": "可选消息"
}

## 附加引用
@api-specs.yaml
@auth-flow.md
```

### 如何使用Notepads

使用Notepads的基本步骤：
1. 点击Notepads部分的"+"按钮
2. 为notepad起一个有意义的名称
3. 添加内容、上下文、文件等相关信息
4. 在Chat或Composer中使用`@`引用你的notepad

这也是我经常使用的功能。当你有复杂需求时，可以创建一个详细的notepad，然后在Chat模式中直接用`@`引用这个笔记，Cursor会根据笔记内容生成相应代码。

例如，你可以创建不同类型的Prompt模板：
- 代码重构Prompt
- 性能优化Prompt
- 问题排查Prompt
- 单元测试生成Prompt

这使得你可以快速应用常用的工作流程，而无需每次重新编写相同的指令。

## 总结：构建个性化AI编码体验

当大家都使用相同的工具和模型时，掌握这些高级功能可以让你的AI助手更加理解你的意图，更好地满足你的特定需求。`.cursorrules`文件和Notepads功能是实现这一目标的两个强大工具。

有效使用这些功能的关键是：
1. **持续改进**：随着项目发展不断调整和完善你的规则
2. **记录有效模式**：遇到效果好的Prompt和工作流程，立即保存到Notepads中
3. **团队共享**：在团队中分享有效的规则和模板，建立统一标准
4. **上下文意识**：学会为AI提供精确、相关的上下文信息

通过这些实践，你将能够更快速、更准确地实现开发目标，充分发挥Cursor的AI能力。