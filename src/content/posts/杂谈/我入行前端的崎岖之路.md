---
title: "我入行前端的崎岖之路"
date: 2021-9-5 12:07:38
categories:
  - "杂谈"
tags:
  - "杂谈"
  - "hide"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

本篇文章是讲述我如何成为一个前端开发者的辛酸历程，希望对于现在想要转行或者还没有毕业的同学给予一点参考，首先我成为前端开发者的经历是非常曲折的，并不是太顺利，所以希望现在感觉工作或学习不如意的人，能够再坚持和努力一下，或许就能达到你的目标。

# 1. 经历

我转行做前端开发已经有一段时间了，我个人是土木工程毕业，跟计算机行业完全没有任何关系，当时高考考得不好，然后赶上房价暴涨房地产行业欣欣向荣的时机，被家里人强制报了土木工程专业，其实我当时想报计算机专业。

而到了大学，天天以游戏和看直播度日，有课就去上，没课就玩，完全不会去主动学习。

实习的时候去了工地，呆了不到一周发现实在不是我想要的生活，那个时候又赶上房地产行业的衰落，很多相关的公司长时间拖欠工资，让我更没有兴趣进入房地产行业。

毕业后不知道干什么工作，于是机缘巧合下投递了一家游戏公司，进到里面当了3个月的客服，那个时候工资不到2000，3个月后因为工资太低，还有就是整个公司除了客服全是双休，于是离职跑路。后面我想找游戏策划岗位。

过完年经过长达3个月的面试，因为没有任何策划相关的工作经验，而且自己对于策划一点都不了解，所以投递出去的简历也是石沉大海，那段时间**连面试客服岗位也全部被拒。**

后面我投递了一个游戏测试岗位，因为我想到我在之前3个月的工作经历中，帮助策划做了一些测试相关的工作，我以为测试就是像一个玩家一样玩游戏，后面有家公司的HR打电话过来问了很多问题，就是些：你为什么想做测试、你知道测试是干什么嘛？测试没有你说的这么简单。

聊着聊着他还说了句：客服都不要你啊！当时他的这句话刚好戳中了我，当场就和他吵了一架。

直到6月份，我接到了完美世界的通知，给的职位是**IM**即在线客服，进去就培训了快一个月，接线的过程其实并不顺利，尤其是在下午7、8点钟的时候，有非常多难缠的客户气得我砸桌子和键盘，所以在这里劝大家一句：**如果不是实在无路可选的情况下，不要去做客服！**

因为客服的权限是非常低的，而且必须要按照规定的话术进行回答，如果回答的答案不符合话术以及当天的交接，是会扣绩效分，直接影响到你的当月绩效工资，而且客服的整体工资是非常低的。

还好后面因为**违规处理组**缺人，我被调入违规处理组，不再接线，在违规处理呆了快1年半，由于当时通宵班太多了，而且发现根本看不到前途，所以这个时候就有了想要培训程序的想法。

# 2. 培训

后面说通了家人，由于我是土木工程毕业，所以身边没有认识的人在做程序，完全没有一个人能够给我参考意见，而那个时候我非常喜欢游戏，所以我就去培训了**unity3D**，想要从事游戏开发行业，培训周期是4个月。培训费是1万8。

在培训的时候我**自身也并不努力**，几乎每天仅仅学习上课时教的知识，下午下课后就不再学习，而且当时那个培训机构只有**上午1~2个小时在讲课，其它时间都是自己实操的时间**，所以对于我这种**0基础的人就少了非常多的基础知识**。

4个月几乎是一转眼就过去了，我当时做了一个[保卫萝卜](https://www.bilibili.com/video/BV1HW411d7DM/)，满怀信心的到处投递简历然后去面试，结果到处碰壁，由于基础不牢，所以即便是**薪资只有2000左右的岗位依然被拒。**

当时是4月开始培训，培训到8月底，后面3个月都在找工作，最后好不容易**以实习生的身份进入了一个较大的游戏公司**，但是！这公司有一个月的考核期，每周有一次测试，就算全部通过最后工资也只有2000多。

在第一个星期，我目睹了该公司的程序996，而且当时没有任何一个人来指导我，就发了几本电子书让我看，好不容易到了第一次考核，拿到考题我直接傻眼了，虽然现在回想起来确实是一些基础知识，但是对于当时的我来说，基础又差，然后又对于996的恐惧，呆到下午2、3点的时候我还是决定跑路。

这个时候的我对程序已经心灰意冷，时间已经来到了12月份，整天开始混日子，也不再学习程序的相关知识，开始找一些审核岗位，但是我觉得找审核岗位那我培训的钱又浪费了。

混到了来年2月份，家里面介绍了一份工作，是一个**加我一共5人的培训办证学校**，其中有2个还是老板亲戚，工资也只有2400块，相当于1天100块，由于我感觉那份工作没有什么盼头，于是在考虑其它的工作机会，由于受到了一个朋友的影响，看他做淘宝运营收入还是不错，达到了10k，就准备找一个淘宝客服慢慢转为淘宝运营。

而这一次面试客服倒是挺顺利的，直接就面试上了，马上就和家里人说我这里要离职然后去一家新公司上班。

结果去到那家电商公司，早上有站立会议，还要**喊口号**，就是那种销售岗位或者火锅店每天喊的那种......这让我感到很难受，**下午的时候又安排我打电话催别人评论**，我本人是一个非常讨厌打电话的人，而这个时候那个培训办证学校的老板又找我聊，叫我回去，于是第二天我告别了这个电商公司，又厚着脸皮回到了培训学校。

# 3. 转折点

这个时候我感觉再这样下去已经看不到未来，于是又开始学习程序相关的知识，但是我已经决定不在游戏开发上浪费时间了。

这里就说一下游戏开发到底有什么问题，游戏开发的工资是要高于普通开发岗位的（例如前端、后端）而且游戏卖的好还有奖金，但是！游戏公司本来就少，找工作就比较困难，当时我几乎投递了一大半的**成都的unity3D岗位**，而且游戏开发岗位很容易007，之前在培训的时候**有人出来工作后直接就住在公司，醒来就工作。**而且后面我发现我对游戏的热情逐步减弱。

所以我开始自学Python，教程就是[小甲鱼的Python视频](https://www.bilibili.com/video/BV1xs411Q799?from=search&seid=9041941697782899831&spm_id_from=333.337.0.0)。

这个时候因为工作中常用Excel，于是我就写了一些VBA自动操作Excel的宏，来提高工作效率，同时使用UiBot来做一些自动化工作，挤出时间来学习Python。

但是Python学到了6月份我发现再学Python依然会找不到一份程序员工作，因为现在Python之所以火，完全是因为大数据、机器学习，而这两个岗位对学历是有非常高的需求的。

失去了方向的我就开始学习英语，一直到8月份，转折点来了，某个游戏群里面发了一段代码，他们说是JavaScript的代码，这个时候我终于开始了解前端和后端分别做什么，当时[小甲鱼又出了一个前端视频教程](https://www.bilibili.com/video/BV1QW411N762?spm_id_from=333.999.0.0)，当时的室友又说他的朋友做前端现在还不错。

我当时就跟所有人一样，觉得**前端的钱途不如后端**，但是！从入行难度来说，前端可能更好找工作一点，现在回想起来，我非常庆幸当时选择了前端，因为现在后端内卷非常严重，学习Java的人实在太多了。

小甲鱼的HTML教程更新的非常慢，于是我又找到了另一个教程[pink老师的视频](https://www.bilibili.com/video/BV14J4114768?from=search&seid=6013803213485967808&spm_id_from=333.337.0.0)。非常基础非常详细！虽然我培训了游戏开发，但是正如我前面所说，每天老师只会讲1个小时左右，所以很多关于编程的基础知识我是不知道的。

这时时间来到了9月份，当时我已经不想再呆在培训办证学校了，就想离职。跟家里说了后家里面强烈反对，于是跟家里吵了一架，但是吵架后我还是没有离职，于是天天利用上班的时间开始自学前端，由于我之前写了很多自动化脚本，所以效率特别高，老板安排的活几下就做完了，做完后就开始看视频学习，好在老板也不管。

HTML+CSS+[JavaScript](https://www.bilibili.com/video/BV1yb411x7Mj?spm_id_from=333.999.0.0)学习完了后，由于没有指导者，所以我就开始在网上搜我下一步应该学习什么，经过一番搜索我又开始学习JQuery，就是[李南江老师的jQuery视频](https://www.bilibili.com/video/BV17W41137jn?from=search&seid=3743054201041876940&spm_id_from=333.337.0.0)，学完jQuery后我觉得我应该学习一个前端框架才能够找到工作，网上一搜太多的前端框架，不知道学什么。

后来机缘巧合下我开始学习[Vue](https://www.bilibili.com/video/BV1rE411x7vd?p=171)，Vue学习完了后又开始学习React。

这个时候时间来到了12月份，我实在是不想在培训办证学校呆下去了，提出了离职，就开始闭关学习，准备开年后就找工作，没有想到第二年遇到了疫情，于是我又学习了Java、TypeScript，因为我之前学习过C#，所以Java、TypeScript上手也很快，但是我觉得Java的Spring Boot框架太过于重量级，后面就放弃了。

这个时候我发现了Cocos Creator于是我用它做了一个[快乐鸟](https://game.cclliang.com/HappyBird-cocos/)和一个[飞机大战](https://game.cclliang.com/airplanewar/)小游戏。

时间来到了5月份，我开始做一些自媒体，但是经过一番尝试，发现赚1块钱都很难，于是这个时候我开始分享一些我的学习笔记以及心得，也就有了我的[个人博客](https://www.cclliang.com/)。

6月份我开始投递一些简历，这个时候我已经失去了学习的方向，因为没人指引，我逐步开始停止学习，投了非常多的简历，中途只有一家公司叫我去机试，我去了后看了看题，其实现在的我回想起来是非常简单，但是当时我没有实战经验，做了2个多小时，一直从9点多做到11点做，有一些功能依旧没有实现，于是进入面试阶段，问了我一些问题，现在回想起来那个面试官的水平也不怎么样。

时间来到了8月底，又有一个面试通知，本来我觉得那个公司**在住宅里面不是很靠谱**，但是因为各种原因我还是去面试了，面试的时候问的问题还是比较简单，我最后叫价5k，晚上的时候就通知让我下周一入职但是月薪砍到4.5k，试用期打8折就是3.6k。

# 4. 入职

准备入职的那几天心里还是很慌的，因为我完全不知道多人如何合作开发，所以怀着忐忑的心情入职了，入职后一看到项目代码感觉完全看不懂，就觉得写这个的很厉害，但是发现一部分代码和另一部分代码完全像是两个人写的（事实也是如此，当时那个项目直接用了[人人的一个框架](https://gitee.com/renrenio/renren-fast-vue)）。

后面我接到的第一个项目就是写一个可以拖拽的落地页生成器，这个项目我就用的TypeScript+Vue2.X写的。

后来转正的时候说给不了我那么多工资只能给3.8k，也就是转正只比没转正多200，当时我就忍了，那公司还有每周打扫卫生的规定，如果不打扫则第一次扣20第二次40第三次80，必须上班之前打扫。到了12月份直接出了一系列的新规定，居然还有一部分工资是你不打扫卫生就要扣的，离谱的是发现一个BUG也会扣钱，有个同事因为没有打扫卫生被扣了几百。

在此期间，有一个后端告诉了我很多东西，比如Jenkins，Docker这些东西，然后我就去看相关的教程，我还学习了一些Linux基础。

到了12月底的某一周，周一的时候告诉我们要改一些BUG，结果开会的时候说的全是一系列的新需求，也没有产品，也不说怎么做。

比如某一个需求就是：新增一个用户引导功能。就这么一句话，至于哪些地方要引导，引导提示是什么完全不告诉你，到了周五的时候告诉我们这些需求没有做完，周六要加一天班（不给钱）于是我和另一个前端加一个后端和他们大吵一架，当场就离职走人。

# 5. 再次入职

离职后我又看了一些uni-app的知识以及Flutter相关的知识。

第二年3月份，我又开始投递简历，当时想的是只找自研公司，投了很多简历依然是石沉大海，好不容易有一家公司发起了面试邀约，最后没有面试上，隔了一周又有一家公司邀请我去面试，当时我问是不是自研公司，她说有自己的产品，于是我就去面试了，最后叫价8k（从我目前看来，我当时是喊低了，因为我后面当面试官时，很多人还不如我，都喊得14k，因为当时我并不知道我的价值）。

后面成功的面试上，入职之后招我那个前端就离职了，之前的前端老员工到此全部离职，后面有一个新项目我开始使用React+TypeScript进行编写，目前项目已经有700+接口，几乎是我一个人完成的（有个同事写了个登陆页就被开了）。

这里总结一下我的学习路：

- HTML+CSS+JavaScript+Vue：学到这里就可以开始找工作了。
- TypeScript+React+Webpack+Nodejs：进阶需要学习这些。

如果你要玩服务器，那你还得学习

- Linux基础。
- Docker容器。
- Jenkins自动部署，解决项目一多，部署上面的麻烦事。

# 6. 前端和后端

最后说一下前端和后端，后端现在太卷了，在**外包公司后端甚至还要充当商务，还要去对接客户，客户有什么问题还会不分时间的打电话来问你，驻场开发也是后端占多数，甚至招不到前端的时候还得由后端来做一些前端的活**，而且现在前端的工资已经逐渐开始超过后端，因为前端实在是太缺了，我当时招React+TypeScript基本上都招不到。

为什么会造成这种现状，到现在很多人觉得前端都没有什么难度，被戏称为**切图仔**，在前后端还没有分离的时候需要把写好的静态界面交给后端，后端进行部署，而且当时前端写不了太复杂的界面，这也导致前端比后端工资低很多，前后端没有分离的时候前端技术并不复杂你会HTML+CSS+JavaScript+JQuery就可以找到工作，所以大部分后端也可以担任前端的工作。

自从前后端分离开始，前端技术正式大爆炸，抛弃了jQuery使用现代js框架后，前端的复杂度呈指数上涨，你现在叫一个2、3年前写过前端的后端来写前端项目，他不一定看得懂。

直到现在前端都还在被轻视，所以现在**依然是入行前端的好时机，因为前端的技术爆炸还在持续。**

# 7. 外包与自研

为什么当时我想要找自研公司，如果是一家正常的产品公司，那么他的人员一定是比较齐全的，比如**产品、测试、开发、UI**。流程也会比较严谨，从**产品调研->UI设计->开发入场->测试->上线**，这整个流程人员是非常完善的，而我现在在的这家外包公司，目前做的产品，则是产品调研的同时，UI和开发就开始做了，所以天天原型图都可能发生变化。

而且大部分外包公司一般是不把人当人的，因为公司的收入基本来源于外包，所以公司会接很多个外包项目，可能一个前端一个后端就要负责一个项目，而且外包公司一般请不起大牛，所以大部分身在外包的人，**代码规不规范，代码质量好不好，是没有人给你提意见的**，也没有人有那个时候来帮你审核代码，这就会直接导致别人接手到你的代码时可能会非常困难，还会导致你的代码越来越放飞自我。

作为一个转行者来说，想进一家大的自研公司往往不如进一家外包公司容易，所以均衡利弊还得自己考虑。

# 8. 最后

我的这一路其实是非常坎坷的，中途承受了非常多的压力，所以如果你是一个计算机相关专业毕业，那么恭喜你，你已经比很多人有了更高的起点，建议你不要放弃这行。

如果你是一个想转行的人，那么你就必须付出更多的努力，前端目前的人才缺口还是很大的。
