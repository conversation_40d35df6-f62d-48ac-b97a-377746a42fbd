---
title: "WebStorm-2021.2抢先版带来了什么便利"
date: 2021-7-17 00:22:13
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

WebStorm一直是我最喜欢的前端开发编辑器，它集成了目前前端开发中几乎所有需要使用到的功能，在之前我也出过好几篇相关的文章，有兴趣的话可以看：

- [WebStorm各种便利的功能](/2020/10/17/杂谈/webstorm各种便利的功能/)
- [WebStorm各种便利的功能-代码重构](/2020/12/29/杂谈/webstorm各种便利的功能-代码重构/)
- [WebStorm好用的插件](/2020/10/09/杂谈/webstorm好用的插件/)
- [WebStorm好用的插件（二）](/2020/12/27/杂谈/webstorm好用的插件二/)
- [WebStorm强大的Git版本控制](/2020/09/21/杂谈/webstorm强大的git版本控制/)

之前因为我最喜欢的主题`One Dark theme`不支持最新的体验版2021.2，而且在最初的体验版中存在着大量的BUG，导致使用体验很差，其实2021.2的体验版从5月27日就发布了，到目前经过2个多月的更新，它比起2021.1已经拥有了非常多的新功能，本篇文章就来说一说那些让我觉得有用的新功能。

# 1. 新功能

## 1.1 [保存时重新加载](https://blog.jetbrains.com/webstorm/2021/05/webstorm-2021-2-eap-1/#browser_pages_reload_on_save)

在不使用打包工具的情况下，隔壁VSCode可以通过插件获得更改源文件后刷新界面的功能，而在之前版本的WebStorm中，也可以通过打开调试模式来获得这项功能，而现在WebStorm支持了保存时重新加载的功能，不用再羡慕隔壁的VSCode了。

![reload-on-save](/images/other/WebStorm-2021.2抢先版带来了什么便利/reload-on-save.gif)

## 1.2 [JSDoc中支持TypeScript类型](https://blog.jetbrains.com/webstorm/2021/06/webstorm-2021-2-eap-4/#support_for_typescript_types_in_jsdoc)

对于习惯了TypeScript的开发者而言，再让他们去使用JavaScript，往往会出现让他们非常难受的一些事情，比如大量的类型警告没有办法处理，使用对象时无法获取到对象中的属性提示，而JSDoc可以在一定程度上缓解这些问题（但依然无法全部解决）。

现在可以正确的使用下面的TypeScript语法作为变量的类型声明：

![typescript-syntax-support-in-jsdoc-2021-2](/images/other/WebStorm-2021.2抢先版带来了什么便利/typescript-syntax-support-in-jsdoc-2021-2.png)

## 1.3 [自动引入CommonJS模块](https://blog.jetbrains.com/webstorm/2021/06/webstorm-2021-2-eap-4/#auto_imports_for_commonjs_modules)

之前WebStorm对于自动引入的支持仅仅是针对**ES Module**，这对于Node.js用户来说相当的难受，虽然现在也可以使用某些办法让ES Module成为Node.js的引入方式。

而该次更新就添加了CommonJS的自动引入功能：

![auto-imports-for-common-js](/images/other/WebStorm-2021.2抢先版带来了什么便利/auto-imports-for-common-js.gif)

如果WebStorm不确定你使用的哪种模块导入方式，它还会进行询问：

![import-popup-webstorm-2021-2](/images/other/WebStorm-2021.2抢先版带来了什么便利/import-popup-webstorm-2021-2.png)

## 1.4 [Actions on save](https://blog.jetbrains.com/webstorm/2021/06/webstorm-2021-2-eap-5/#actions_on_save)

这个也是我比较期待的功能之一，简单的说就是在你进行保存的时候可以自动执行**格式化操作、删除无用的导入**等等功能，在以前只有通过Save Actions这个插件进行实现，现在WebStorm自带了该插件的部分功能。

![actions-on-save-in-webstorm-overview](/images/other/WebStorm-2021.2抢先版带来了什么便利/actions-on-save-in-webstorm-overview.png)

## 1.5 [重命名useState hooks](https://blog.jetbrains.com/webstorm/2021/06/webstorm-2021-2-eap-5/#rename_refactoring_for_react_hooks)

对于React用户是极好的一个功能，当我们在使用useState时，如果发现变量命名不合理想重新进行命名时，因为后面的set变量一般情况下都需要和前面的变量名进行对应，所以我们时常会需要修改两次，而本次更新则仅仅只需要修改一次，就能够重新命名useState，当然快捷键依然是`⇧F6 / Shift+F6`。

![rename-react-hooks](/images/other/WebStorm-2021.2抢先版带来了什么便利/rename-react-hooks.gif)

## 1.6 [改进Tailwind CSS支持](https://blog.jetbrains.com/webstorm/2021/07/webstorm-2021-2-beta/#improvements_for_tailwind_css)

我个人比较期待的功能，因为我在写项目的时候很多情况下都会引用Tailwind CSS，WebStorm在该版本之前，如果是使用了`className={}`的形式，则不会正确的弹出Tailwind CSS的提示，而在隔壁VSCode却可以获得正确的提示，当时我十分羡慕VSCode的正确提示功能，好在现在WebStorm改进了对于Tailwind CSS的支持。

![completion-for-dynamically-evaluated-values-tailwind](/images/other/WebStorm-2021.2抢先版带来了什么便利/completion-for-dynamically-evaluated-values-tailwind.png)

不仅如此，它还可以在`styled-components`这个库中得到正确的提示。

![tailwind-class-name-completion-in-styled-components](/images/other/WebStorm-2021.2抢先版带来了什么便利/tailwind-class-name-completion-in-styled-components.png)

## 1.7 [新的箭头函数模板](https://blog.jetbrains.com/webstorm/2021/07/webstorm-2021-2-beta-2/#new_template_for_adding_arrow_functions)

在现在的前端开发项目中，我们会大量的使用到箭头函数，新版本的WebStorm提供了新的箭头函数快速生成模板，只需要输入`co`，就可以得到箭头函数的快速生成提示：

![completion-for-arrow-function-with-template](/images/other/WebStorm-2021.2抢先版带来了什么便利/completion-for-arrow-function-with-template.gif)

# 2. 最后

无论你是使用的什么开发工具，都建议你对自己所用的开发工具了解的更深一点，发挥它的最大功效，提高自己的代码产出效率，这样才会有更多的时间划水去学习更多新的知识，而不能一味的成为一个花大量时间去写一些重复代码的码农。
