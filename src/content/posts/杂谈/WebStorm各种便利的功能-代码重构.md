---
title: "WebStorm各种便利的功能-代码重构"
date: 2020-12-29 11:38:00
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


# 1. 前言

不知不觉都消失一个月了，这个月除了忙之外，发生了好多好多事情，首先是Vue3的使用，通过Vue3给公司做了一个小项目，关于各种表单的填写。

然后自行开始学习Golang。

后面又是Redis的学习，现在仅仅会用，没有深入去了解Redis的各种原理以及高端用法。

对于docker的进一步了解，之前只是了解到如何使用docker启动一个容器，后面我又了解到了Dockerfile，Docker Compose。

还有通过内网穿透，可以直接远程连接主机，或者将电脑上面启动的一些服务，让外网也能访问到。

还有通过ssh连接win10，还有Jenkins的进一步使用，还有gist这个神器，专门管理代码片段...

到目前为止已经有太多学到的东西没有进行整理，刚好最近失了业，会逐步的一篇一篇文章将这些内容慢慢整理出来，以防止后面久了不用忘记怎么使用，又要重新翻找资料，毕竟学习不整理，那效率可是太低了。

说句题外话：之前我的个人家用电脑一直没有安装固态，因为我以前一直觉得固态没啥用，后面发现启动电脑，硬盘占用率常年百分百，卡到爆炸，尝试着装了一个固态硬盘，直接起飞！只能说：固态硬盘真香！

无关的话差不多就到这里，下面进入本篇的正题：在这一段时间我又学到了什么新的使用WebStorm的技巧。

上一篇文章：[WebStorm各种便利的功能](/2020/10/17/杂谈/webstorm各种便利的功能/)

# 2. 重构功能

重构是Jetbrains出品的IDE都具有的一个功能，并且是一个超级强力的功能！

**该功能非常方便！！！**

**为什么要重构代码？**

因为当时写代码时的技术限制、程序员本身水平限制、代码复用性差、代码可读性差、项目优化...这一系列场景，就需要重构代码。而Jetbrains的IDE系列就提供了大量重构代码的方式。

WebStorm的重构功能有很多，但是我只写出几个我经常用到的重构功能。

## 2.1 重构变量名

![image-20201229112709088](/images/other/WebStorm各种便利的功能-代码重构/image-20201229112709088.png)

常用指数：`*****`

主要用途：修改变量、方法、文件等名称。

解决痛点：有时候看到一个命名不是很合理，但是项目中很多地方都用到了它，不敢轻易去修改该名称，一旦修改可能会触发连锁反应。

快捷键：`Shift`+`F6`。

使用方法：将光标放在需要重命名的变量、方法上，右键-重构-重命名，根据提示完成修改后，项目中所有用到这个变量或方法的地方的名字都会被修改成新的变量名，同时该项功能还可以用来修改文件名。

## 2.2 移动文件

![移动文件](/images/other/WebStorm各种便利的功能-代码重构/移动文件.gif)

常用指数：`*****`

主要用途：改变文件路径，将一个文件移动到另一个目录下。

解决痛点：移动文件时该文件可能在很多地方进行引用，这就造成了如果一旦移动该文件，很多引用该文件的路径都要发生变化，跟上面的重构变量名一样，一一的去进行修改引用该文件的地方是很麻烦的，可能会出现漏修改的情况。

使用方法：左键选中文件直接拖拽到文件夹中就可以了，会自动将项目中所有引用到的地址都改成移动后的地址。

## 2.3 安全删除

![image-20201229112100459](/images/other/WebStorm各种便利的功能-代码重构/image-20201229112100459.png)

常用指数：`*****`

主要用途：删除一个变量或者一个方法甚至一个文件。

解决痛点：有时候想要删除一个看似没用的变量、方法、文件，就可以使用安全删除。因为有时可能看似没用的变量，你将它删除掉，可能会引发蝴蝶效应导致一个BUG的出现。

快捷键：`Alt`+`Delete`。

使用方法：将光标放在需要删除的变量或方法上面，点击右键-重构-安全删除，IDE就会自动搜索该变量或方法在其它哪些地方引用过，并且会一一的给你列举出来。

## 2.4 提取方法

![image-20201229110224937](/images/other/WebStorm各种便利的功能-代码重构/image-20201229110224937.png)

常用指数：`****`

主要用途：将代码片段提取成为单独的方法，对函数式编程特别友好，封装成为方法可以增加代码的可读性。

解决痛点：如果手动提取代码，有时候比较麻烦，因为还得手动去写方法中可能会依赖到的参数，而WebStorm中的提取方法，可以自动帮你判断有哪些参数。

快捷键：`Ctrl`+`Alt`+`M`。

![重构方法](/images/other/WebStorm各种便利的功能-代码重构/重构方法.gif)

使用方法：选中需要提取成单独方法的代码，右键-重构-提取方法，或者直接使用快捷键，就可以将它单独提取成一个方法，需要提取的代码中如果有依赖外部变量，它会直接帮你把参数也一键补完。

## 2.5 移动方法到其它文件

![image-20201229111451815](/images/other/WebStorm各种便利的功能-代码重构/image-20201229111451815.png)

常用指数：`***`

主要用途：将一个方法从一个文件移动到另一个文件中。

解决痛点：如果你要将一个文件中的方法移动到另一个文件，该方法可能在其它的文件中进行过调用，如果你要手动的重新指定这些调用文件的路径也不是不行，但是就会显得非常麻烦，有时候可能你不知道该方法在哪儿进行过调用。

快捷键：`F6`

使用方法：选中需要移动的函数代码或者函数名，使用快捷键或者在右键菜单里面选择移动，根据提示就可以将这个方法移动到其它的文件中。

## 2.6 提取Vue组件

![image-20201229111701163](/images/other/WebStorm各种便利的功能-代码重构/image-20201229111701163.png)

常用指数：`***`

这个功能简直是重构Vue项目的神器！我是前不久才发现这个功能的便利性。

主要用途：当你的Vue项目中发现可以复用的组件代码，你想将其提取出来单独作为一个组件时。

解决痛点：如果这段代码不是你写的或者你已经忘记是怎么写的时，你要手动抽取成组件是一个非常麻烦的事情，因为该段代码可能调用了很多页面上面的方法，甚至你根本不清楚该段代码用了项目中的哪些样式，因为我们在编写组件的时候往往会用到样式隔离（`<style scoped>`）。

使用方法：直接选中需要提取成为组件的代码或者最外层的DOM，依然是在重构菜单中，选择`提取Vue组件`，它会自动将依赖的变量、方法、样式都传递到子组件中，可能有点不好理解，但是自己使用一次后就会知道是什么意思，非常的方便！

# 3. 最后

本篇文章就暂时介绍这么多，我也会在今后写代码的过程中进一步挖掘WebStorm一些可以提高效率的功能。

写代码是一个慢慢积累的过程，一个高级程序员和一个初级程序员的差距可能并不是在于代码上面，而是在对写代码工具的熟练度，以及插件和第三方库的积累过少，造成的信息差。
