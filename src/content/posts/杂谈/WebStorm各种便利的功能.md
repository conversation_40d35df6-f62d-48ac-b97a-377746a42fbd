---
title: "WebStorm各种便利的功能"
date: 2020-10-17 13:00:28
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


# 1. 前言

WebStorm系列又来了！我还是挺喜欢挖掘写代码时的一些功能和小技巧，虽然对你写代码的能力没有实质性的帮助，但是可以提高你写代码的效率。

IDE工具就像一把枪，如果一个战士对自己手中的枪都不了解，那写代码的效率也高不到哪儿去。当然，用记事本写代码的大神当我没说。

前景提要：

- [WebStorm和VSCode的浅度比较](/2020/08/13/杂谈/webstorm和vscode的浅度比较/)
- [WebStorm强大的Git版本控制](/2020/09/21/杂谈/webstorm强大的git版本控制/)
- [WebStorm好用的插件](/2020/10/09/杂谈/webstorm好用的插件/)

# 2. 云同步

WebStorm拥有强大的云同步功能，可以将你对IDE的一切配置甚至连你在IDE上装的插件，都能同步到云端。

**PS：当然前提是需要登录JetBrains的账号**，不知道盗版用户是否能使用该功能，不过盗版用户貌似可以通过git同步设置。

一个程序员换电脑的时候会安装大量的环境，也会对自己的编辑器做很多个人化的配置，好在前端需要安装的环境不多，基本都是对IDE的配置。

直接点击右下角的齿轮就可以同步配置了：

![image-20201017125807313](/images/other/image-20201017125807313.png)

我之前把同步配置给关掉了，然后一顿找啊，最后发现开启的方法是在：

![image-20201017125937153](/images/other/image-20201017125937153.png)

这个里面打开同步配置。



# 3. 创建模板

创建代码的模板是一个非常有用的功能，其中又分为`文件模板`和`代码模板`。

## 3.1 文件模板

在新建一个模板的时候，就会自动生成一些自带的模板，比如创建一个`.vue`文件，创建完成后你就会发现文件中已经有相应的结构。

这个创建文件时使用的模板也是可以进行编辑的：

![image-20201017125111640](/images/other/image-20201017125111640.png)

## 3.2 代码模板

很多时候重复的代码我们就使用代码模板直接进行生成。

![image-20201017125333695](/images/other/image-20201017125333695.png)

设置完成后，我们再：

![image-20201017125425282](/images/other/image-20201017125425282.png)

下面的位置中选择应用到的文件类型中，根据自己的实际情况选择就可以了。下面就可以愉快的进行使用了。

![wj](/images/other/wj.gif)

当然，创建一个适合自己的模板可不是这么简单的，关于变量的用法在本篇文章中就先不讲了~

# 4. JSDOC

注释在项目中占了很大的比重，如果一个项目没有注释，那么对于后期的维护是非常吃力的，可能你自己当时写的时候知道这个方法是干什么的，但是一周后，一个月后，几个月后再看自己的代码时你还能看的懂嘛。

自己都如此，更别说当别人接手你的代码时，如果没有合理的注释，代码的维护成本是非常高的。

使用的方式也很简单，在某个函数上输入`/**`然后敲击回车，就会生成JSDOC注释。

![jsdoc](/images/other/jsdoc.gif)

至于`@xxx`的颜色，可以在设置里面进行自定义：

![image-20201017124657832](/images/other/image-20201017124657832.png)

# 5. TODO待完成

在写代码的时候，可能经常写到一半就跑去写别的文件，或者另一个项目需要紧急修复BUG，而等到BUG修复完后，再回自己原来的代码，可能并不知道刚才自己在干什么。

而WebStorm对注释中的`TODO`和`FIXME`赋予了特殊的功能。

在注释中，`TODO`和`FIXME`不区分大小写。

## 5.1 TODO

未完成的代码。

可以写在项目的任何注释中，表示这里有代码没有写完。

```javascript
// TODO

/** TODO */

/**
 * TODO
 */
```

上面的3种注释都是可以的。

## 5.2 FIXME

待修复的代码。示例：

```js
// FIXME

/** FIXME */

/**
 * FIXME
 */
```

标记完成后，在WebStorm的底部选择栏有个TODO选项框，点击打开后就可以直接定位到这些标记所在的位置：

![image-20201017122040911](/images/other/image-20201017122040911.png)

# 6. 检测代码

在WebStorm中拥有强大的代码检测功能，有时候需要检测你的代码是否有错误。当前文件的检测在页面的右上角就可以看到：

![image-20201017122233244](/images/other/image-20201017122233244.png)

但是有时候项目要打包之前，你并不知道哪个文件可能有错误或者警告，所以就需要使用全局文件检测。

![image-20201017122420360](/images/other/image-20201017122420360.png)

在这里就可以直接检测到整个项目中的文件是否有错误。
