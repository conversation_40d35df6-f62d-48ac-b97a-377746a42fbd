---
title: "WebStorm和VSCode的浅度比较"
date: 2020-8-13 11:51:23
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


# 1. 吐槽

上机测试的时候准备的工具是VSCode，虽然我早就知道一般情况下，都准备的会是VSCode，但是我没有当回事，结果一操作起来傻眼了，VSCode编写代码的流畅程度和WebStorm完全不同。

在最开始学习Web开发时我也是使用的VSCode，而且我曾一度看不起其它的前端代码编写工具。

观看学习视频自学时，视频里面使用的是WebStorm，而我跟着视频学了很长一段时间，都是使用的VSCode，直到后面我看到视频里演示了两个WebStorm非常惊艳的功能，从那个时候开始，我才逐渐接触JetBrains旗下的产品，然后一发不可收拾。

下面说说让我入坑的两个功能。

## 1.1 .log

当我们需要在控制台进行打印时，需要使用到`console.log()`这个方法。

### 1.1.1 VSCode

VSCode中，输入`log`然后按下回车，自动会补全为`console.log()`。

### 1.1.2 WebStorm

在写代码时，很多时候都会先打出需要打印的部分，再尝试打印，这时就可以使用WebStorm中的`.log`功能，会自动将前面的内容使用`console.log()`包含起来。

![WebStormlog](/images/other/WebStormlog.gif)

## 1.2 吸取颜色

在我们做页面时，总有些颜色需要使用一些吸管工具，才能准备的判断这个颜色的属性值，比如QQ自带的截图工具：

![image-20200812174355124](/images/other/image-20200812174355124.png)

将光标移动到你要提取的颜色上，按下c键就可以复制色号，非常方便。

### 1.2.1 VSCode

可以使用一个插件进行实现该功能。

### 1.2.2 WebStorm

自身就带有吸管工具。

![Getcolor](/images/other/Getcolor.gif)

可以看到十分方便，只需要吸取一下，自动就会为你替换为你需要的颜色。

# 2. 优点

## 2.1 WebStorm

优点太多，直接在下面和VSCode进行对比。

## 2.2 VSCode

1. 免费且开源，这也是大部分大前端选择VSCode的原因，觉得哪里用的不爽分分钟自己写个插件解决。
2. 启动速度以及打开项目速度非常快，几乎同样的配置同样的工程，VSCode的打开速度远远快于WebStorm。
3. 插件非常多，而且VSCode本身就基于Electron开发，所以大前端自己写插件显得不是那么困难。

# 3. 缺点

## 3.1 WebStorm

1. 收费！这是WebStorm最大的缺点，收费还不便宜，个人版一年大概要300多人民币。
2. 启动速度以及打开工程速度缓慢，有时候打开一个小项目都要等1分钟甚至更长时间，更别说一个庞大的项目。
3. 非常吃资源，占用的内存远高于VSCode。

![image-20200813120410623](/images/other/image-20200813120410623.png)

上图为打开同一个工程所占用的资源情况。

## 3.2 VSCode

几乎没有什么太大的缺点，所有感觉是缺点的地方都是因为和WebStorm进行对比，如果你没有用过WebStorm，你根本不会察觉到这些缺点。

# 4. WebStorm方便之处

## 4.1 对第三方库API的支持

导入第三方库，比如jQuery，在WebStorm中进行编写代码时会有jQuery相关的提示，而在VSCode中貌似并没有，不过可以通过插件进行实现，但是每次引入一个新的库就需要配置代码提示，我感觉有点麻烦。

**VSCode**：

![VSCodejQuery](/images/other/VSCodejQuery.gif)

**WebStorm**：

![WebStormjQuery](/images/other/WebStormjQuery.gif)

## 4.2 快速引入组件

在WebStorm中，只需要输入`import 组件名`再敲击回车键，就会自动帮你补全地址。VSCode中似乎必须手动引入，或者是有其它的方法可以实现这一点。

![improtHello](/images/other/improtHello.gif)

## 4.3 自动修改代码中不符合eslint的地方

如果你的项目中有使用eslint，在WebStorm中可以打开设置，保存时会自动帮你修复不符合规则的地方。

![image-20200813113501481](/images/other/image-20200813113501481.png)

## 4.4 自动保存

你是否在写代码时因为蓝屏，停电，死机等各种原因没有保存代码而抓狂过，你是否已经养成了有事没事`ctrl+s`一下以防因为不可抗力导致代码丢失，而WebStorm完美的解决了这个问题，WebStorm拥有自动保存的功能，再也不用担心自己的代码没有保存了。

WebStorm还有些十分方便的功能，这里就不一一列举了，主要是一时半会我也想不完。

# 5.最后

因为我很少用VSCode，仅仅只是因为今天上机时发现VSCode对比于WebStorm各种不方便，并非引战，因为在近两年VSCode的市场份额已经大幅度超越了WebStorm，占据整个前端编辑器市场的50%+。

其实写代码的工具，哪个顺手就用哪个，你如果觉得记事本顺手，完全可以用记事本来写代码，只要能实现功能和效率，哪个代码编辑器都是浮云。
