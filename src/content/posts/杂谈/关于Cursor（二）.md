---
title: "Cursor：三种对话模式的最佳使用场景与Agent模式深度解析"
date: 2025-03-21 21:50:00
categories:
  - "杂谈"
tags:
  - "Cursor"
---


使用Cursor已经第二个月了，这段时间我最大的感受就是写代码越来越可以将自己的注意力集中在代码的架构上，而不是集中在功能的实现上，有非常多的好的想法可以快速实现。

上一篇文章[Cursor：值得花20美元的AI编程利器，让我告别WebStorm的真实体验](/2025/03/04/杂谈/关于cursor/)中，我介绍了Cursor的基本功能，本篇文章我就分享一下我使用Cursor时累积的一些经验。

## 三种对话模式

在Cursor 0.46版本后，它将Chat对话框进行了整合，现在在一个对话框中使用选择框就可以切换`Ask`、`Edit`、`Agent`三种模式。

在0.47版本后，还可以根据自己的喜好自定义模式，不再局限于`Ask`、`Edit`、`Agent`三种模式上。

在论坛中[custom chats are amazing tips 0.47x](https://forum.cursor.com/t/custom-chats-are-amazing-tips-0-47-x/62270)这篇文章对自定义模式进行了详细的介绍，有兴趣的可以去看一下。

这里主要是讲解一下我平时的模式选择。

`Ask`和`Agent`模式是我平时使用的非常多的模式。

## 两种模型

我个人常用的两种模型是`gpt-4o-mini`和`claude-3.7-sonnet`。

`claude`是现在公认的AI模型中，最适合写代码的模型，它拥有超长的上下文，可以让你在写代码时，读取更多已有代码的信息，生成更符合你需求的代码。

而`gpt-4o-mini`主打一个能用，Cursor每个月提供不限量的`gpt-4o-mini`快速请求，所以一些简单的问题，我通常会使用`gpt-4o-mini`模型。

但是`gpt-4o-mini`无法使用在`Agent`模式中。

## Ask

`Ask`：不会对现有代码进行任何修改，只会在对话框中输出对话以及代码，这种模式适合我平时写代码时，遇到一个问题，比如：如何实现一个打字机效果，我只需要在对话框中输入：

```
如何实现一个打字机效果？
```

然后Cursor就会在对话框中输出打字机效果的代码，但它并不会修改你的代码库，你可以根据它给出的内容，自己思考后再编写你觉得合适的代码。

同时由于Cursor每个月快速请求是有限制的，而一些简单的问题使用`gpt-4o-mini`模型就可以解决，而这个模式每个月是无限量使用的。

这样我们在了解项目，查找对应的文件时，就可以使用`Ask`模式，使用`gpt-4o-mini`模型，即满足了需求，又可以将快速请求次数用于一些更复杂的需求上。

### 如何使用`Ask`模式

在Cursor中，如果要使用`Ask`模式了解项目，那么就必须要 `@Codebase` ，这时Cursor就会根据你当前的代码库，回答你的问题。

比如：

```
@Codebase 简单介绍一下这个项目
```

那么Cursor就会根据你的项目，给出对应的项目信息。

## Edit

`Edit`模式（在旧版本中称为Normal模式）允许AI直接修改你的代码。当你在这个模式下提出需求时，Cursor会生成代码并立即应用更改，而不仅仅是提供建议。你稍后可以选择接受或拒绝这些更改。

这种模式非常适合：
- 当你知道确切想要什么，需要快速实现时
- 开始一个新项目，快速创建文件和初始代码结构

`Edit`模式与`Ask`模式的主要区别在于它可以自主创建文件并直接修改代码，使开发过程更为流畅和快速。

## Agent

在最近一个月`Agent`这个词越来越多的被提及。

`Agent`：使用一段自然语言，完成一套需求，比如让AI在Figma中设计一个页面，然后在项目中写出对应的代码，运行项目进行测试，测试完成后Git提交代码到仓库，然后运行CI/CD，部署到服务器上。

这就是一个完整的`Agent`流程。

但是理想很丰满，现实很骨感，要以一个命令完成这一套流程，而不需要人为干预，还非常困难。

而Cursor的`Agent`模式，就是让你可以用一段话，比如：生成一个打字机效果的组件。

然后Cursor会根据你的需求，创建对应的文件，并生成对应的代码，生成对应的测试用例，最后运行测试用例。

如果不使用MCP，那么Cursor就无法在Figma中生成设计图，也无法将代码提交到Git仓库，更无法运行CI/CD，部署到服务器上。

而MCP（Model Context Protocol，模型上下文协议），是一个开放协议，它标准化了应用程序如何向LLM提供上下文和工具。可以将MCP视为Cursor的插件系统，它允许通过标准化接口将Agent连接到各种数据源和工具，从而扩展其能力。

我经过几天的研究，发现它的理念十分强大，它可以通过多个Service来完成一个需求，比如操作Figma，那么就使用Figma Service，操作Git，那么就使用Git Service，操作CI/CD，那么就使用CI/CD Service。从而实现真正的Agent流程。

但是我现在只能说：MCP是未来，但不是现在，现在MCP还处于一个非常早期的阶段，很多功能还不完善。

### Agent与Edit模式的区别

`Agent`模式与`Edit`模式相比有两个主要区别：

1. **更好的上下文理解**：Agent能更全面地理解你的代码库和需求
2. **可以执行终端命令**：它能自动运行命令行操作，而不需要你手动执行

这使得`Agent`模式成为一个更强大的选择，特别是对于复杂的任务。在最新的Cursor更新中，Agent模式获得了许多增强功能，如支持`gpt-4o`模型、能读取linter错误自动修复问题、可在后台运行命令等。

### 什么时候使用`Agent`模式

在很多帖子或者很多视频中，都会出现《不懂代码但是2个小时开发一个app，赚了2万美金》这种差不多的内容，不说能不能赚到这么多钱，但使用Cursor，别说2个小时，5分钟就可以实现一个功能简单的APP。

你完全不需要懂代码，只需要你通过自然语言描述清楚你的需求，Cursor就会根据你的需求，生成对应的代码，并且代码是可以直接运行的。

在英文中，有一个词`Vibe`是描述这种行为的，这个词我还找不到准确的中文翻译，大概意思就是不像传统的写代码一样要一行一行的敲，只需要使用自然语言描述你的需求，AI帮你写出对应的代码，这就叫vibe coding（气氛编程）。

Vibe coding是一种通过AI辅助编码的方式，本质上是利用AI工具完成代码的繁重工作，而你只需要通过创意和描述来引导这个过程。这个术语最初是由AI专家Andrej Karpathy创造的，用来描述"使用AI工具...进行编码的繁重工作，快速构建软件"。在vibe coding中，你不需要手写每一行代码，而是传达你想要什么（程序或功能的"氛围"），AI会为你生成代码。

既然这么方便，那有没有什么坏处？

有！如果你在已有项目中使用`Agent`模式，你想要的需求越复杂，那么Cursor越不会创建出你想要的效果，你想要Cursor修复一个bug，Cursor则会生成一大堆乱七八糟的代码，最后折腾好久，都无法修复对应的BUG。

如果你完全不懂代码，虽然使用`Agent`模式，开始时会非常顺利，但随着代码量越来越多，Cursor会越来越无法实现你的需求，比如在一个页面生成一个简单的按钮，可能你花上好几十条请求，等待几个小时，最后Cursor都没有实现你想要的效果。

所以正如我上一篇文章所说，如果你本身不懂代码，那么想要通过Cursor写一个APP然后赚几千几万块是非常难的，这个几率不亚于中彩票。

那么到底什么时候使用`Agent`模式？

在你对于你项目的代码非常了解，你能非常清晰的描述你的需求，你具有非常好的Debug能力，那么你就可以在一个已有的项目中使用`Agent`模式，不然生成代码5分钟，Debug一天。

我个人使用`Agent`模式，一般都是以下这种情况：

- 将普通的css样式转为tailwindcss样式
- 优化代码，删除冗余的代码
- 提取组件
- 生成测试用例
- 完成一些简单的需求

## 总结

这是Cursor系列的第二篇文章，不得不说Cursor改变了我的开发方式，可以让我更快的实现产品设计的功能，也让我更加注重代码的架构，编写出更加利于维护的代码，并且Cursor的组件化能力与代码的优化重构能力往往可以让我在短时间内完成一个复杂的需求。

同时通过`Ask`模式，我可以在不了解项目的情况下，快速了解项目。

下一篇文章会着重说一下Cursor的`.cursorrules`文件，以及Notepads的妙用。