---
title: "WebStorm强大的调试前端项目功能"
date: 2022-1-25 14:14:46
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

# 1. 前言

WebStorm系列又双叒来了！我挺喜欢挖掘写代码时的一些工具和小技巧，虽然对你写代码的能力没有实质性的帮助，但是可以提高你写代码的效率。

在我们写代码的时候经常会有一些调试的需求，因为某些方法中涉及到一些数据处理，在前端开发中，我们通常使用`console.log()`的方式在控制台进行打印一些变量，来调试代码，或者直接在代码中用下面这种方式：

```js
useEffect(() => {
  debugger;
}, []);
```

但是这样存在一个问题，那就是如果你忘记删除掉标记，那么生产环境下同样也会触发断点状态（虽然Chorme的断点触发是需要打开调试工具）。

而本篇文章我要讲的就是WebStorm强大的调试功能！这也是我非常喜欢的功能，使用的频率非常高，它可以让你快速定位代码中的问题。

经过WebStorm版本的更迭，现在已经可以非常方便的使用它来调试前端项目，无论是Vue还是React项目，都可以使用它进行调试，还不需要做任何的配置！

# 2. 启动调试

其实平时在使用WebStorm的时候，启动项目时是会提示你如何进入调试模式，但可能很多人都没有注意。

要开启调试模式你**首先得先使用WebStorm的终端启动项目**，即下面的这个按钮：

![image-20220125133249322](/images/other/WebStorm强大的调试前端项目功能/image-20220125133249322.png)

上面的选项就对应的是`package.json`中定义的脚本：

![image-20220125133325176](/images/other/WebStorm强大的调试前端项目功能/image-20220125133325176.png)

我们双击`start`启动项目：

![image-20220125133434088](/images/other/WebStorm强大的调试前端项目功能/image-20220125133434088.png)

启动后你就会看到下面的内容，然后按住`ctrl`+`shift`鼠标左键点击下面的链接`http://localhost:3000`：

![image-20220125112523609](/images/other/WebStorm强大的调试前端项目功能/image-20220125112523609.png)

**注意：链接一定要是`http://localhost:3000`这个，点击`http://*************:3000`那个链接是无法打开调试模式的。**

这个时候你会看到WebStorm弹出来这么一个调试面板，并且打开了一个新的Chrome窗口（该窗口为全新的Chrome窗口，不会附带你之前的Chorme浏览器上面的插件）。

如果你的Chrome不是从谷歌官方下载的（例如从电脑管家中下载）很可能启动调试模式会报错，这个时候你只要删除掉Chrome去官方重新下载就好了。

![image-20220125112657714](/images/other/WebStorm强大的调试前端项目功能/image-20220125112657714.png)

# 3. 断点

写过后端的同学就知道，断点是一个非常有用的调试工具，可以帮你快速的找到BUG所在的位置。

打开了WebStorm调试模式后，就可以像调试后端项目那样调试前端项目，这也是我一直使用WebStorm的原因之一，因为这个调试实在是太好用了，你不需要安装其它的插件，只需要根据上面的步奏打开调试模式就可以了。

这种断点比起代码中加入debugger语句有一个优点，那就是即使你忘记取消断点，对于打包后的代码没有任何影响。

这里需要说的是，**由于前端项目一般都有热更新，所以有时候我们直接打上断点，当代码运行到这个位置的时候可能并不会触发断点或者触发的位置不对，这个时候你只需要刷新一下浏览器上的页面，当代码再次运行到这里的时候就会正确的触发断点。**

![image-20220125113301456](/images/other/WebStorm强大的调试前端项目功能/image-20220125113301456.png)

触发断点后无论是点击浏览器上面的播放键，还是WebStorm左侧的播放按钮，都能够让代码继续运行：

![image-20220125113746004](/images/other/WebStorm强大的调试前端项目功能/image-20220125113746004.png)

# 4. 步入步过

## 4.1 步入

![image-20220125113407069](/images/other/WebStorm强大的调试前端项目功能/image-20220125113407069.png)

所谓的步入就是会进入方法中，执行方法中的语句：

![stepIn](/images/other/WebStorm强大的调试前端项目功能/stepIn.gif)

## 4.2 步过

![image-20220125113428705](/images/other/WebStorm强大的调试前端项目功能/image-20220125113428705.png)

所谓的步过就是不会进入到方法中，直接跳过方法执行下面的语句，具体的效果自己亲身体验一下就知道了。

![step](/images/other/WebStorm强大的调试前端项目功能/step.gif)

# 5. 调试

WebStorm的调试界面非常棒，完全跟idea一样，它可以给你非常多的信息。

我一般用调试模式都是数据处理太过于复杂的时候，或者后端传递数据回来我接收该数据的时候。

因为JavaScript语言特性的关系，在你不接到后端传递的数据时，你不知道里面究竟有什么数据。即使是TypeScript定义了接口类型，但是后端更新接口后TypeScript的接口类型没有及时更新，你依然不知道后端返回的到底是个什么数据。

比如下面这种时候：

```js
import React, { useEffect, useState } from "react";

function App() {
  const [data, setData] = useState();

  useEffect(() => {
    // 这里是模拟请求数据
    fetch("http://localhost:5000")
      .then((res) => res.json())
      .then((data) => {
        // 在一般情况下，我们要查看data里面的值，就使用console.log()在控制台中打印
        console.log(data);
        // ... 下面就是所要处理的逻辑
        setData(data);
      });
  }, []);

  return <div>{JSON.stringify(data)}</div>;
}

export default App;
```

而如果你使用了WebStorm的调试模式，你只需要打一个断点，然后你就可以看到data的值：

![image-20220125131220726](/images/other/WebStorm强大的调试前端项目功能/image-20220125131220726.png)

不光在代码旁边可以看到变量的值，在调试面板中的变量板块也能看到变量的值，需要值得注意的是，在调试面板的变量板块，一般情况下，只能看到断点所处的方法中的变量值。

# 6. 定位断点

断点还有一个作用，就是可以用来快速定位代码，比如说你平时写代码的时候可能会写到一半就去写其它的代码，再想要跳回之前的代码就比较麻烦，而这个时候你就可以通过下面的方式，来进行快速写代码，提高效率，

点击调试面板中的查看断点：

![image-20220125113535546](/images/other/WebStorm强大的调试前端项目功能/image-20220125113535546.png)

双击左边的断点，即可以跳转到对应的文件。

![image-20220125113607685](/images/other/WebStorm强大的调试前端项目功能/image-20220125113607685.png)

# 7. 最后

WebStorm的调试工具使用还是很简单很方便的，而且调试工具还拥有其它非常多的功能，具体功能可以自行去发掘。

我自己是非常推荐WebStorm的调试模式，并且也经常使用，它可以极大的提升你找BUG的效率，有时候一个BUG你用`console.log()`+`debugger`可能要找半天，而使用调试模式很快就能找到问题所在。

WebStorm的调试模式不能用来调试小程序项目，比如Taro，uni-app的代码如果你想要通过WebStorm调试，那你只能运行成H5模式，将代码运行在浏览器上面才能够进行调试，如果通过微信开发者工具是无法开启WebStorm的调试模式。
