---
title: "WebStorm强大的Git版本控制"
date: 2020-9-21 00:21:07
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


# 1. 前言

之前写了一篇文章[WebStorm和VSCode的浅度比较](/2020/08/13/杂谈/webstorm和vscode的浅度比较/)，发现真的是浅度比较，其实JetBrains出品的IDE最大的一个好处就是开箱即用，而且几乎你开发中需要使用的功能全部都内嵌在IDE上面，毫不夸张的说：就差内嵌一个浏览器了。

最近才发现JetBrains系列自带的Git版本控制是多么的好用。

# 2. 认识界面

如果当前的项目使用了Git版本控制，则会看到下面的界面。

**如果没有使用Git版本控制，这些都是看不到的，添加了Git仓库后，需要重启才能看到这些菜单。**

如果对Git不是很了解，可以看[项目或者文件管理神器-Git](/2020/07/13/项目或者文件管理神器-git/)。

![image-20200917234422545](/images/other/image-20200917234422545.png)

点击右上角的`√`，会看到下面的界面。

![image-20200917234959484](/images/other/image-20200917234959484.png)

最常用的窗口就是图中框出来的两个，其它的保持默认就好，这个步骤就相当于`git add xxx` 和`git commit 提交信息`这两步的结合。

![image-20200917235216663](/images/other/image-20200917235216663.png)

我们通常会将文件全部选中，输入提交信息，点击`commit`。

![image-20200917235320616](/images/other/image-20200917235320616.png)

有可能会弹出这样的一个对话框，其实就是说你准备提交的文件中有一些错误和不规范的情况，`Review`为查看，`Commit`为提交，我一般都会直接点击Commit。

点击了Commit后，可以在左下角查看到它们：

![image-20200917235647036](/images/other/image-20200917235647036.png)

这样就完成了一次提交，当然WebStorm的版本控制可远远不止这么简单。

# 3. 回退版本

如果想要回退到过去的版本，直接在上面的页面中，选中想要回退的版本，点击右键。

![image-20200920235753950](/images/other/image-20200920235753950.png)

会弹出一个对话框：

![image-20200920235814362](/images/other/image-20200920235814362.png)



- Soft：改变HEAD。将HEAD重置到某一commit，并把中间的commit记录放到Index中。

- Mixed (默认)：改变HEAD和Index。它将重置HEAD到某一commit,并且将中间的commit记录和Staging area文件都返回到Working copy状态下。

- Hard：改变HEAD和Index和working copy重置到某一commit。中间的commit修改记录和Staging area的修改文件和Working copy的修改文件将全部丢失，并更新到这一commit的状态。具有破坏性。如果删除，可以get reflog了解一下。

# 4. 新建分支

在右下角，点击master，在弹出的对话框中选择New Branch。

![image-20200921000114673](/images/other/image-20200921000114673.png)

在弹出的对话框中输入你想要创建的分支的名字。

如果想要切换分支，选择某一个分支，点击`Checkout`。

![image-20200921000256601](/images/other/image-20200921000256601.png)

# 5. 合并分支

首先需要注意，右下角显示的是哪个分支，就是合并后的分支，而选择合并的那个分支，就是被合并的分支。

![image-20200921000711297](/images/other/image-20200921000711297.png)

可以看到test就是被合并的分支，点击Merge into Current进行合并。

## 5.1 解决冲突

当多人合作开发时，合并分支时，或者拉取远程仓库的代码时，难免会遇到代码上的冲突。

![image-20200921001146491](/images/other/image-20200921001146491.png)

一般情况下都是选择手动合并。

![image-20200921001519887](/images/other/image-20200921001519887.png)

完成合并后直接点击右下角Apply就可以了。

# 6. 最后

比起Java的IntelliJ IDEA个人版需要149美元/年和14.90美元/月，WebStorm的价格简直是低到一个令Java开发者羡慕的程度，WebStorm个人版仅59美元/年和5.90美元/月，而且第二年只需要47美元，第三年只需要35美元。

所以作为一个前端开发者，如果不是大神级别的开发者，WebStorm绝对是明智之选，其实我不太理解为什么那么多人选择VScode，VScode需要配置的地方太多太多，你想要将它打造成一个顺手的开发环境，所要付出的时间太多了。

而WebStorm是真正意义上的开箱即用，几乎不需要任何配置，对于新手或者接触前端不久的人来说是非常友好，（除了需要付费，可能这也是用VScode的人那么多的原因吧）。
