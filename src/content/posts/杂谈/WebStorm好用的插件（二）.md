---
title: "WebStorm好用的插件（二）"
date: 2020-12-27 13:34:43
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


之前有一篇文章已经介绍过WebStorm好用的插件：[WebStorm好用的插件](/2020/10/09/杂谈/webstorm好用的插件/)，但那时我也是刚刚才开始挖掘这些插件，从发布那篇文章到现在已经过了2个多月，这段时间，我又接触到了非常多的好用的WebStorm插件，接下来听我一一道来。

**注意：大部分插件Jetbrains旗下的其它IDE也同样可以使用。**

# 1. Chinese (Simplified) Language Pack / 中文语言包

推荐指数：`*****`

**Jetbrains官方出的中文语言包**，以前的英文界面很多功能我都看不懂，所以错失了很多有用的功能。自从用了中文语言包后，虽然最开始从英文变成中文可能会出现找不到选项的情况，但是使用久了是真的香！而且官方还在快速迭代该中文语言包，汉化度越来越完善。

强烈推荐英文不好的朋友使用。

# 2. .env files support

推荐指数：`*****`

在使用Webpack进行打包的时候，我们会有一些环境变量，在编写代码的时候，如果安装了该插件，就会提示环境变量文件中所拥有的环境变量。

# 3. Comments Highlighter

推荐指数：`****`

![Screenshot 1](/images/other/WebStorm好用的插件（二）/screenshot_20553.png)

丰富代码注释高亮样式，可以突出你代码中比较需要注意的一些文字。

# 4. Gist Snippet

推荐指数：`***`

![gist](/images/other/WebStorm好用的插件（二）/gist.gif)

Gist插件，可以快速的在IDE中插入你Gist中的代码片段！关于Gist，我后面会专门写一篇推荐Gist以及Gist如何使用的文章。

# 5. Jenkins Control Plugin

推荐指数：`***`

如果你使用了Jenkins，那么你可以直接通过该插件控制Jenkins打包，当然有些公司的Jenkins一般是由运维或者后端人员进行操作，前端根本不需要自行去配置Jenkins，但是我还是推荐前端开发者学一学Jenkins，因为如果你的个人项目数量比较多，使用Jenkins自动化部署还是非常方便的，可以省下非常多的时间。

关于Jenkins，我后面也会整理一篇我是如何使用Jenkins部署前端项目的文章。

# 6. Json Parser

![JsonParser](/images/other/WebStorm好用的插件（二）/JsonParser.gif)

推荐指数：`**`

被Chrome插件**前端助手**完爆的一个功能，推荐直接使用前端助手，该插件最方便的地方就是可以直接在IDE中使用不需要打开浏览器，但是前端人有不打开浏览器的时候嘛...

# 7. Redis

推荐指数：`****`

可以直接操作Redis的插件，最主要是方便，目前很多主流的操作数据库的软件很少有支持Redis的，缺点是要付费，但是每次IDE更新（？不确定）就可以重新进行30天的免费试用。

# 8. SonarLint

推荐指数：`****`

代码风格检查工具，跟Eslint很相似，有些老项目无法使用Eslint时就可以使用它，新项目也可以和Eslint同时使用，几乎没有什么冲突的规则。

如果你严格按照代码质量检测工具进行编写代码，那么你会编写出一手很漂亮的代码。同时消灭一些浅在BUG，并且以后别人接手你的代码时，如果别人用了代码质量检查工具，不会满篇的警告和错误。

# 9. Docker

推荐指数：`****`

可以很方便的操作Docker镜像和容器，并且可以看到容器内部的目录结构、挂载数据卷的位置、容器的配置等信息，前端开发者几乎很少会使用到Docker，后端和运维可能用的会比较多一点。

该插件为捆绑安装，可以直接在设置里面的构建处找到。

![image-20201227133414338](/images/other/WebStorm好用的插件（二）/image-20201227133414338.png)

# AI代码助手介绍

随着机器学习的快速发展，现在在代码领域也有AI辅助你写代码，目前来说，跟IDE的代码提示很相似，但是它会根据你已经输入过的代码进行学习，如果你下次再输入类似的代码，它就会出现提示，帮助你快速补全代码。

# 1. Codota AI Autocomplete for Java and JavaScript

![Screenshot 1](/images/other/WebStorm好用的插件（二）/screenshot_19077.png)

推荐指数：`****`

我最开始使用的第一款AI代码助手，优点是提高了编码的效率，缺点是仅仅支持Java和JavaScript，并且该插件受到网络环境的限制，经常性失效，不会出现提示，不过有提示的时候确实挺好用的。

# 2. Tabnine AI Autocomplete: JavaScript C Python Ruby Rust Go PHP...

![With Tabnine](/images/other/WebStorm好用的插件（二）/with-tab9-JB-MP.gif)

推荐指数：`***`

同样是上面那个公司的作品，貌似不会受到网络环境的限制，我用的不是很多，几乎支持当前所有的主流编程语言，提示不会出现不生效的情况。

# 3. Kite AI Code AutoComplete: Python, Java, Javascript, HTML/CSS, Go, C/C#/C++

![img](/images/other/WebStorm好用的插件（二）/with_without_kite_v1.png)

推荐指数：`***`

我目前正在使用的AI代码助手，不会存在网络的限制，提示也非常的快，缺点也非常明显！提示的太多有时候会和IDE自带的提示冲突，而且有些自带的提示会被挤到很后面去，选择起来并不是很方便，并且它还需要下一个AI引擎，该引擎必须常年打开，占用的内存量还不低。

![image-20201227131026127](/images/other/WebStorm好用的插件（二）/image-20201227131026127.png)

推荐特别依赖代码提示和机器配置不错的朋友使用。

# 最后

WebStorm的插件有很多，可能有很多好用的插件我都没有发觉，不过没关系，本来写代码就是一个慢慢积累的事情，一个高级程序员和一个初级程序员的差距可能并不是在于代码上面，而是在于这些插件和第三方库造成的信息差。
