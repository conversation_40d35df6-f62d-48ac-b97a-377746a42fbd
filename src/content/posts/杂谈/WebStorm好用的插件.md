---
title: "WebStorm好用的插件"
date: 2020-10-9 23:21:32
categories:
  - "杂谈"
tags:
  - "IDE工具"
---


随着时间的推移，编写JavaScript代码很多有用的插件都集成了WebStorm中，在WebStorm中即使不装任何插件，都能很舒服的进行开发网页应用、Node应用。

插件对于WebStorm并不是特别的重要，但是某些插件实在是比较好用，所以本篇文章就整理一下比较好用的插件。

# 1. 实用插件

## 1.1 [CodeGlance](https://plugins.jetbrains.com/plugin/7275-codeglance)

![Screenshot 1](/images/other/screenshot_16821.png)

在右侧生成一个代码缩略图，用过vscode的朋友肯定很熟悉了，实用度可以说是非常高。

## 1.2 [Rainbow Brackets](https://plugins.jetbrains.com/plugin/10080-rainbow-brackets)

![Screenshot 2](/images/other/screenshot_17373.png)

实现括号配对。

但是在白色主题下不明显...

## 1.3 [Translation](https://plugins.jetbrains.com/plugin/8579-translation)

![Screenshot 1](/images/other/screenshot_17785.png)

好用的翻译插件，在编写代码的很多时候需要用到翻译功能，除非你的英语非常好。

点击状态栏的翻译引擎状态图标或者使用快捷键 `Ctrl + Shift + S`（Mac OS: `Control + Meta + Y`）可以快速切换翻译引擎，目前有谷歌翻译、有道翻译和百度翻译。

使用该插件，可以直接在WebStorm中内嵌一个翻译界面。

## 1.4 [Codota](https://plugins.jetbrains.com/plugin/7638-codota)

![Screenshot 1](/images/other/screenshot_19077.png)

快速的编写Nodejs代码。

## 1.5 [IDE Features Trainer](https://plugins.jetbrains.com/plugin/8554-ide-features-trainer)

![Screenshot 2](/images/other/screenshot_22871.png)

会对IDE中的一些快捷键，以及实用功能进行交互式教学，从而达到抛弃官方手册，就可以让你书写代码的效率更上一层楼。

## 1.6 [String Manipulation](https://plugins.jetbrains.com/plugin/2162-string-manipulation)

![Screenshot 2](/images/other/screenshot_16015.png)

提供了非常多的处理字符串的功能，比如换成`camelCase`驼峰命名，换成`kebab-case`命名`PascalCase`命名，还有各种处理字符串、切换大小写对字符串进行排序等等功能，实用性可以说是非常高。

## 1.7 [Json Parser](https://plugins.jetbrains.com/plugin/10650-json-parser)

![Screenshot 1](/images/other/screenshot_19190.png)

前端开发者一般情况下接触到JSON格式特别多，但是每次想要格式化JSON数据都需要通过打开浏览器到对应的格式化网站，通过这个插件，你可以直接在IDE中进行格式化。

# 2. 不实用插件，但是酷

## 2.1 [Dmitry Batkovich](https://plugins.jetbrains.com/plugin/8575-nyan-progress-bar)

![image-20201007003944178](/images/other/image-20201007003944178.png)

将所有进度条变成彩虹条。

## 2.2 [Background Image Plus](https://plugins.jetbrains.com/plugin/8502-background-image-plus)

可以自定义编辑器的背景图片。

## 2.3 [Material Theme UI](https://plugins.jetbrains.com/plugin/8006-material-theme-ui)

![Screenshot 1](/images/other/screenshot_17526.png)

修改编辑器UI。

## 2.4 [Power Mode II](https://plugins.jetbrains.com/plugin/8251-power-mode-ii)

![Screenshot 1](/images/other/screenshot_15884.png)

让你输入代码时拥有很多酷炫的特效。
