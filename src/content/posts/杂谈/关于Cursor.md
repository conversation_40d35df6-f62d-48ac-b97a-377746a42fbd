---
title: "Cursor：值得花20美元的AI编程利器，让我告别WebStorm的真实体验"
date: 2025-03-04 22:11:43
categories:
  - "杂谈"
tags:
  - "Cursor"
---


在前两个星期，机缘巧合下，我接触到了Cursor这款AI编程工具。

我很早就开始使用GitHub Copilot，大概是在2022年，使用后就开始了很长时间的付费订阅，每个月10美元，大概订阅了近2年的时间。

自从接触到了通义灵码，因为它是免费的，效果也和GitHub Copilot差不多，所以去年年中的时候我取消了GitHub Copilot的订阅。

在订阅的近2年时间里，GitHub Copilot的功能并没有太大的变化，这也是我取消订阅的原因之一。

## 接触Cursor

在接触到Cursor之前，我一直使用WebStorm作为我的主力IDE，因为WebStorm强大的代码补全功能、代码自动引入能力、代码重构能力、代码跳转能力，让我非常依赖JetBrains全家桶。在过去5年的前端开发中，我一直使用WebStorm。

在知道Cursor后，因为Cursor是基于VSCode研发的一款AI编程工具，我其实对它并不感冒。VSCode我也有在用，但它给我的感觉就是，与WebStorm相比，缺少了很多WebStorm的特性，尤其是代码之间的跳转、Git操作、代码重构、代码补全、代码提示等功能，让我非常不习惯。

但是！试用了Cursor后，我只能用下面这句话来形容它：

Cursor的AI是加特林级别，编辑器是手枪级别。

因为VSCode的开源特性，Cursor并不是以插件的形式存在，而是将VSCode的代码编辑功能和AI功能进行了整合，让它变得更加智能。

## tabtabtab

Cursor和GitHub Copilot一样，拥有代码补全功能，但它比GitHub Copilot更加智能，它可以根据你当前的代码，给出更加智能的代码补全建议。

与GitHub Copilot不同的是，Cursor的代码补全功能可以修改已经存在的代码，只需要按下`tab`键，就可以修改已经存在的代码。这个功能让我感到非常震惊，因为同类型的工具中，`tab`补全往往只能新增代码，而不能修改已经存在的代码。

同时，Cursor的代码补全功能还有一点不同，它会根据你的上下文，预测你下一步想要做的事情，你按下`tab`键后，它就会聚焦到你可能想要操作的代码上。

## agent

如果说tabtabtab是Cursor的代码补全功能，那么agent就是Cursor的灵魂。

正是因为agent，让Cursor直接火出了圈。现在有很多文章标题，例如："2个小时开发一款独立APP"、"完全不会代码的我，用Cursor开发了一款应用"等等。

所谓的agent，就是你用自然语言描述你的需求，然后Cursor会根据你的需求，创建项目、编写对应的代码、debug。

同时，Cursor生成的界面颜值非常高。一般来说，独立开发者对于UI都是十分头痛的，因为作为程序员，UI设计并不是他们的强项，并且UI设计需要大量的时间进行积累。

而Cursor现在可以调用Claude，而Claude的UI审美也非常在线，比大多数程序员依靠自己搭建的UI要好看很多。

agent确实非常适合完全不会代码的人，因为它不仅可以帮助你生成代码，还可以根据代码中的报错信息，修改对应的代码。

## 程序员手中的Cursor

但是！在程序员手中的Cursor，我个人觉得和在完全不会代码的人手中的Cursor，是完全不同的体验。

因为小白看不懂代码，所以tabtabtab补全的代码对于他们来说是没有任何意义的。而tabtabtab补全的代码，对于程序员来说，是非常好用的。就我个人而言，我很少使用到agent功能，但却离不开tabtabtab功能。

因为它不仅可以补全代码，还可以根据上下文，自动帮我引入对应的变量、对应的函数、对应的组件，弥补了VSCode自动引入代码功能上的不足。

## 20美元

Cursor的定价是20美元/月，在试用了一周后，我还是选择了付费，抛弃了WebStorm，投入到VSCode的怀抱。我之前从来没有想过，WebStorm居然会因为AI功能而被我抛弃。

那么20美元到底值不值呢？

我觉得值。如果不值的话，我也不会选择付费。同时，20美元换算成人民币，大概140多元，一般程序员的工资都不低。如果因为使用Cursor，让你多出了很多时间摸鱼，让你可以学习更多的东西，我个人觉得是非常划算的。而且在现在，140多元，大概是一顿火锅的钱，少吃一顿火锅，让工作轻松一点，何乐而不为？

对于非程序员来说值得订阅吗？

我觉得不值得，因为非程序员本身就不靠代码吃饭，而要使用Cursor做出一款应用然后赚到钱，是非常难的。很多程序员当独立开发者都不能解决温饱的问题，那非程序员就更难了。

## 最后

使用Cursor半个月了，它确实使我的开发效率提升了很多，帮助我解决了很多重复性劳动。同时，它可以帮我分析一个新接触到的项目中的代码，让我快速找到项目中存在的问题。

对于AI是否会取代程序员，我个人认为是程序员之间的差距会一下子拉大。愿意接受新事物的程序员，开发效率会越来越高，效率越高就有时间接触到更多的东西，然后进一步提高自己的效率。