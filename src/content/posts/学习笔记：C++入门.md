---
title: "学习笔记：C++入门"
date: 2020-05-27
categories:
  - "C++"
tags:
  - "集成开发环境"
  - "C++"
---


# **1. IDE**

## **1.1 Visual Studio**

![img](/images/v2-265ce98a09b8b29727a4c13fc8ddada8_720w.jpg)

## **1.2 CLion**

我最后还是选择了 **CLion** 因为之前一直用的JetBrains公司开发的IDE工具，换成Visual Studio突然还不习惯，虽然Visual Studio是我接触的第一款IDE，但是用它来编写C++代码提示居然默认不选中，编写C#的时候都可以，网上搜索发现Visual Studio编写C++代码就是这么设计的！（黑人问号？）

当然IDE这种东西还是自己顺手就好，如果你喜欢，你完全可以用记事本来写程序。

### **1.2.1 CLion 运行环境**

因为我安装了Visual Studio所以可以直接导入Visual Studio的运行环境。

### **1.2.2 中文乱码**

![img](/images/v2-d81e1dd047d2de11c6472a4d1bb73032_720w.jpg)

图中的设置部分换成`UTF-8`，然后右下角控制台格式切换为`GBK`

![img](/images/v2-5ef6f95e5ee2cc295c8fed2b35cfe396_720w.jpg)

# **2. 入门**

## **2.1 Hello, World!**

```cpp
 #include <iostream>
 
 using namespace std;
 
 int main() {
     cout << "Hello, World!" << endl;
     return 0;
 }
```

## **2.2 控制台打印**

需要引入`using namespace std;`，然后通过`cout << XXX << endl;`打印，不得不说学过高级语言后觉得这实在很难用。

虽然可以用C语言的`printf()`但是你如果要打印一个变量需要给出类型，比如：

```cpp
 #include <iostream>
 
 int main() {
     int a = 10;
     printf("%d", a);// 必须指定类型，不然会报错
     return 0;
 }
```

## **2.3 清除控制台**

```
system("cls");
```

## **2.4 保留关键字**

C++中预先保留的单词，**在定义变量或者常量时候，不要用关键字**

![img](/images/v2-24cc250da7e38eb4e1399bd71d8e7d57_720w.jpg)

# **3. 数据类型**

## **3.1 整形**

![img](/images/v2-aef5c9ea7a02946a2d07f4d86769f74d_720w.jpg)

## **3.2 浮点型**

用于表示小数

浮点型变量分为两种：

1. 单精度float
2. 双精度double

两者的**区别**在于表示的有效数字范围不同。

![img](/images/v2-542adae633ed058045b4c73885156ae4_720w.png)

**一般情况下`float`就足够了，如果不够用再定义`double`。**

## **3.3 字符**

**语法：**`char ch = 'a';`

- C和C++中字符型变量只占用`1个字节`。
- 字符型变量并不是把字符本身放到内存中存储，而是将对应的ASCII编码放入到存储单元。

## **3.4 字符串**

**C风格字符串**： `char 变量名[] = "字符串值"`

**C++风格字符串**： `string 变量名 = "字符串值"` 需要加入头文件`#include<string>`

## **3.5 布尔类型 bool**

- true --- 真（本质是1）
- false --- 假（本质是0）
- **bool类型占1个字节大小**

## **3.6 转义字符**

![img](/images/v2-e34ba010b952bd041443d31ffe36d0e7_720w.jpg)

# **4. 运算符**

## **4.1 算术运算符**

![img](/images/v2-ac1efde1442f83c97ea9e6745fbd39eb_720w.jpg)

## **4.2 赋值运算符**

![img](/images/v2-b4b0e321fd55b5826c379c584528497a_720w.jpg)

## **4.3 比较运算符**

![img](/images/v2-312d5675c66472b892bc77f7a454b5d6_720w.jpg)

## **4.4 逻辑运算符**

![img](/images/v2-86186573378281763a342b4f13d8876b_720w.png)

# **5. 流程控制**

## **5.1 if语句（常用）**

```
if(条件){ 条件满足执行的语句 }
```

![img](/images/v2-65f99327170787901b8b4f2639d9bb3b_720w.png)

```cpp
 int main() {
 
     //选择结构-单行if语句
     //输入一个分数，如果分数大于600分，视为考上一本大学，并在屏幕上打印
 
     int score = 0;
     cout << "请输入一个分数：" << endl;
     cin >> score;
 
     cout << "您输入的分数为： " << score << endl;
 
     //if语句
     //注意事项，在if判断语句后面，不要加分号
     if (score > 600) {
         cout << "我考上了一本大学！！！" << endl;
     }
 
     system("pause");
 
     return 0;
 }
```

**多行格式if语句**：`if(条件){ 条件满足执行的语句 }else{ 条件不满足执行的语句 };`

![img](/images/v2-098fbb7e36ce51d49281509de4769ff4_720w.jpg)

```cpp
 int main() {
 
     int score = 0;
 
     cout << "请输入考试分数：" << endl;
 
     cin >> score;
 
     if (score > 600) {
         cout << "我考上了一本大学" << endl;
     } else {
         cout << "我未考上一本大学" << endl;
     }
 
     system("pause");
 
     return 0;
 }
```

**多条件的if语句**：`if(条件1){ 条件1满足执行的语句 }else if(条件2){条件2满足执行的语句}... else{ 都不满足执行的语句}`

![img](/images/v2-551948e98def630e5e86c56df0f51d6d_720w.jpg)

```cpp
 int main() {
 
     int score = 0;
 
     cout << "请输入考试分数：" << endl;
 
     cin >> score;
 
     if (score > 600) {
         cout << "我考上了一本大学" << endl;
     } else if (score > 500) {
         cout << "我考上了二本大学" << endl;
     } else if (score > 400) {
         cout << "我考上了三本大学" << endl;
     } else {
         cout << "我未考上本科" << endl;
     }
 
     system("pause");
 
     return 0;
 }
```

**嵌套if语句**：在if语句中，可以嵌套使用if语句，达到更精确的条件判断

```cpp
 int main() {
 
     int score = 0;
 
     cout << "请输入考试分数：" << endl;
 
     cin >> score;
 
     if (score > 600) {
         cout << "我考上了一本大学" << endl;
         if (score > 700) {
             cout << "我考上了北大" << endl;
         } else if (score > 650) {
             cout << "我考上了清华" << endl;
         } else {
             cout << "我考上了人大" << endl;
         }
 
     } else if (score > 500) {
         cout << "我考上了二本大学" << endl;
     } else if (score > 400) {
         cout << "我考上了三本大学" << endl;
     } else {
         cout << "我未考上本科" << endl;
     }
 
     system("pause");
 
     return 0;
 }
```

## **5.2 三目运算符**

**语法：**`表达式1 ? 表达式2 ：表达式3`

**解释：**

如果表达式1的值为真，执行表达式2，并返回表达式2的结果；

如果表达式1的值为假，执行表达式3，并返回表达式3的结果。

```cpp
 int main() {
 
     int a = 10;
     int b = 20;
     int c = 0;
 
     c = a > b ? a : b;
     cout << "c = " << c << endl;
 
     //C++中三目运算符返回的是变量,可以继续赋值
 
     (a > b ? a : b) = 100;
 
     cout << "a = " << a << endl;
     cout << "b = " << b << endl;
     cout << "c = " << c << endl;
 
     system("pause");
 
     return 0;
 }
```

## **5.3 switch语句**

```cpp
 switch(表达式){
         
     case 结果1：执行语句;break;
 
     case 结果2：执行语句;break;
 
     ...
 
     default:执行语句;break;
 
 }
```

## **5.4 循环结构**

### **5.4.1 while循环语句（常用）**

**语法：**`while(循环条件){ 循环语句 }`

![img](/images/v2-12272cc189f8a1a6aac721cb5b4ca42f_720w.png)

```cpp
 int main() {
 
     int num = 0;
     while (num < 10) {
         cout << "num = " << num << endl;
         num++;
     }
 
     system("pause");
 
     return 0;
 }
```

### **5.4.2 do...while循环语句**

**语法：** `do{ 循环语句 } while(循环条件);`

**注意：与while的区别在于do...while会先执行一次循环语句，再判断循环条件**

```cpp
 int main() {
 
     int num = 0;
 
     do {
         cout << num << endl;
         num++;
 
     } while (num < 10);
 
 
     system("pause");
 
     return 0;
 }
```

### **5.4.3 for循环语句（常用）**

**作用：** 满足循环条件，执行循环语句

**语法：**`for(起始表达式;条件表达式;末尾循环体) { 循环语句; }`

```cpp
 int main() {
 
     for (int i = 0; i < 10; i++) {
         cout << i << endl;
     }
 
     system("pause");
 
     return 0;
 }
```

## **5.5 跳转语句**

### **5.5.1 break语句**

break使用的时机：

- 出现在switch条件语句中，作用是终止case并跳出switch
- 出现在循环语句中，作用是跳出当前的循环语句
- 出现在嵌套循环中，跳出最近的内层循环语句

### **5.5.2 continue语句**

**作用：**在**循环语句**中，跳过本次循环中余下尚未执行的语句，继续执行下一次循环

### **5.5.3 goto语句（慎用）**

**注意：在程序中不建议使用goto语句，以免造成程序流程混乱**

# **6. 数组**

所谓数组，就是一个集合，里面存放了相同类型的数据元素

## **6.1 一维数组**

1. `数据类型 数组名[ 数组长度 ];`
2. `数据类型 数组名[ 数组长度 ] = { 值1，值2 ...};`
3. `数据类型 数组名[ ] = { 值1，值2 ...};`

```cpp
 int main() {
 
     //定义方式1
     //数据类型 数组名[元素个数];
     int score[10];
 
     //利用下标赋值
     score[0] = 100;
     score[1] = 99;
     score[2] = 85;
 
     //利用下标输出
     cout << score[0] << endl;
     cout << score[1] << endl;
     cout << score[2] << endl;
 
 
     //第二种定义方式
     //数据类型 数组名[元素个数] =  {值1，值2 ，值3 ...};
     //如果{}内不足10个数据，剩余数据用0补全
     int score2[10] = {100, 90, 80, 70, 60, 50, 40, 30, 20, 10};
 
     //逐个输出
     //cout << score2[0] << endl;
     //cout << score2[1] << endl;
 
     //一个一个输出太麻烦，因此可以利用循环进行输出
     for (int i = 0; i < 10; i++) {
         cout << score2[i] << endl;
     }
 
     //定义方式3
     //数据类型 数组名[] =  {值1，值2 ，值3 ...};
     int score3[] = {100, 90, 80, 70, 60, 50, 40, 30, 20, 10};
 
     for (int i = 0; i < 10; i++) {
         cout << score3[i] << endl;
     }
 
     system("pause");
 
     return 0;
 }
```

## **6.2 二维数组**

二维数组定义的四种方式：

1. `数据类型 数组名[ 行数 ][ 列数 ];`
2. `数据类型 数组名[ 行数 ][ 列数 ] = { {数据1，数据2 } ，{数据3，数据4 } };`
3. `数据类型 数组名[ 行数 ][ 列数 ] = { 数据1，数据2，数据3，数据4};`
4. `数据类型 数组名[ ][ 列数 ] = { 数据1，数据2，数据3，数据4};`

> 建议：以上4种定义方式，利用第二种更加直观，提高代码的可读性

```cpp
 int main() {
 
     //方式1  
     //数组类型 数组名 [行数][列数]
     int arr[2][3];
     arr[0][0] = 1;
     arr[0][1] = 2;
     arr[0][2] = 3;
     arr[1][0] = 4;
     arr[1][1] = 5;
     arr[1][2] = 6;
 
     for (int i = 0; i < 2; i++) {
         for (int j = 0; j < 3; j++) {
             cout << arr[i][j] << " ";
         }
         cout << endl;
     }
 
     //方式2 
     //数据类型 数组名[行数][列数] = { {数据1，数据2 } ，{数据3，数据4 } };
     int arr2[2][3] =
             {
                     {1, 2, 3},
                     {4, 5, 6}
             };
 
     //方式3
     //数据类型 数组名[行数][列数] = { 数据1，数据2 ,数据3，数据4  };
     int arr3[2][3] = {1, 2, 3, 4, 5, 6};
 
     //方式4 
     //数据类型 数组名[][列数] = { 数据1，数据2 ,数据3，数据4  };
     int arr4[][3] = {1, 2, 3, 4, 5, 6};
 
     system("pause");
 
     return 0;
 }
```

# **7. 函数（重要）**

**作用：**将一段经常使用的代码封装起来，减少重复代码

一个较大的程序，一般分为若干个程序块，每个模块实现特定的功能。

## **7.1 定义**

```cpp
 返回值类型 函数名 （参数列表）{
 
        函数体语句
 
        return表达式
 
 }
```

- 返回值类型 ：一个函数可以返回一个值。在函数定义中
- 函数名：给函数起个名称
- 参数列表：使用该函数时，传入的数据
- 函数体语句：花括号内的代码，函数内需要执行的语句
- return表达式： 和返回值类型挂钩，函数执行完后，返回相应的数据

## **7.2 值传递**

- 所谓值传递，就是函数调用时实参将数值传入给形参
- 值传递时，如果形参发生，并不会影响实参

## **7.3 函数的声明**

**作用：** 告诉编译器函数名称及如何调用函数。函数的实际主体可以单独定义。

- 函数的**声明可以多次**，但是函数的**定义只能有一次**

## **7.4 分文件编写**

**作用：**让代码结构更加清晰

函数分文件编写一般有4个步骤

1. 创建后缀名为.h的头文件
2. 创建后缀名为.cpp的源文件
3. 在头文件中写函数的声明
4. 在源文件中写函数的定义

```cpp
 //swap.h文件
 #include<iostream>
 using namespace std;
 
 //实现两个数字交换的函数声明
 void swap(int a, int b);
 //swap.cpp文件
 #include "swap.h"
 
 void swap(int a, int b)
 {
     int temp = a;
     a = b;
     b = temp;
 
     cout << "a = " << a << endl;
     cout << "b = " << b << endl;
 }
 //main函数文件
 #include "swap.h"
 int main() {
 
     int a = 100;
     int b = 200;
     swap(a, b);
 
     system("pause");
 
     return 0;
 }
```

# **8. 指针(重要)**

**几乎所有的编程语言都存在着指针，但是C++中的指针真是我学过的语言中最复杂的。其它语言最多只了解一下值类型和引用类型需要理解指针，但是C++却需要操作指针。**

## **8.1 概念**

**指针的作用：** 可以通过指针间接访问内存

- 内存编号是从0开始记录的，一般用十六进制数字表示
- 可以利用指针变量保存地址

**指针变量定义**语法： `数据类型 * 变量名；`

- 普通变量存放的是数据,指针变量存放的是地址
- 指针变量可以通过" * "操作符，操作指针变量指向的内存空间，这个过程称为解引用

## **8.2 指针所占内存**

所有指针类型在32位操作系统下是4个字节

## **8.3 空指针和野指针**

**空指针**：指针变量指向内存中编号为0的空间

**用途：**初始化指针变量

**注意：**空指针指向的内存是不可以访问的

```cpp
 int main() {
 
     //指针变量p指向内存地址编号为0的空间
     int * p = NULL;
 
     //访问空指针报错 
     //内存编号0 ~255为系统占用内存，不允许用户访问
     cout << *p << endl;
 
     system("pause");
 
     return 0;
 }
```

**野指针**：指针变量指向非法的内存空间

```cpp
 int main() {
 
     //指针变量p指向内存地址编号为0x1100的空间
     int * p = (int *)0x1100;
 
     //访问野指针报错 
     cout << *p << endl;
 
     system("pause");
 
     return 0;
 }
```

**空指针和野指针都不是我们申请的空间，因此不要访问。**

## **8.4 const修饰指针**

const修饰指针有三种情况

1. const修饰指针 --- 常量指针
2. const修饰常量 --- 指针常量
3. const即修饰指针，又修饰常量

# **9. 结构体**

结构体属于用户**自定义的数据类型**，允许用户存储不同的数据类型

**语法：**`struct 结构体名 { 结构体成员列表 }；`

通过结构体创建变量的方式有三种：

- struct 结构体名 变量名
- struct 结构体名 变量名 = { 成员1值 ， 成员2值...}
- 定义结构体时顺便创建变量

## **9.1 指针**

**作用：**通过指针访问结构体中的成员

- 利用操作符 `->`可以通过结构体指针访问结构体属性

# **10. 参考资料**

视频：https://www.bilibili.com/video/BV1et411b73Z

笔记：https://github.com/AnkerLeng/Cpp-0-1-Resource
