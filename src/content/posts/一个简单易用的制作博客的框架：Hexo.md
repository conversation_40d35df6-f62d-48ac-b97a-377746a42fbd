---
title: "一个简单易用的制作博客的框架：Hexo"
date: 2020-05-19
categories:
  - "blog"
tags:
  - "个人博客"
  - "Hexo"
---


学习一项技术的最好方法就是写博客，博客不光可以分享你学到的知识，而且如果你的学习中有什么错误或者纰漏，偶尔也会被路过的大神指出。

使用Vue或者React搭建一个静态博客是非常简单的事情，但是如果想要搭建一个像知乎这种具有动态更新，还带有评论系统，并且界面还具有自适应，这些就显得不是那么容易。毕竟一个人的精力有限，如果需要面面俱到就需要花上很长的时间。

很多公司意识到了这些事情，所以推出了非常多的快速搭建博客的框架，大致看了一下，非常多的人都推荐使用Hexo，所以我也想尝试一下使用Hexo搭建博客，是不是真的那么方便。

# 官网

https://hexo.io/zh-cn/

# 安装前提

- [Node.js](https://nodejs.org/en/) (Node.js 版本需不低于 8.10，建议使用 Node.js 10.0 及以上版本)
- [Git](https://git-scm.com/)

这两个软件是运行环境，必须要进行安装。

# 安装

装好了上面两个运行环境后，直接按住键盘上的`Ctrl右边的键`+`r` ，在打开的运行窗口中输入`cmd`，再在新弹出的窗口中输入`npm install -g hexo-cli`等待安装完成即可。

# 创建项目

到你需要创建项目的文件夹中，打开`cmd`

![img](/images/v2-b0fca0c8dc063705e70cda5b6aa1e26d_b.jpg)

在弹出的窗口中输入`hexo init 你需要创建的博客名`。

# 文件目录

创建完成后，进入到你创建的文件夹，会看到下面的目录样式。

```text
.
├── _config.yml 网站的配置信息，您可以在此配置大部分的参数。
├── package.json 应用程序的信息，如果需要更改需要对node有一定了解
├── scaffolds 文章存放的文件夹。
├── source 资源文件夹
|   ├── _drafts
|   └── _posts
└── themes 主题文件夹。
```

这时还需要再像上面一样打开`cmd` 输入`npm install` 回车运行，安装依赖包。

# 配置

刚才提到的`_config.yml`文件中，可以配置大部分的信息，比如网站的标题，描述，关键字。如果有需要的话，可以直接参考官方给出的配置文档。

[配置hexo](https://hexo.io/zh-cn/docs/configuration)

如果按照上面的步骤，顺利走到了这里，那么继续按照上面的方法打开`cmd`

输入`hexo server`，会看到如下图所示

![img](/images/v2-d7193f1d9bb78df6065e869b2d03f6b6_720w.png)

将框起来的那部分在浏览器地址栏上进行输入。

![img](/images/v2-8d39b1b6dd060c0c038e99154a7ff28c_720w.jpg)

如果看到这个界面，恭喜，你的博客框架已经大体搭建完成

# 主题

官方自带的主题不是很符合胃口，我认为使用Hexo搭建博客如果还需要自行敲代码不如直接使用Vue或者React搭建了，所以可以直接使用人家开源出的主题。

https://github.com/fluid-dev/hexo-theme-fluid

下载完成后直接放在`themes`文件夹中

![img](/images/v2-a0b4cfe339bea903b23195102534a226_720w.jpg)

要进行应用需要修改项目根目录中的`_config.yml`文件中的`theme`属性，修改为刚才下载的主题文件夹名。

例如：`theme: hexo-theme-fluid`

再通过`hexo serve`运行项目，就可以查看到效果。

![img](/images/v2-847e34e46aee11dc944de56d5a07a235_720w.jpg)

如果需要对主题进行修改，可以直接进入主题文件夹中找到 `_config.yml` 文件，修改里面的参数。

# 打包发布

在项目文件中打开`cmd`输入命令`hexo generate` 会看见多出来一个`public`文件夹，将里面的内容直接发布到自己的服务器上面即可。如果没有自己的服务器，也可以使用GitHub Pages，但是GitHub Pages由于特殊原因，在国内访问速度会比较慢。

至于GitHub Pages是什么，以及怎么用，可以看这篇~

https://developer.mozilla.org/zh-CN/docs/Learn/Common_questions/Using_Github_pages

# **总结**

如果你仅仅只需要使用主题搭建自己的博客，不需要做太多的个性化修改，那么使用Hexo确实是一个非常好的选择，几乎不需要太多的编程知识就可以搭建出一个还不错的博客界面，但是如果你想搭建高度自定义的博客界面，那还是不太推荐使用Hexo框架，主要是EJS这玩意...现在几乎已经不用了。

---

6月24日 更新

我自己尝试做网站的时候遇到了客户端渲染和服务器渲染的问题，简单的说Vue和React搭建出的单页面应用都为客户端渲染，不利于SEO，而Hexo就完全打包成了一个静态页面...有利于SEO，至于使用Vue的服务器渲染可以看这篇：

[Vue服务器渲染优秀项目](https://zhuanlan.zhihu.com/p/149981401)

总的来说，如果你想将博客布置到GitHub Pages那么Hexo优于使用Vue搭建的页面，因为它更加利于SEO。

---

8月16日 更新

在md文件中书写&#123;&#123;&#125;&#125;会报错，这个时候我们对特殊字符进行转换成字符实体`&#123;&#123;&#125;&#125;`。

[HTML字符实体转换](https://www.qqxiuzi.cn/bianma/zifushiti.php)
