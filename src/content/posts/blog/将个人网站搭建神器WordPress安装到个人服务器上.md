---
title: "将个人网站搭建神器WordPress安装到个人服务器上"
date: 2021-3-4 15:20:04
categories:
  - "blog"
tags:
  - "WordPress"
---


> WordPress是一个以PHP和MySQL为平台的自由开源的博客软件和内容管理系统。WordPress具有插件架构和模板系统。截至2018年4月，排名前1000万的网站中超过30.6%使用WordPress。WordPress是最受欢迎的网站内容管理系统。全球有大约30%的网站(7亿5000个)都是使用WordPress架设网站的。WordPress是目前因特网上最流行的博客系统。WordPress在最著名的网络发布阶段中脱颖而出。如今，它被使用在超过7000万个站点上。

作为一个开发者而言通常有**搭建个人博客或者个人网站相关的需求（推销自己嘛）**，如果要自己动手写一个也不是不可以，但是从**网页设计**到**网页构建**再到**前后端交互**等一系列操作会花费大量的时间与精力。

而WordPress就解决了这一痛点，它拥有大量的网站模板，以及大量的插件能够满足你个人博客和个人网站的需求，只需要少量的代码或者完全不需要使用代码就能搭建属于你自己的博客或网页。

**那么使用WordPress建站需要准备什么？**

1. 服务器
2. 域名

国内的服务器要使用80端口首先得备案，**服务器推荐选择1G以上的内存**。并且域名需要解析到服务器对应的ip地址，至于服务器的选择和域名的解析在这里就不着重讲解了，因为这里面的门道三言两语还说不清楚。

不过还是要强调一点：**服务器推荐使用Linux系统，不推荐使用Windows Server**，因为Windows Server非常消耗内存！

---

整个流程一共分为5个步骤：

1. 安装宝塔。
2. 在宝塔中安装Nginx。
3. 在宝塔中安装docker。
4. 在控制台中安装docker-compose。
5. 使用docker-compose启动容器。
6. 登陆域名初始化WordPress。

**为什么要使用宝塔？**

因为宝塔提供了可视化界面，可以很方便的对Nginx、数据库、docker这些软件进行操作，对于一个不是专攻Linux的开发者而言体验是极好的，同时宝塔还提供**一键申请SSL证书**，**SSL证书到期前一个月自动续签**，**数据库定时备份**等功能，总而言之，宝塔是非常好用的软件管理工具。

**为什么要使用docker？**

docker提供了一个完美的沙箱机制，虽然它的沙箱机制太过于完美而导致有时候会有一些很难甚至无法解决的问题，但是它实在是太好用了。你不需要一个软件时，直接把容器删掉就好了。

更重要的是如果你想在服务器上面放置多个WordPress网站，直接再创建一个容器就好了。

**为什么不是Hexo？**

Hexo最终会打包成为静态页面然后放在GitHub Pages这类的网站，但是！Hexo要实现留言功能需要借助第三方平台，将数据放在别人的服务器上面，而WordPress不仅自带留言功能，甚至还有登陆等一系列的功能，并且数据都是存放在自己的服务器上面！除非你的服务器被攻击，不然不会出现数据泄露的情况。

**Hexo的文章编辑需要在本地**，如果你有文章更新，你需要手动去进行重新打包，然后发布到服务器上面（当然你也可以使用Jenkins这种自动部署工具，我就是使用Jenkins进行部署），而WordPress就跟你在知乎或者掘金上面写文章一样方便！

**如果你想完全免费搭建一个个人博客（不想购买服务器），那么就选择Hexo，不然WordPress是你最好的选择。**

# 1. 安装宝塔

[宝塔安装教程](https://www.bt.cn/bbs/thread-19376-1-1.html)

宝塔的安装几乎是傻瓜式安装，直接根据自身的系统输入对应的安装命令就行。

安装结束会出现一个面板地址：

![image-20210304124838684](/images/blog/将个人网站搭建神器WordPress安装到个人服务器上/image-20210304124838684-1614842519019.png)

复制**外网面板地址**到浏览器打开，输入账号密码进行登录。

# 2. 安装Nginx

登录后需要你进行注册，注册完毕后会弹出一个弹窗，在弹出的弹窗中去掉除了Nginx之外的其它所有选项，点击安装，并且等待安装结束。

![image-20210304125244546](/images/blog/将个人网站搭建神器WordPress安装到个人服务器上/image-20210304125244546-1614842517501.png)

## 2.1 创建网站

点击**左侧网站**-**添加站点**。

![image-20210304125406636](/images/blog/将个人网站搭建神器WordPress安装到个人服务器上/image-20210304125406636-1614842516320.png)

填写你解析到该服务器上的对应的域名后点击提交。

![image-20210304125546208](/images/blog/将个人网站搭建神器WordPress安装到个人服务器上/image-20210304125546208-1614842514224.png)

## 2.2 开启SSL

接下来我们需要开启SSL，即HTTPS访问，首先需要点击你新增的域名：

![image-20210304151704448](/images/blog/将个人网站搭建神器WordPress安装到个人服务器上/image-20210304151704448.png)

之后在弹出的窗口中按照下图的顺序进行操作：

![image-20210304151607213](/images/blog/将个人网站搭建神器WordPress安装到个人服务器上/image-20210304151607213-1614842512753.png)

操作完毕后就可以开启SSL功能。

## 2.3 配置Nginx

这里需要使用到Nginx的反向代理，**反向代理就是客户端不知道服务器使用了代理。**

关于Nginx的正向代理和反向代理概念这里也就不多进行讲解了，搜索引擎上面一搜一大堆。

根据下图进行操作：

![image-20210304125801811](/images/blog/将个人网站搭建神器WordPress安装到个人服务器上/image-20210304125801811-1614842509070.png)

代码：

```json
location ~ {
    try_files /_not_exists_ @backend;
}

location @backend {
    proxy_connect_timeout 60s;
    proxy_read_timeout 60s;
    client_max_body_size 2000m;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_pass http://127.0.0.1:7000;
}
```

# 3. 安装docker

在**软件商店搜索docker**点击安装，等待安装结束。

![image-20210304130038628](/images/blog/将个人网站搭建神器WordPress安装到个人服务器上/image-20210304130038628-1614842503555.png)

# 4. 安装docker-compose

Linux下是默认没有安装docker-compose，所以我们要进行手动安装：

1、输入下面的命令安装docker-compose：

```shell
curl -L "https://github.com/docker/compose/releases/download/1.28.5/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
```

2、给它添加权限让它能够启动：

```shell
sudo chmod +x /usr/local/bin/docker-compose
```

创建`docker-compose.yml`文件，写入下面的代码：

```yml
version: '3.1'

services:

  wordpress:
    image: wordpress
    restart: always
    ports:
      - 7000:80
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: exampleuser
      WORDPRESS_DB_PASSWORD: examplepass
      WORDPRESS_DB_NAME: exampledb
    volumes:
      - wordpress:/var/www/html

  db:
    image: mysql:5.7
    restart: always
    environment:
      MYSQL_DATABASE: exampledb
      MYSQL_USER: exampleuser
      MYSQL_PASSWORD: examplepass
      MYSQL_RANDOM_ROOT_PASSWORD: '1'
    volumes:
      - db:/var/lib/mysql

volumes:
  wordpress:
  db:
```

其中：

- `WORDPRESS_DB_NAME`-`MYSQL_DATABASE`
- `WORDPRESS_DB_USER`-`MYSQL_USER`
- `WORDPRESS_DB_PASSWORD`-`MYSQL_PASSWORD`

这三组的值可以任意修改，但是必须一一对应，也就是`WORDPRESS_DB_NAME`必须和`MYSQL_DATABASE`的值相同。

**将该文件放入到服务器任意的文件夹下。**

# 5. 启动容器

通过

```shell
cd (docker-compose.yml文件所在的地址)
```

然后输入指令：

```shell
docker-compose up
```

等待片刻，访问你最开始解析到服务器的域名，应该就可以看到WordPress初始化界面。

# 6. 最后

使用WordPress来搭建个人网站或者博客是一件非常方便的事情，并且它还具有非常强的拓展性，让你不用学编程也可以轻松搭建自己的网站。

