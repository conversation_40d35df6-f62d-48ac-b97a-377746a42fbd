---
title: "再谈Promise"
date: 2020-8-17 13:09:35
categories:
  - "面试"
tags:
  - "JavaScript"
---


之前我写了一篇介绍Promise的文章：[面试题常客之-Promise](/2020/07/08/面试/面试题常客之-promise/)，但是没有实际的例子。

因为平时我做的项目都比较小，所以处理异步函数一般都使用`async await`，很少用到`Promise.then`，但是这一天就碰到了这么一个问题。

# 1. 问题

有6个异步请求，分别是`A1`，`A2`，`A3`，`B1`，`B2`，`B3`。而`A1`，`A2`，`A3`的执行顺序是`A1→A2→A3`，并且`A2`要获得`A1`取得的数据，而`B1`，`B2`，`B3`在`A1`，`A2`，`A3`按照顺序执行完成后同时执行，并且只能通过Promise实现。

![A1A2A3](/images/Interview/A1A2A3.jpg)

当时听题的时候被问的一愣一愣的，但是现在回想起来，使用`async await`可能是最简单的方法，但是题目中明确的说明了只能使用Promise进行实现，那么这道题目应该怎么解呢。

首先我们用`setTimeout`来模拟出上面的这些数据：

```js
let a1 = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      let time = 1;
      console.log("a1经过1s获得了数据");
      resolve(time);
    }, 1000);
  });
};

let a2 = (time) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      let newTime = time + 1;
      console.log("a2经过" + newTime + "s获得了数据");
      resolve(newTime);
    }, 3000);
  });
};

let a3 = (time) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      let newTime = time + 1;
      console.log("a3经过" + newTime + "s获得了数据");
      resolve(newTime);
    }, 4000);
  });
};

let b1 = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("b1经过1s获得了数据");
    }, 1000);
  });
};

let b2 = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("b2经过1s获得了数据");
    }, 1000);
  });
};

let b3 = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("b3经过1s获得了数据");
    }, 1000);
  });
};
```

# 2. Promise实现

嵌套实现：

```javascript
a1().then(res => {
  a2(res).then(res => {
    a3(res).then(res=>{
      b1();
      b2();
      b3();
    });
  });
});
```

`.then`链式调用实现：

```js
a1().then(res => {
  return res;
}).then(res => {
  return a2(res);
}).then(res => {
  return a3(res);
}).then(res => {
  b1();
  b2();
  b3();
});
```

# 3. async await实现

没有对比就没有伤害，`async await`做为ES7的终极大招，来实现这个问题。

看看和Promise的区别。

```javascript
(async () => {
  let time = await a1();
  time = await a2(time);
  await a3(time);
  b1();
  b2();
  b3();
})();
```

这...只能说`async await`牛逼。
