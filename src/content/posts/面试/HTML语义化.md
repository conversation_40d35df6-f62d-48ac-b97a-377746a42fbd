---
title: "HTML语义化"
date: 2020-7-31 12:22:39
categories:
  - "面试"
tags:
  - "HTML"
---


# 1. 为什么需要语义化

语义化有下面几个好处：

- 易修改、易维护。
- 阅读HTML源代码时更容易进行定位。
- 对搜索引擎更加的友好，更加方便SEO。
- 在未来的HTML中，浏览器可能会提供更多的标签。

语义标签不会做任何的事情，仅仅只是为了规范HTML结构，实质效果和一个`<div>`标签没有任何区别，也不会对内容造成本质影响，但是现在随着前端技术的广度和深度逐渐扩大，我们就需要对代码制定一系列的规范，而不能随心所欲想怎么写就怎么写。

当然，如果是不开源的个人项目，也不想要做SEO，只要自己能看懂，就可以随心所欲。**不过建议还是在个人写代码的时候就遵循规范，因为习惯了一时半会改不过来。**

其实HTML的语义化，用下面一张图就可以很直观的看到：

![image-20200417132554712](/images/web/image-20200417132554712.png)

# 2. 头部`<header>`

1. 头部标签，有一般来说用来标注内容的标题，或者标注网页的页眉，内部可以包含页面导航。
2. 一般不在内容中使用，除非在文章内容标题附带其它信息的情况下：发布时间、作者等。
3. 网页中可以包含多个`<header>`元素。按照 HTML5 的规定，`<header>`都应包含某个级别的标题，所以应隐式或显式地包含标题，通常将不希望显示的标题设置为`display: none;`，一方面遵守规范，另一方面则提供了无障碍阅读而不至于影响到页面设计。

# 3. 导航栏`<nav>`

一个页面可以包含多个`<nav>`元素，但通常仅仅在页面的主要导航部分使用它。导航部分的常见示例是菜单，目录和索引。

- 并不是所有的链接都必须使用`<nav>`元素，它只用来将一些热门的链接放入导航栏,例如`<footer>`元素就常用来在页面底部包含一个不常用到,没必要加入`<nav>`的链接列表。
- 一个网页也可能含有多个`<nav>`元素，例如一个是网站内的导航列表，另一个是本页面内的导航列表。
- 对于屏幕阅读障碍的人，可以使用这个元素来确定是否忽略初始内容。

# 4. 附注`<aside>`

`<aside>`元素并不仅仅是侧栏，表示一个和其余页面内容几乎无关的部分，被认为是独立于该内容的一部分并且可以被单独的拆分出来而不会使整体受影响。其通常表现为侧边栏或者标注框（call-out boxes）。

当`<aside>`用于侧栏时，其表示整个网页的附加内容。通常的广告区域、搜索、分享链接则位于侧栏。侧栏中的`<aside>`元素规定了一个区域，通常是带有标题的内容。

`<section>`标签适合标记的内容区块：

- 与页面主体并列显示的小内容块。
- 独立性内容，清单、表单等。
- 分组内容，如 CMS 系统中的文章分类区块。
- 比较长文档的一部分，可能仅仅是为了正确规定页面大纲。

# 5. 页脚`<footer>`

`<footer`标签仅仅可以包含版权、来源信息、法律限制等等之类的文本或链接信息。如果想要在页脚中包含其它内容，可以使用熟悉的`<div>`来帮忙。

```html
<div>
  <aside>
  <!-- 其它内容 -->
  </aside>
  
  <footer>
    <!-- 法律、版权、来源、联系信息等 -->
  </footer>
</div>
```

# 6. 主要内容`<main>`

`<main>`元素呈现了文档的`<body>`或应用的主体部分。主体部分由与文档直接相关，或者扩展于文档的中心主题、应用的主要功能部分的内容组成。**一般来讲：除去头部、尾部、侧栏等其它部分，剩下的就是主体部分。**

`HTML5.1`中规定了一个`<main>`标签来标识主体内容。该标签通常是`<body>`的子标签，或者是全局`<div>`的子标签。不建议在页面的其它子标签中进行使用，也就是说，一个页面最好仅仅只拥有一个`<main>`标签。

该标签可以帮助屏幕阅读工具或者爬虫迅速识别页面的主题部分，从而让访问者或者爬虫更容易得到有用的信息。

# 7. 文章`<article>`

`<article>`表示文档、页面、应用或网站中的独立结构，其意在成为可独立分配的或可复用的结构，如在发布中，它可能是论坛帖子、杂志或新闻文章、博客、用户提交的评论、交互式组件，或者其他独立的内容项目。

文章中包含插图时，使用新的语义元素`<figure>`标签。

```html
<article>
  <h1>标题</h1>
  <p>
    <!-- 内容 -->
  </p>
  <figure>
    <img src="#" alt="插图">
    <figcaption>这是一个插图</figcaption> <!--文章中插图图像的标题-->
  </figure>
</article>
```

上述情况下，`<figcaption>`包含了关于插图的详细解释，则`<img>`的`alt`属性可以略去。

不过目前大多数框架中，依然没有在文章中应用`<figure>`标签。比如：`Hexo`。

# 8. 万能容器`<div>`

`<div>`元素 (或 HTML 文档分区元素) 是一个通用型的流内容容器，在不使用CSS的情况下，其对内容或布局没有任何影响。

再过去还没有推行语义化之前，一个页面的构建会大量的使用`<div>`，当然在现在，你不清楚到底该使用什么语义化标签时，或者没有对应的语义化标签，都可以使用`<div>`标签。

# 9. 其它标签

除了这些页面结构的标签之外，其实在HTML中还有更多的语义化标签。

比如标题标签`<h1><h2><h3><h4>...`尤其是`<h1>`标签，推荐一个界面中仅仅使用一次，用来突出页面中最重要的标题，因为`<h1>`标签对SEO来说至关重要。
