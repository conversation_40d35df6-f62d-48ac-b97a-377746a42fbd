---
title: "面试题常客之-Promise"
date: 2020-07-08 18:12:54
categories:
  - "面试"
tags:
  - "JavaScript"
---


# 1. Promise是什么

`Promise` 是异步编程的一种解决方案，比传统的解决方案**回调函数和事件**更合理和更强大。简单说就是一个容器，里面保存着某个未来才会结束的事件（通常是一个异步操作）的结果。从语法上说，`Promise` 是一个对象，从它可以获取异步操作的消息。`Promise` 提供统一的 `API`，各种异步操作都可以用同样的方法进行处理。

# 2. 解决的痛点

`Promise` 对象是 `JavaScript` 的异步操作解决方案，为异步操作提供统一接口。它起到代理作用（`proxy`），充当异步操作与回调函数之间的中介，使得异步操作具备同步操作的接口。`Promise` 可以让异步操作写起来，就像在写同步操作的流程，而不必一层层地嵌套回调函数。

1. 回调地狱，代码难以维护。
2. `promise`可以支持多个并发的请求，获取并发请求中的数据。
3. `promise`可以解决可读性的问题，异步的嵌套带来的可读性的问题。
4. `promise`可以解决信任问题，对于回调过早、回调过晚或没有调用和回调次数太少或太多，由于`promise`只能决议一次，决议值只能有一个，决议之后无法改变，任何then中的回调也只会被调用一次，所以这就保证了`Promise`可以解决信任问题。

# 3. 其它解决方案

1. `setTimeout`：缺点不精确，只是确保在一定时间后加入到任务队列，并不保证立马执行。只有执行引擎栈中的代码执行完毕，主线程才会去读取任务队列。
2. 事件监听：任务的执行不取决于代码的顺序，而取决于某个事件是否发生。
3. `Generator`函数虽然将异步操作表示得很简洁，但是流程管理却不方便（即何时执行第一阶段、何时执行第二阶段），即如何实现自动化的流程管理。
4. `async`/`await`终极解决方案，但是过多的使用容易造成性能降低，一般只用在必须要使用的地方。

# 4. Promise 原理

`Promise` 对象是一个代理对象（代理一个值），被代理的值在`Promise`对象创建时可能是未知的。它允许你为异步操作的成功和失败分别绑定相应的处理方法（handlers）。 这让异步方法可以像同步方法那样返回值，但并不是立即返回最终执行结果，而是一个能代表未来出现的结果的`promise`对象

一个 `Promise`有以下几种状态:

- `pending`: 初始状态，既不是成功，也不是失败状态。
- `fulfilled`: 意味着操作成功完成。
- `rejected`: 意味着操作失败。

`pending` 状态的 `Promise` 对象可能会变为fulfilled 状态并传递一个值给相应的状态处理方法，也可能变为失败状态（`rejected`）并传递失败信息。当其中任一种情况出现时，`Promise` 对象的 `then` 方法绑定的处理方法（`handlers` ）就会被调用（`then`方法包含两个参数：`onfulfilled` 和 `onrejected`，它们都是 `Function` 类型。当`Promise`状态为`fulfilled`时，调用 `then` 的 `onfulfilled` 方法，当`Promise`状态为`rejected`时，调用 `then` 的 `onrejected` 方法， 所以在异步操作的完成和绑定处理方法之间不存在竞争）。

因为 `Promise.prototype.then` 和 `Promise.prototype.catch` 方法返回`promise` 对象， 所以它们可以被链式调用。

![img](/images/promises.png)

# 5. Promise 创建

创建一个`Promise`非常简单，使用 new 来调用 Promise 的构造器来进行实例化。

```js
let promise =new Promise((resolve, reject)=>{
    // 异步处理
    // 处理结束后、调用resolve 或 reject
})
```

`Promise` 构造函数包含一个参数和一个带有 `resolve`（解析）和 `reject`（拒绝）两个参数的回调。在回调中执行一些操作（例如异步），如果一切都正常，则调用 `resolve`，否则调用 `reject`。

# 6. Promise 常用的方法

## 6.1 then

`then`方法可以接受两个回调函数作为参数。

- 第一个回调函数是`Promise`对象的状态变为`resolved`时调用。
- 第二个回调函数是`Promise`对象的状态变为`rejected`时调用。

其中，第二个函数是可选的，不一定要提供。这两个函数都接受`Promise`对象传出的值作为参数。

可以采用链式写法，例如：

```js
getJSON("/posts.json").then(function(json) {
  return json.post;
}).then(function(post) {
  // ...
});
```

## 6.2 catch

- 指定发生错误时的回调函数。

- Promise 内部的错误不会影响到 Promise 外部的代码，通俗的说法就是“Promise 会吃掉错误”。

- 一般来说，不要在`then()`方法里面定义 Reject 状态的回调函数（即`then`的第二个参数），总是使用`catch`方法。

- 没有报错时，会跳过`catch()`

  ```javascript
  const someAsyncThing = function() {
    return new Promise(function(resolve, reject) {
      var x = 2;
      resolve(x + 2);
    });
  };
  
  someAsyncThing()
    //没有报错时会跳过
    .catch(function(error) {
      console.log("oh no", error);
    })
    .then(function() {
      console.log("carry on");
    });
  
  ```

## 6.3 resolve

有时需要将现有对象转为 Promise 对象，`Promise.resolve()`方法就起到这个作用。

```javascript
const jsPromise = Promise.resolve($.ajax('/whatever.json'));
```

上面代码将 jQuery 生成的`deferred`对象，转为一个新的 Promise 对象。

`Promise.resolve()`等价于下面的写法。

```javascript
Promise.resolve('foo')
// 等价于
new Promise(resolve => resolve('foo'))
```

`Promise.resolve`方法的参数分成四种情况。

**（1）参数是一个 Promise 实例**

如果参数是 Promise 实例，那么`Promise.resolve`将不做任何修改、原封不动地返回这个实例。

**（2）参数是一个`thenable`对象**

`thenable`对象指的是具有`then`方法的对象，比如下面这个对象。

```javascript
let thenable = {
  then: function(resolve, reject) {
    resolve(42);
  }
};
```

`Promise.resolve`方法会将这个对象转为 Promise 对象，然后就立即执行`thenable`对象的`then`方法。

```javascript
let thenable = {
  then: function(resolve, reject) {
    resolve(42);
  }
};

let p1 = Promise.resolve(thenable);
p1.then(function(value) {
  console.log(value);  // 42
});
```

上面代码中，`thenable`对象的`then`方法执行后，对象`p1`的状态就变为`resolved`，从而立即执行最后那个`then`方法指定的回调函数，输出 42。

**（3）参数不是具有`then`方法的对象，或根本就不是对象**

如果参数是一个原始值，或者是一个不具有`then`方法的对象，则`Promise.resolve`方法返回一个新的 Promise 对象，状态为`resolved`。

```javascript
const p = Promise.resolve('Hello');

p.then(function (s){
  console.log(s)
});
// Hello
```

上面代码生成一个新的 Promise 对象的实例`p`。由于字符串`Hello`不属于异步操作（判断方法是字符串对象不具有 then 方法），返回 Promise 实例的状态从一生成就是`resolved`，所以回调函数会立即执行。`Promise.resolve`方法的参数，会同时传给回调函数。

**（4）不带有任何参数**

`Promise.resolve()`方法允许调用时不带参数，直接返回一个`resolved`状态的 Promise 对象。

所以，如果希望得到一个 Promise 对象，比较方便的方法就是直接调用`Promise.resolve()`方法。

```javascript
const p = Promise.resolve();

p.then(function () {
  // ...
});
```

上面代码的变量`p`就是一个 Promise 对象。

需要注意的是，立即`resolve()`的 Promise 对象，是在本轮“事件循环”（event loop）的结束时执行，而不是在下一轮“事件循环”的开始时。

```javascript
setTimeout(function () {
  console.log('three');
}, 0);

Promise.resolve().then(function () {
  console.log('two');
});

console.log('one');

// one
// two
// three
```

上面代码中，`setTimeout(fn, 0)`在下一轮“事件循环”开始时执行，`Promise.resolve()`在本轮“事件循环”结束时执行，`console.log('one')`则是立即执行，因此最先输出。

## 6.4 reject

`Promise.reject(reason)`方法也会返回一个新的 Promise 实例，该实例的状态为`rejected`。

```javascript
const p = Promise.reject('出错了');
// 等同于
const p = new Promise((resolve, reject) => reject('出错了'))

p.then(null, function (s) {
  console.log(s)
});
// 出错了
```

上面代码生成一个 Promise 对象的实例`p`，状态为`rejected`，回调函数会立即执行。

注意，`Promise.reject()`方法的参数，会原封不动地作为`reject`的理由，变成后续方法的参数。这一点与`Promise.resolve`方法不一致。

```javascript
const thenable = {
  then(resolve, reject) {
    reject('出错了');
  }
};

Promise.reject(thenable)
.catch(e => {
  console.log(e === thenable)
})
// true
```

上面代码中，`Promise.reject`方法的参数是一个`thenable`对象，执行以后，后面`catch`方法的参数不是`reject`抛出的“出错了”这个字符串，而是`thenable`对象。

## 6.5 all

- 将多个 Promise 实例，包装成一个新的 Promise 实例。
- 只有每个实例的状态都变成`fulfilled`，或者其中有一个变为`rejected`，才会调用`Promise.all`方法后面的回调函数。
- 如果作为参数的 Promise 实例，自己定义了`catch`方法，那么它一旦被`rejected`，并不会触发`Promise.all()`的`catch`方法。

例如：

```js
// 生成一个Promise对象的数组
const promises = [2, 3, 5, 7, 11, 13].map(function (id) {
  return getJSON('/post/' + id + ".json");
});

Promise.all(promises).then(function (posts) {
  // ...
}).catch(function(reason){
  // ...
});
```

## 6.6 race

- 将多个 `Promise` 实例，包装成一个新的 `Promise` 实例。
- 率先改变的 `Promise` 实例的返回值就传给`P`的回调函数。

```js
const p = Promise.race([p1, p2, p3]);
```

## 6.7 finally

**ES9的标准，用于指定不管 Promise 对象最后状态如何，都会执行的操作。**

`finally`方法的回调函数不接受任何参数，这意味着没有办法知道，前面的 Promise 状态到底是`fulfilled`还是`rejected`。这表明，`finally`方法里面的操作，应该是与状态无关的，不依赖于 Promise 的执行结果。

`finally`本质上是`then`方法的特例。

```javascript
promise.finally(() => {
  // 语句
});

// 等同于
promise.then(
  result => {
    // 语句
    return result;
  },
  error => {
    // 语句
    throw error;
  }
);

```

实现方法

```javascript
Promise.prototype.finally = function (callback) {
  let P = this.constructor;
  return this.then(
    value  => P.resolve(callback()).then(() => value),
    reason => P.resolve(callback()).then(() => { throw reason })
  );
};
```

# 7. async,await

为了解决`promise.then`过多导致代码可读性变差问题，在ES7中发布了`async,await`。使用`async,await`就可以将异步代码转换为同步代码。

- `async`就是将函数返回值使用`Promise.resolve()`包裹了下，和`then`中处理返回值一样，并且`await`只能配套`async`使用。
- `await`就是`generator`加上`Promise`的语法糖，且内部实现了自动执行`generator`。
- `await` 将异步代码改造成同步代码，**如果多个异步操作没有依赖性而使用 await 会导致性能上的降低，所以不能滥用。**

# 8. 总结

其实`Promise`的原理并不算太复杂，但是理解起来有一定难度，一般来讲只要会用就可以了，如果有闲心的话再去研究。

但是`Promise`是面试的时候的一个重点问题，所以还是建议多看看资料，其实这种原理性的问题，对于我来说，几天不看就忘记了...

# 9. 参考资料

https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Promise

https://juejin.im/post/5b32f552f265da59991155f0

https://es6.ruanyifeng.com/#docs/promise
