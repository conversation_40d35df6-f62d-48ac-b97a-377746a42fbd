---
title: "更加实用的Grid布局"
date: 2020-10-1 20:59:00
categories:
  - "面试"
tags:
  - "CSS3"
  - "Flex"
---


之前有一篇文章是讲解`CSS`中的`Flex`布局

[非常实用的Flex布局](/2020/10/01/面试/非常实用的flex布局/)

<br/>所以本篇文章要讲解另一个更为强大的布局方式`Grid`。


# 1. 入门

在早期的网页布局中，如果要实现非常复杂的布局是很麻烦的事情，只有通过`CSS`框架，或者自行花费大量的时间去写相关的`CSS`样式，所以在10年以前，网页的页面布局都非常的简陋。

`Flex`和`Grid`非常的相似，同时也存在着非常大的不同，简单的来说`Flex`更适合用在单行的布局上，而多行布局使用`Flex`虽然能够实现，但是非常的麻烦。

`Grid`的拿手好戏就是多行布局，它可以将容器划分为“行”和“列”，如果有做过控制台开发小游戏的朋友应该清楚，**在一个平面中要做出一个游戏界面，就需要使用二维数组进行布局，而`Grid`就恰好是“二维布局”。**

正因为`Grid`是二维布局，所以它远比一维布局的`Flex`更加的强大。

<style>
  .grid-container {
    width: 300px;
    background-color: #dbdbdb;
    display: grid;
  }

  .grid-container > div {
    border: 1px solid black;
    margin: 0 -1px -1px 0;
  }

  .grid-container > div:hover {
    z-index: 1;
    border: 2px solid #e20303;
  }

  .grid-container > div[data-index]::before {
    content: attr(data-index);
    background-color: #e5a9a9;
    color: #fff;
    position: absolute;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 14px;
    text-align: center;
    line-height: 20px;
    z-index: 1;
  }

  label {
    margin-left: 10px;
  }
</style>

# 2. 基本概念

## 2.1 container和item

跟`Flex`一样，`Grid`布局也只对顶层子元素有效，比如：

```html
<div class="container">
  <div class="item"><p>1</p></div>
  <div class="item"><p>2</p></div>
  <div class="item"><p>3</p></div>
</div>
```


将`container`设置为`Grid`，那么`Grid`布局就仅仅只对`item`生效，对`<p>`是不生效的。

  ## 2.2 row和column

  `container`中水平区域称为`row`（行），垂直区域称为`column`（列）。

  ## 2.3 cell

  行和列的交叉区域，就称为`cell`（单元格）。

  ## 2.4 grid line

  划分单元格的线，就称为`grid line`（网格线），水平网格线划分出行，垂直网格线划分出列。

  # 3. 容器属性

  ## 3.1 display属性

  `display: grid`指定一个容器采用网格布局。

```css
div {
    display: grid; /*块级元素*/
    display: inline-grid; /*行内元素。*/
}
```


  **注意，设为网格布局以后，容器子元素（项目）的`float`、`display: inline-block`、`display: table-cell`、`vertical-align`和`column-*`等设置都将失效。**

  ## 3.2 grid-template-columns 属性， grid-template-rows 属性

  - `grid-template-columns`属性定义每一列的列宽。
  - `grid-template-rows`属性定义每一行的行高。

```css
.grid-container {
    display: grid;
    grid-template-columns: 100px 100px 100px;
    grid-template-rows: 100px 100px 100px;
}
```

<div class="grid-container" style="grid-template-columns: 100px 100px 100px;grid-template-rows: 100px 100px 100px;">
  <div data-index="1"></div>
  <div data-index="2"></div>
  <div data-index="3"></div>
  <div data-index="4"></div>
  <div data-index="5"></div>
  <div data-index="6"></div>
  <div data-index="7"></div>
  <div data-index="8"></div>
  <div data-index="9"></div>
</div>
<br/>

除了用绝对单位，也可以设置为容器的百分之多少：

```css
.grid-container {
    display: grid;
    grid-template-columns: 33.33% 33.33% 33.33%;
    grid-template-rows: 33.33% 33.33% 33.33%;
}
```

<div class="grid-container"
     style="width: 300px;height: 300px;grid-template-columns: 33.33% 33.33% 33.33%;grid-template-rows: 33.33% 33.33% 33.33%;">
  <div data-index="1"></div>
  <div data-index="2"></div>
  <div data-index="3"></div>
  <div data-index="4"></div>
  <div data-index="5"></div>
  <div data-index="6"></div>
  <div data-index="7"></div>
  <div data-index="8"></div>
  <div data-index="9"></div>
</div>

### 3.2.1 repeat()

上面的代码用`repeat()`改写如下。

```css
.grid-container {
    display: grid;
    grid-template-columns: repeat(3, 33.33%);
    grid-template-rows: repeat(3, 33.33%);
}
```

`repeat()`接受两个参数，第一个参数是重复的次数（上例是3），第二个参数是所要重复的值。

同时所要重复的值可以是多个，例如：

```css
.grid-container {
    display: grid;
    grid-template-columns: repeat(2, 50px 30px 60px);
    grid-template-rows: repeat(2, 50%);
}
```

效果如下：

<div class="grid-container"
     style="height: 200px;grid-template-columns: repeat(2, 50px 30px 60px);grid-template-rows: repeat(2, 50%);">
  <div data-index="1"></div>
  <div data-index="2"></div>
  <div data-index="3"></div>
  <div data-index="4"></div>
  <div data-index="5"></div>
  <div data-index="6"></div>
  <div data-index="7"></div>
  <div data-index="8"></div>
  <div data-index="9"></div>
</div>

### 3.2.2 auto-fill 关键字

如果单元格的大小固定，但是容器大小不确定的情况下，使用`auto-fill`就可以让每行或者每列尽可能多的收纳单元格。

```css
.grid-container {
    display: grid;
    grid-template-rows: repeat(2, 50%);
}
```

点一点看效果：

<div>
  <label>
    <input onclick="gridClick('autoFill',value)" type="radio" name="autoFill" value="repeat(auto-fill, 30px)" checked>
    grid-template-columns: repeat(auto-fill, 30px);
  </label>
  <label>
    <input onclick="gridClick('autoFill',value)" type="radio" name="autoFill" value="repeat(auto-fill, 40px)">
    grid-template-columns: repeat(auto-fill, 40px);
  </label>
  <label>
    <input onclick="gridClick('autoFill',value)" type="radio" name="autoFill" value="repeat(auto-fill, 50px)">
    grid-template-columns: repeat(auto-fill, 50px);
  </label>
  <div id="autoFill" class="grid-container"
       style="height: 200px;grid-template-columns: repeat(auto-fill, 30px);grid-template-rows: repeat(2, 50%);">
    <div data-index="1"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

### 3.2.3 fr 关键字

`fr`关键字（fraction 的缩写，意为"片段"）。如果两列的宽度分别为`1fr`和`2fr`，就表示后者是前者的两倍。

```css
.grid-container {
    display: grid;
    grid-template-rows: repeat(3, 33.33%);
}
```

<div>
  <label>
    <input onclick="gridClick('fr',value)" type="radio" name="fr" value="150px 1fr 2fr" checked> grid-template-columns:
    150px 1fr 2fr
  </label>
  <label>
    <input onclick="gridClick('fr',value)" type="radio" name="fr" value="150px 2fr 2fr"> grid-template-columns: 150px
    2fr 2fr
  </label>
  <label>
    <input onclick="gridClick('fr',value)" type="radio" name="fr" value="100px 3fr 2fr"> grid-template-columns: 100px
    3fr 2fr
  </label>
  <div id="fr" class="grid-container"
       style="height: 300px;grid-template-columns: 150px 1fr 2fr;grid-template-rows: repeat(3, 33.33%);">
    <div data-index="1"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>

</div>

### 3.2.4 minmax()

`minmax()`函数产生一个长度范围，表示长度就在这个范围之中。它接受两个参数，分别为最小值和最大值。

```css
grid-template-columns: 1fr 1fr minmax(100px, 1fr);
```

上面代码中，`minmax(100px, 1fr)`表示列宽不小于`100px`，不大于`1fr`。

### 3.2.5 auto 关键字

`auto`关键字表示由浏览器自己决定长度。

```css
.grid-container {
    display: grid;
    grid-template-columns: 40px auto 40px;
    grid-template-rows: 100px 100px 100px;
}
```

上面代码中，第二列的宽度，基本上等于该列单元格的最大宽度，除非单元格内容设置了`min-width`，且这个值大于最大宽度。

<div class="grid-container" style="grid-template-columns: 40px auto 40px;grid-template-rows: 100px 100px 100px;">
  <div data-index="1"></div>
  <div data-index="2"></div>
  <div data-index="3"></div>
  <div data-index="4"></div>
  <div data-index="5"></div>
  <div data-index="6"></div>
  <div data-index="7"></div>
  <div data-index="8"></div>
  <div data-index="9"></div>
</div>
<br/>

### 3.2.6 网格线的名称

`grid-template-columns`属性和`grid-template-rows`属性里面，还可以使用方括号，指定每一根网格线的名字，方便以后的引用。

```css
.grid-container {
    display: grid;
    grid-template-columns: [c1] 100px [c2] 100px [c3] auto [c4];
    grid-template-rows: [r1] 100px [r2] 100px [r3] auto [r4];
}
```

上面代码指定网格布局为3行 x 3列，因此有4根垂直网格线和4根水平网格线。方括号里面依次是这八根线的名字。

网格布局允许同一根线有多个名字，比如`[fifth-line row-5]`。


## 3.3 grid-row-gap 属性， grid-column-gap 属性， grid-gap 属性

- `grid-row-gap`属性设置行与行的间隔（行间距）。
- `grid-column-gap`属性设置列与列的间隔（列间距）。
- `grid-gap`属性是`grid-column-gap`和`grid-row-gap`的合并简写形式。

注：现在可以简写成`row-gap`、`column-gap`、`gap`。

点一点看效果：

`grid-row-gap`：

<div>
  <label>
    <input onclick="gridClick('gridRowGap',value)" type="radio" name="grid-row-gap" value="10px" checked> grid-row-gap:
    10px
  </label>
  <label>
    <input onclick="gridClick('gridRowGap',value)" type="radio" name="grid-row-gap" value="20px"> grid-row-gap: 20px
  </label>
  <label>
    <input onclick="gridClick('gridRowGap',value)" type="radio" name="grid-row-gap" value="25px"> grid-row-gap: 25px
  </label>
  <div id="gridRowGap" class="grid-container"
       style="height: 300px;grid-template-columns: repeat(3, 80px);grid-template-rows: repeat(3, 80px);grid-row-gap: 10px;">
    <div data-index="1"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

<br/>

`grid-column-gap`：


<div>
  <label>
    <input onclick="gridClick('gridColumnGap',value)" type="radio" name="grid-column-gap" value="10px" checked>
    grid-column-gap: 10px
  </label>
  <label>
    <input onclick="gridClick('gridColumnGap',value)" type="radio" name="grid-column-gap" value="20px"> grid-column-gap:
    20px
  </label>
  <label>
    <input onclick="gridClick('gridColumnGap',value)" type="radio" name="grid-column-gap" value="25px"> grid-column-gap:
    25px
  </label>
  <div id="gridColumnGap" class="grid-container"
       style="height: 300px;grid-template-columns: repeat(3, 80px);grid-template-rows: repeat(3, 80px);grid-column-gap: 10px;">
    <div data-index="1"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

<br/>


## 3.4 grid-template-areas 属性

网格布局允许指定"区域"（area），一个区域由单个或多个单元格组成。`grid-template-areas`属性用于定义区域。

```css
.brid-container {
    display: grid;
    grid-template-columns: 100px 100px 100px;
    grid-template-rows: 100px 100px 100px;
    grid-template-areas: 'a b c'
                         'd e f'
                         'g h i';
}
```

如果某些区域不需要利用，则使用"点"（`.`）表示。

```css
grid-template-areas: 'a . c'
                     'd . f'
                     'g . i';
```

上面代码中，中间一列为点，表示没有用到该单元格，或者该单元格不属于任何区域。

注意，**区域的命名会影响到网格线。每个区域的起始网格线，会自动命名为`区域名-start`，终止网格线自动命名为`区域名-end`。**

比如，区域名为`header`，则起始位置的水平网格线和垂直网格线叫做`header-start`，终止位置的水平网格线和垂直网格线叫做`header-end`。

## 3.5 grid-auto-flow 属性

### 3.5.1 属性

- `row`：先行后列。
- `column`：先列后行。

- `row dense`："先行后列"，并且尽可能紧密填满，尽量不出现空格。
- `column dense`："先列后行"，并且尽量填满空格。

点一点看效果：


<div>
  <label>
    <input onclick="gridClick('gridAutoFlow',value)" type="radio" name="grid-auto-flow" value="row" checked> row
  </label>
  <label>
    <input onclick="gridClick('gridAutoFlow',value)" type="radio" name="grid-auto-flow" value="column"> column
  </label>
  <div id="gridAutoFlow" class="grid-container"
       style="height: 300px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);">
    <div data-index="1"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>
<br/>

`row dense`

点一点看效果：

<div>
 <label>
    <input onclick="gridClick('rowDense',value)" type="radio" name="rowDense" value="row" checked> row
  </label>
  <label>
    <input onclick="gridClick('rowDense',value)" type="radio" name="rowDense" value="row dense"> row dense
  </label>
  <div id="rowDense" class="grid-container"
       style="height: 400px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);grid-auto-flow: row">
    <div data-index="1" style="grid-column-start: 1;grid-column-end: 3"></div>
    <div data-index="2" style="grid-column-start: 1;grid-column-end: 3"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

<br/>

`column dense`

点一点看效果：

<div>
 <label>
    <input onclick="gridClick('columnDense',value)" type="radio" name="columnDense" value="column" checked> column 
  </label>
  <label>
    <input onclick="gridClick('columnDense',value)" type="radio" name="columnDense" value="column dense"> column dense
  </label>
  <div id="columnDense" class="grid-container"
       style="height: 300px;grid-template-columns: repeat(4, 75px);grid-template-rows: repeat(3, 100px);grid-auto-flow: column;">
    <div data-index="1" style="grid-column-start: 1;grid-column-end: 3;"></div>
    <div data-index="2" ></div>
    <div data-index="3" style="grid-row-start: 2;grid-row-end: 4"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

## 3.6 justify-items 属性， align-items 属性， place-items 属性

- `justify-items`属性设置单元格内容的水平位置（左中右）。
- `align-items`属性设置单元格内容的垂直位置（上中下）。
- `place-items`属性是`align-items`属性和`justify-items`属性的合并简写形式。

### 3.6.1 属性

- `start`：对齐单元格的起始边缘。
- `end`：对齐单元格的结束边缘。
- `center`：单元格内部居中。
- `stretch`：拉伸，占满单元格的整个宽度（默认值）。

点一点看效果：

`justify-items`：

<div>
  <label>
    <input onclick="gridClick('justifyItems',value)" type="radio" name="justify-items" value="start" checked> start
  </label>
  <label>
    <input onclick="gridClick('justifyItems',value)" type="radio" name="justify-items" value="end"> end
  </label>
  <label>
    <input onclick="gridClick('justifyItems',value)" type="radio" name="justify-items" value="center"> center
  </label>
  <label>
    <input onclick="gridClick('justifyItems',value)" type="radio" name="justify-items" value="stretch"> stretch
  </label>
  <div id="justifyItems" class="grid-container"
       style="height: 300px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);justify-items: start;">
    <div style="min-width: 80px" data-index="1"></div>
    <div style="min-width: 80px" data-index="2"></div>
    <div style="min-width: 80px" data-index="3"></div>
    <div style="min-width: 80px" data-index="4"></div>
    <div style="min-width: 80px" data-index="5"></div>
    <div style="min-width: 80px" data-index="6"></div>
    <div style="min-width: 80px" data-index="7"></div>
    <div style="min-width: 80px" data-index="8"></div>
    <div style="min-width: 80px" data-index="9"></div>
  </div>
</div>

<br/>

`align-items`：

<div>
  <label>
    <input onclick="gridClick('alignItems',value)" type="radio" name="align-items" value="start" checked> start
  </label>
  <label>
    <input onclick="gridClick('alignItems',value)" type="radio" name="align-items" value="end"> end
  </label>
  <label>
    <input onclick="gridClick('alignItems',value)" type="radio" name="align-items" value="center"> center
  </label>
  <label>
    <input onclick="gridClick('alignItems',value)" type="radio" name="align-items" value="stretch"> stretch
  </label>
  <div id="alignItems" class="grid-container"
       style="height: 300px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);align-items: start;">
    <div style="min-height: 80px" data-index="1"></div>
    <div style="min-height: 80px" data-index="2"></div>
    <div style="min-height: 80px" data-index="3"></div>
    <div style="min-height: 80px" data-index="4"></div>
    <div style="min-height: 80px" data-index="5"></div>
    <div style="min-height: 80px" data-index="6"></div>
    <div style="min-height: 80px" data-index="7"></div>
    <div style="min-height: 80px" data-index="8"></div>
    <div style="min-height: 80px" data-index="9"></div>
  </div>
</div>

<br/>

## 3.7 justify-content 属性， align-content 属性， place-content 属性

- `justify-content`属性是整个内容区域在容器里面的水平位置（左中右）。
- `align-content`属性是整个内容区域的垂直位置（上中下）。
- `place-content`属性是`align-content`属性和`justify-content`属性的合并简写形式。

### 3.7.1 属性：

- `start`：对齐容器的起始边框。
- `end`：对齐容器的结束边框。
- `center`：容器内部居中。
- `stretch`：项目大小没有指定时，拉伸占据整个网格容器。
- `space-around`：每个项目两侧的间隔相等。所以，项目之间的间隔比项目与容器边框的间隔大一倍。
- `space-between`：项目与项目的间隔相等，项目与容器边框之间没有间隔。
- `space-evenly`：项目与项目的间隔相等，项目与容器边框之间也是同样长度的间隔。

点一点看效果：

`justify-content`：

<div>
  <label>
    <input onclick="gridClick('justifyContent',value)" type="radio" name="justify-content" value="start" checked> start
  </label>
  <label>
    <input onclick="gridClick('justifyContent',value)" type="radio" name="justify-content" value="end"> end
  </label>
  <label>
    <input onclick="gridClick('justifyContent',value)" type="radio" name="justify-content" value="center"> center
  </label>
  <label>
    <input onclick="gridClick('justifyContent',value)" type="radio" name="justify-content" value="stretch"> stretch
  </label>
  <label>
    <input onclick="gridClick('justifyContent',value)" type="radio" name="justify-content" value="space-around">
    space-around
  </label>
  <label>
    <input onclick="gridClick('justifyContent',value)" type="radio" name="justify-content" value="space-between">
    space-between
  </label>
  <label>
    <input onclick="gridClick('justifyContent',value)" type="radio" name="justify-content" value="space-evenly">
    space-evenly
  </label>
  <div id="justifyContent" class="grid-container"
       style="grid-template-columns: repeat(3, 80px);grid-template-rows: repeat(3, 80px);grid-auto-rows: 50px;">
    <div data-index="1"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

<br/>

`align-content`：

<div>
  <label>
    <input onclick="gridClick('alignContent',value)" type="radio" name="align-content" value="start" checked> start
  </label>
  <label>
    <input onclick="gridClick('alignContent',value)" type="radio" name="align-content" value="end"> end
  </label>
  <label>
    <input onclick="gridClick('alignContent',value)" type="radio" name="align-content" value="center"> center
  </label>
  <label>
    <input onclick="gridClick('alignContent',value)" type="radio" name="align-content" value="stretch"> stretch
  </label>
  <label>
    <input onclick="gridClick('alignContent',value)" type="radio" name="align-content" value="space-around">
    space-around
  </label>
  <label>
    <input onclick="gridClick('alignContent',value)" type="radio" name="align-content" value="space-between">
    space-between
  </label>
  <label>
    <input onclick="gridClick('alignContent',value)" type="radio" name="align-content" value="space-evenly">
    space-evenly
  </label>
  <div id="alignContent" class="grid-container"
       style="height: 450px;grid-template-columns: repeat(3, 80px);grid-template-rows: repeat(3, 80px);grid-auto-rows: 50px;">
    <div data-index="1"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

<br/>

## 3.8 grid-auto-columns 属性， grid-auto-rows 属性

当一个网格仅仅只指定为3*3，而如果在指定网格之外还有项目，则可以使用`grid-auto-columns`和`grid-auto-rows`进行指定，如果不指定这两个属性，浏览器完全根据单元格内容的大小，决定新增网格的列宽和行高。

点一点看效果：

<div>
  <label>
    <input onclick="gridClick('gridAutoRows',value)" type="radio" name="grid-auto-rows" value="50px" checked>
    grid-auto-rows: 50px;
  </label>
  <label>
    <input onclick="gridClick('gridAutoRows',value)" type="radio" name="grid-auto-rows" value="80px"> grid-auto-rows:
    80px;
  </label>
  <label>
    <input onclick="gridClick('gridAutoRows',value)" type="radio" name="grid-auto-rows" value="100px"> grid-auto-rows:
    100px;
  </label>
  <div id="gridAutoRows" class="grid-container"
       style="height: 400px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);grid-auto-rows: 50px;">
    <div data-index="1"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9" style="grid-area: 4"></div>
  </div>
</div>

<br/>

## 3.9 grid-template 属性， grid 属性

- `grid-template`属性是`grid-template-columns`、`grid-template-rows`和`grid-template-areas`这三个属性的合并简写形式。
- `grid`属性是`grid-template-rows`、`grid-template-columns`、`grid-template-areas`、 `grid-auto-rows`、`grid-auto-columns`、`grid-auto-flow`这六个属性的合并简写形式。

# 4. 项目属性

## 4.1 grid-column-start 属性， grid-column-end 属性， grid-row-start 属性， grid-row-end 属性

- `grid-column-start`属性：左边框所在的垂直网格线
- `grid-column-end`属性：右边框所在的垂直网格线
- `grid-row-start`属性：上边框所在的水平网格线
- `grid-row-end`属性：下边框所在的水平网格线

```css
.item {
    grid-column-start: 1;
    grid-row-start: 1;
    grid-row-end: 2;
}
```

下面只改变`grid-column-end`的效果，其它的属性同理：

<div>
  <label>
    <input onclick="gridClick('gridColumnEnd',value)" type="radio" name="grid-column-end" value="2" checked>
    grid-column-end: 2
  </label>
  <label>
    <input onclick="gridClick('gridColumnEnd',value)" type="radio" name="grid-column-end" value="3"> grid-column-end: 3
  </label>
  <label>
    <input onclick="gridClick('gridColumnEnd',value)" type="radio" name="grid-column-end" value="4"> grid-column-end: 4
  </label>
  <div class="grid-container"
       style="height: 300px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);">
    <div id="gridColumnEnd" data-index="1" style="grid-column-start: 1;grid-row-start: 1;grid-row-end: 2;"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
  </div>
</div>


## 4.2 grid-column 属性， grid-row 属性

`grid-column`属性是`grid-column-start`和`grid-column-end`的合并简写形式。

`grid-row`属性是`grid-row-start`属性和`grid-row-end`的合并简写形式。

## 4.3 grid-area 属性

`grid-area`属性指定项目放在哪一个区域。

```css
.item {
    grid-area: 3;
}
```

<div class="grid-container"
     style="height: 300px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);">
  <div data-index="1" style="grid-area: 3"></div>
  <div data-index="2"></div>
  <div data-index="3"></div>
  <div data-index="4"></div>
  <div data-index="5"></div>
  <div data-index="6"></div>
  <div data-index="7"></div>
  <div data-index="8"></div>
  <div data-index="9"></div>
</div>

<br/>

`grid-area`属性还可用作`grid-row-start`、`grid-column-start`、`grid-row-end`、`grid-column-end`的合并简写形式，直接指定项目的位置。

```css
.item {
    grid-area: 1 / 1 / 3 / 3;
}
```


<div class="grid-container"
     style="height: 300px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);">
  <div data-index="1" style="grid-area: 1 / 1 / 3 / 3"></div>
  <div data-index="2"></div>
  <div data-index="3"></div>
  <div data-index="4"></div>
  <div data-index="5"></div>
  <div data-index="6"></div>
</div>

## 4.4 justify-self 属性， align-self 属性， place-self 属性

- `justify-self`属性设置单元格内容的水平位置（左中右），跟`justify-items`属性的用法完全一致，但只作用于单个项目。
- `align-self`属性设置单元格内容的垂直位置（上中下），跟`align-items`属性的用法完全一致，也是只作用于单个项目。
- `place-self`属性是`align-self`属性和`justify-self`属性的合并简写形式。

### 4.4.1 属性

- `start`：对齐单元格的起始边缘。
- `end`：对齐单元格的结束边缘。
- `center`：单元格内部居中。
- `stretch`：拉伸，占满单元格的整个宽度（默认值）。

点一点看效果：

`justify-self`：

<div>
  <label>
    <input onclick="gridClick('justifySelf',value)" type="radio" name="justify-self" value="start" checked> start
  </label>
  <label>
    <input onclick="gridClick('justifySelf',value)" type="radio" name="justify-self" value="end"> end
  </label>
  <label>
    <input onclick="gridClick('justifySelf',value)" type="radio" name="justify-self" value="center"> center
  </label>
  <label>
    <input onclick="gridClick('justifySelf',value)" type="radio" name="justify-self" value="stretch"> stretch
  </label>
  <div class="grid-container"
       style="height: 300px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);">
    <div id="justifySelf" data-index="1" style="width: 50px;height: 50px;"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

<br/>

`align-self`：

<div>
  <label>
    <input onclick="gridClick('alignSelf',value)" type="radio" name="align-self" value="start" checked> start
  </label>
  <label>
    <input onclick="gridClick('alignSelf',value)" type="radio" name="align-self" value="end"> end
  </label>
  <label>
    <input onclick="gridClick('alignSelf',value)" type="radio" name="align-self" value="center"> center
  </label>
  <label>
    <input onclick="gridClick('alignSelf',value)" type="radio" name="align-self" value="stretch"> stretch
  </label>
  <div class="grid-container"
       style="height: 300px;grid-template-columns: repeat(3, 100px);grid-template-rows: repeat(3, 100px);">
    <div id="alignSelf" data-index="1" style="width: 50px;height: 50px;"></div>
    <div data-index="2"></div>
    <div data-index="3"></div>
    <div data-index="4"></div>
    <div data-index="5"></div>
    <div data-index="6"></div>
    <div data-index="7"></div>
    <div data-index="8"></div>
    <div data-index="9"></div>
  </div>
</div>

<br/>

# 5. 总结

通过上面的眼花缭乱的属性，可以得知`Grid`比`Flex`强大的多，如果是单行布局建议选用`Flex`，而多行布局选用`Grid`，其实`Grid`和`Flex`在项目中也经常用到，不过我之前看教学视频的时候，貌似大部分讲师都更熟悉`Flex`一些，而`Grid`却很少用到（可能是由于`Grid`出来的比较晚）。所以导致我之前对`Grid`知之甚少，几乎没有用到过。

不过现在大部分现代浏览器都支持了`Grid`，所以可以放心大胆的用，但是如果是想要兼容老版本的`IE`浏览器...那就只能节哀顺变了。

# 6. 参考资料

[CSS Grid 网格布局教程](https://www.ruanyifeng.com/blog/2019/03/grid-layout-tutorial.html)

<script>
  function gridClick(type, value) {
    if (type === 'autoFill') {
      $('#autoFill').css('gridTemplateColumns', value);
    }
    if (type === 'fr') {
      $('#fr').css('gridTemplateColumns', value);
    }
    if (type === 'gridRowGap') {
      $('#gridRowGap').css('gridRowGap', value);
    }
    if (type === 'gridColumnGap') {
      $('#gridColumnGap').css('gridColumnGap', value);
    }
    if (type === 'gridAutoFlow') {
      $('#gridAutoFlow').css('gridAutoFlow', value);
    }
    if (type === 'justifySelf') {
      $('#justifySelf').css('justifySelf', value);
    }
    if (type === 'alignSelf') {
      $('#alignSelf').css('alignSelf', value);
    }
    if (type === 'alignItems') {
      $('#alignItems').css('alignItems', value);
    }
    if (type === 'justifyItems') {
      $('#justifyItems').css('justifyItems', value);
    }
    if (type === 'gridColumnEnd') {
      $('#gridColumnEnd').css('gridColumnEnd', value);
    }
    if (type === 'gridAutoRows') {
      $('#gridAutoRows').css('gridAutoRows', value);
    }
    if (type === 'justifyContent') {
      if (value === 'stretch') {
        $('#justifyContent').css({'gridTemplateColumns': 'none', 'gridTemplateRows': 'none'});
      } else {
        $('#justifyContent').css({'gridTemplateColumns': 'repeat(3, 80px)', 'gridTemplateRows': 'repeat(3, 80px)'});
      }
      $('#justifyContent').css('justifyContent', value);
    }
    if (type === 'alignContent') {
      if (value === 'stretch') {
        $('#alignContent').css({'gridTemplateColumns': 'none', 'gridTemplateRows': 'none'});
      } else {
        $('#alignContent').css({'gridTemplateColumns': 'repeat(3, 80px)', 'gridTemplateRows': 'repeat(3, 80px)'});
      }
      $('#alignContent').css('alignContent', value);
    }
    if (type === 'rowDense') {
      $('#rowDense').css('gridAutoFlow', value);
    }
    if (type === 'columnDense') {
      $('#columnDense').css('gridAutoFlow', value);
    }
  }
</script>

