---
title: "辨别用户身份和储存数据的关键-Cookie"
date: 2020-7-20 16:28:55
categories:
  - "面试"
tags:
  - "Cookie"
---


当你在一个网页进行登录后，你关闭浏览器，你下次打开这个网页依然是登录状态，这是怎么做到的呢？

因为`HTTP`协议是无状态协议，即服务器不知道用户上一次做了什么，这严重阻碍了交互式`Web`应用程序的实现。在典型的网上购物场景中，用户浏览了几个页面，买了一盒饼干和两瓶饮料。

最后结帐时，由于`HTTP`的无状态性，不通过额外的手段，服务器并不知道用户到底买了什么，所以`Cookie`就是用来绕开`HTTP`的无状态性的“额外手段”之一。服务器可以设置或读取`Cookies`中包含信息，借此维护用户跟服务器会话中的状态。

# 1. 什么是Cookie？

根据上面的例子，我们可以懂的：

1. `Cookie`是一些数据, 存储于你电脑上的文本文件中。
2. `Cookie`是浏览器状态管理文件，告诉服务器我们上一步做了什么事情。

# 2. 属性

## 2.1 name

`Cookie`的名称。

## 2.2 value

`Cookie`的值。

## 2.3 domain

`Cookie`绑定的域名，如果不设置，就会自动绑定到执行语句所在的域名。

注意：二级域名的`Cookie`是不能共用的，比如`www.baidu.com`和`zhishu.baidu.com`。

## 2.4 path

可以访问`Cookie`的页面路径，比如`domain`是`baidu.com`，`path`是`/test`，那么只有`/test`路径下的页面可以读取此`Cookie`。

## 2.5 expires/Max-Age

`Cookie`到期时间，值为时间，如果该值被设定，则达到该值所对应的时间后，此`Cookie`失效，如果不设置的话默认值是`Session`，意思是`Cookie`会和`Session`一起失效。当浏览器关闭（不是浏览器标签页，而是整个浏览器）后，此`Cookie`失效。

很多网站在浏览器关闭后需要重新登录就是这个原理。

## 2.6 Size

`Cookie`的大小。

## 2.7 Security

设置是否只能通过`HTTPS`来传递此条`Cookie`。

## 2.8 HttpOnly

不能在除`HTTP`和`HTTPS`请求之外获取`Cookie`。 比如通过`JavaScript`的`document.cookie`访问。

设置该项会使安全性大幅度提升，所以`Facebook`和`Google`正在广泛地使用`HttpOnly`属性。

# 3. Cookie的增删改查

## 3.1 增

创建`Cookie`：

```javascript
document.cookie="username=zhangsan";
/* 添加过期时间expires属性，通过path参数告诉浏览器Cookie属于的路径 */
document.cookie="username=zhangsan; expires=Thu, 18 Dec 2043 12:00:00 GMT; path=/";
```

## 3.2 删

设定`expires`参数为以前的时间，即可以删除`Cookie`。

```js
document.cookie = "username=; expires=Thu, 01 Jan 1970 00:00:00 GMT";
```

删除`Cookie`时不需要指定`Cookie`的值。

## 3.3 改

在`JavaScript`中，修改`Cookie`类似于创建`Cookie`。

```js
document.cookie="username=zhangsan; expires=Thu, 18 Dec 2043 12:00:00 GMT; path=/";
```

旧的`Cookie`将被覆盖。

## 3.4 查

在`JavaScript`读取`Cookie`的方法，以字符串的形式返回所有字符。

```js
let cookie = document.cookie;
```

# 4. 例子

## 4.1 点一点

<div>
  <button onclick="showCookie()" class="btn btn-primary">点击查看Cookie</button>
  <button onclick="addCookie('cookie1')" class="btn btn-primary">添加Cookie-1</button>
  <button onclick="addCookie('cookie2')" class="btn btn-primary">添加Cookie-2</button>
  <button onclick="deletCookie('cookie1')" class="btn btn-primary">删除Cookie-1</button>
  <button onclick="deletCookie('cookie2')" class="btn btn-primary">删除Cookie-2</button>
</div>

## 4.2 百度指数

使用过爬虫爬取百度指数的同学应该很清楚，百度验证用户信息就是靠`Cookie`。

![image-20200720145049365](/images/image-20200720145049365.png)

在浏览器的开发者工具中`Network`下的`index.html`的`Headers`中，就可以看到关于我们账号信息的`Cookie`，即通过该项，我们就可以让爬虫发送同样的`Cookie`，就可以爬取到需要登录才能访问的页面。

# 5. Cookie的缺陷

- Cookie会被附加在每个HTTP请求中，所以无形中增加了流量。
- 由于在HTTP请求中的Cookie是明文传递的，所以安全性成问题，除非用超文本传输安全协定。
- Cookie的大小限制在4KB左右，对于复杂的存储需求来说是不够用的。

# 6. 总结

对于一些企业网站的开发，因为不需要进行登录操作，所以几乎不会用到`Cookie`，但是对于一些论坛等等需要进行登录操作的网站来说，尽量不要将敏感的信息放在`Cookie`中。


<script>
  function addCookie(type) {
    switch (type) {
      case 'cookie1':
        document.cookie = 'username1=zhangsan; expires=Thu, 18 Dec 2043 12:00:00 GMT; path=/';
        alert('添加cookie1');
        break;
      case 'cookie2':
        document.cookie = 'username2=lisi; expires=Thu, 18 Dec 2043 12:00:00 GMT; path=/';
        alert('添加cookie2');
        break;
    }
  }

  function deletCookie(type) {
    switch (type) {
      case 'cookie1':
        document.cookie = 'username1=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
        alert('删除cookie1');
        break;
      case 'cookie2':
        document.cookie = 'username2=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
        alert('删除cookie2');
        break;
    }
  }

  function showCookie() {
    if (document.cookie === '') {
      alert('没有Cookie');
    } else {
      alert(document.cookie);
    }
  }
</script>
