---
title: "TCP三次握手四次挥手"
date: 2020-7-31 15:53:34
categories:
  - "面试"
tags:
  - "TCP"
---


# 1. 一张图表示

下面的图片很清晰的表现出`TCP`从握手到挥手整个过程：

![1717b52d0080bf53-1587536620176](/images/web/1717b52d0080bf53-1587536620176.jpg)

# 2. 场景模拟

其实用一个场景就很容易模拟出TCP的握手挥手过程，就在两座山上，中间隔了一个悬崖，两座山上的人想要通话，那么会产生什么样的事情。

小明：喂！对面有人嘛？（第一次握手）

小红：有人！（第二次握手）

小明：那我要开始说话了！（第三次握手）

...（中间的对话）

小明：我说完了，你可以走了！（第一次挥手）

小红：好的。（第二次挥手）

小红：那我要走了哦！（第三次挥手）

小明：收到！（第四次挥手）

小明说完收到后，等待一会，转身离开。

（因为小明最后一句“收到”可能小红没有听到，所以需要等待小红是否会再次询问“是否能走了”，如果没有这个等待时间小明就走了，那小红那边永远都收不到最后一句“收到”，小红就永远也走不了了。）

小红听到收到后，转身离开。

# 3. 解释

## 3.1 三次握手

1. 第一次握手：起初两端都处于`CLOSED`关闭状态，`Client`将标志位`SYN`置为1，随机产生一个值`seq=x`，并将该数据包发送给`Server`，`Client`进入`SYN-SENT`状态，等待`Server`确认；
2. 第二次握手：`Server`收到数据包后由标志位`SYN=1`得知`Client`请求建立连接，`Server`将标志位`SYN`和`ACK`都置为1，`ack=x+1`，随机产生一个值`seq=y`，并将该数据包发送给`Client`以确认连接请求，`Server`进入`SYN-RCVD`状态，此时操作系统为该`TCP`连接分配`TCP`缓存和变量；
3. 第三次握手：`Client`收到确认后，检查`ack`是否为`x+1`，`ACK`是否为1，如果正确则将标志位`ACK`置为1，`ack=y+1`，并且此时操作系统为该`TCP`连接分配`TCP`缓存和变量，并将该数据包发送给`Server`，`Server`检查`ack`是否为`y+1`，`ACK`是否为1，如果正确则连接建立成功，`Client`和`Server`进入`ESTABLISHED`状态，完成三次握手，随后`Client`和`Server`就可以开始传输数据。

## 3.2 四次挥手

1. 双方彼此都建立了连接，因此双方都要释放自己的连接，A向B发出一个释放连接请求，他要释放链接表明不再向B发送数据了，此时B收到了A发送的释放链接请求之后，给A发送一个确认，A不能再向B发送数据了，它处于`FIN-WAIT-2`的状态，但是此时B还可以向A进行数据的传送。此时B向A 发送一个断开连接的请求，A收到之后给B发送一个确认。此时B关闭连接。A也关闭连接。
2. 为什么要有`TIME-WAIT`这个状态呢，这是因为有可能最后一次确认丢失，如果B此时继续向A发送一个我要断开连接的请求等待A发送确认，但此时A已经关闭连接了，那么B永远也关不掉了，所以我们要有`TIME-WAIT`这个状态。
3. 当然TCP也并不是100%可靠的。

## 3.3 为什么要三次握手四次挥手

当然不能是三次挥手和两次握手，五次挥手和四次握手也是可以的，也就是挥手握手只能多不能少，但是多了的话就会产生一些无意义的挥手握手，增大响应延迟。

# 4. TCP状态转移

|    **状态**     | 描述                                                   |
| :-------------: | :----------------------------------------------------- |
|   **CLOSED**    | 阻塞或关闭状态，表示主机当前没有正在传输或者建立的链接 |
|   **LISTEN**    | 监听状态，表示服务器做好准备，等待建立传输链接         |
|  **SYN RECV**   | 收到第一次的传输请求，还未进行确认                     |
|  **SYN SENT**   | 发送完第一个SYN报文，等待收到确认                      |
| **ESTABLISHED** | 链接正常建立之后进入数据传输阶段                       |
|  **FIN WAIT1**  | 主动发送第一个FIN报文之后进入该状态                    |
|  **FIN WAIT2**  | 已经收到第一个FIN的确认信号，等待对方发送关闭请求      |
| **TIMED WAIT**  | 完成双向链接关闭，等待分组消失                         |
|   **CLOSING**   | 双方同时关闭请求，等待对方确认时                       |
| **CLOSE WAIT**  | 收到对方的关闭请求并进行确认进入该状态                 |
|  **LAST ACK**   | 等待最后一次确认关闭的报文                             |



