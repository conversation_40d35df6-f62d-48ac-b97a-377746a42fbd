---
title: "非常实用的Flex布局"
date: 2020-10-1 20:42:57
categories:
  - "面试"
tags:
  - "CSS3"
  - "Flex"
---


# Flex布局

传统的布局，基于盒装模型，早期的前端开发就是一个叠盒子的过程，即各种`<div>`的互相嵌套。

传统的布局一般依赖`position`或者`float`属性控制盒子所在的位置，但是对于特殊布局就显得非常的不方便，比如：垂直居中、均匀分布等。

在2009年，W3C提出了一种新的布局方案，即现在非常常用的Flex布局，可以非常轻松的完成和实现各种页面布局。到目前为止，它已经获得了主流浏览器的支持。但遗憾的是，如果要兼容早期的IE版本，还是得使用传统的`position`，`float`，不过由于兼容以前的IE版本会增加非常多的成本，所以现在大部分开发者已经放弃兼容早期的IE。

Flex是Flexible Box的缩写，意为"弹性布局"，现在比较流行的响应式界面，就是使用Flex和媒体查询进行实现。




<style>
    .box1 {
        padding: 10px;
        background-color: #f6ecec;
    }
    .box1>label {
        margin-left: 10px;
    }
    #row,#wrap,#content,#items,#alignContent {
        max-width: 600px;
        display: flex;
        border:1px #0f0f0f solid;
    }
    #alignContent {
        height: 500px;
        flex-wrap:wrap;
    }
    #flexBasis {
        flex-basis: 100px;
    }
    #wrap>div,
    #content>div,
    #items>div,
    #row>div,
    #alignContent>div{
        background-color:#f3b3b3;
    }


    #wrap>div[data-index]::before,
    #content>div[data-index]::before,
    #items>div[data-index]::before,
    #row>div[data-index]::before,
    #alignContent>div[data-index]::before {
        content: attr(data-index);
        background-color: #e5a9a9;
        color: #fff;
        position: absolute;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 14px;
        text-align: center;
        line-height: 20px;
        z-index: 1;
    }
    @media screen  and (max-width: 600px){
         #content>div {
             width: 80px;
         }
         #alignContent>div:nth-child(5),
              #alignContent>div:nth-child(6),
              #alignContent>div:nth-child(7){
              display: none;
         }
         .flex-grow>div {
            width: 80px;
         }
         .flex-shrink-not {
            display: none;
         }
    }
</style>

# 声明

任何一个容易都可以指定为Flex布局。

```css
.box {
    display: flex;
}
```

注意：设为 Flex 布局以后，子元素的`float`、`clear`和`vertical-align`属性将失效。

# 容器的属性

## flex-direction

`flex-direction`属性决定主轴的方向（即项目的排列方向）。

```css
.box {
  flex-direction: row | row-reverse | column | column-reverse;
}
```

点一点看效果

<div class="box1">
    <label>
        <input onclick="rowClick('row',value)" type="radio" name="flex-direction" value="row" checked> row
    </label>
    <label>
    <input onclick="rowClick('row',value)" type="radio" name="flex-direction" value="row-reverse"> row-reverse
    </label>
    <label>
    <input onclick="rowClick('row',value)" type="radio" name="flex-direction" value="column"> column
    </label>
    <label>
    <input onclick="rowClick('row',value)" type="radio" name="flex-direction" value="column-reverse"> column-reverse
    </label>
    <div id="row">
    <div data-index="1">
        <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2">
        <img width="110px" src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    </div>
</div>


### 属性

- `row` flex容器的主轴被定义为与文本方向相同。 主轴起点和主轴终点与内容方向相同。
- `row-reverse` 表现和row相同，但是置换了主轴起点和主轴终点
- `column` flex容器的主轴和块轴相同。主轴起点与主轴终点和书写模式的前后点相同
- `column-reverse` 表现和column相同，但是置换了主轴起点和主轴终点

## flex-wrap

默认情况下，项目都排在一条线（又称“轴线"）上。`flex-wrap`属性定义，如果一条轴线排不下，如何换行。

```css
.box{
  flex-wrap: nowrap | wrap | wrap-reverse;
}
```

点一点看效果

<div class="box1">
<label>
    <input onclick="rowClick('wrap',value)" type="radio" name="flex-wrap" value="nowrap" checked> nowrap
</label>
<label>
    <input onclick="rowClick('wrap',value)" type="radio" name="flex-wrap" value="wrap"> wrap
</label>
<label>
    <input onclick="rowClick('wrap',value)" type="radio" name="flex-wrap" value="wrap-reverse"> wrap-reverse
</label>
    <div id="wrap">
    <div data-index="1">
        <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2">
        <img width="110px" src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    <div data-index="4">
        <img width="200px" src="/images/Flex/4.jpg"/>
    </div>
    </div>
</div>

### 属性

- `nowrap` flex 的元素被摆放到到一行，这可能导致溢出 flex 容器。 cross-start  会根据 flex-direction 的值 相当于 start 或 before。
- `wrap` flex 元素 被打断到多个行中。cross-start 会根据 flex-direction 的值选择等于start 或before。cross-end 为确定的 cross-start 的另一端。
- `wrap-reverse ` 和 wrap 的行为一样，但是 cross-start 和 cross-end 互换。

## flex-flow

`flex-flow`属性是`flex-direction`属性和`flex-wrap`属性的简写形式，默认值为`row nowrap`。

```css
.box {
  flex-flow: <flex-direction> || <flex-wrap>;
}
```

## justify-content

`justify-content`属性定义了项目在主轴上的对齐方式。

```css
.box {
  justify-content: flex-start | flex-end | center | space-between | space-around;
}
```

点一点看效果

<div class="box1">
<label>
    <input onclick="rowClick('content',value)" type="radio" name="justify-content" value="flex-start" checked> flex-start
</label>
<label>
    <input onclick="rowClick('content',value)" type="radio" name="justify-content" value="flex-end"> flex-end
</label>
<label>
    <input onclick="rowClick('content',value)" type="radio" name="justify-content" value="center"> center
</label>
<label>
    <input onclick="rowClick('content',value)" type="radio" name="justify-content" value="space-between"> space-between
</label>
<label>
    <input onclick="rowClick('content',value)" type="radio" name="justify-content" value="space-around"> space-around
</label>
    <div style="height: 200px;" id="content">
    <div data-index="1"> 
        <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2">
        <img width="110px" src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    </div>
</div>


### 属性

- `flex-start`（默认值）：左对齐
- `flex-end`：右对齐
- `center`： 居中
- `space-between`：两端对齐，项目之间的间隔都相等。
- `space-around`：每个项目两侧的间隔相等。所以，项目之间的间隔比项目与边框的间隔大一倍。

## align-items

`align-items`属性定义项目在交叉轴上如何对齐。

```css
.box {
  align-items: flex-start | flex-end | center | baseline | stretch;
}
```

点一点看效果

<div class="box1">
<label>
    <input onclick="rowClick('items',value)" type="radio" name="align-items" value="flex-start" checked> flex-start
</label>
<label>
    <input onclick="rowClick('items',value)" type="radio" name="align-items" value="flex-end"> flex-end
</label>
<label>
    <input onclick="rowClick('items',value)" type="radio" name="align-items" value="center"> center
</label>
<label>
    <input onclick="rowClick('items',value)" type="radio" name="align-items" value="baseline"> baseline
</label>
<label>
    <input onclick="rowClick('items',value)" type="radio" name="align-items" value="stretch"> stretch
</label>
    <div style="height: 200px;" id="items">
    <div data-index="1">
        <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2">
        <img width="110px" src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    </div>
</div>


### 属性

- `flex-start`：交叉轴的起点对齐。
- `flex-end`：交叉轴的终点对齐。
- `center`：交叉轴的中点对齐。
- `baseline`: 项目的第一行文字的基线对齐。
- `stretch`（默认值）：如果项目未设置高度或设为auto，将占满整个容器的高度。

## align-content

`align-content`属性定义了多根轴线的对齐方式。如果项目只有一根轴线，该属性不起作用。

```css
.box {
  align-content: flex-start | flex-end | center | space-between | space-around | stretch;
}
```

点一点看效果

<div class="box1">
<label>
    <input onclick="rowClick('alignContent',value)" type="radio" name="choose" value="stretch" checked> stretch
</label>
<label>
    <input onclick="rowClick('alignContent',value)" type="radio" name="choose" value="flex-start"> flex-start
</label>
<label>
    <input onclick="rowClick('alignContent',value)" type="radio" name="choose" value="flex-end"> flex-end
</label>
<label>
    <input onclick="rowClick('alignContent',value)" type="radio" name="choose" value="center"> center
</label>
<label>
    <input onclick="rowClick('alignContent',value)" type="radio" name="choose" value="space-between"> space-between
</label>
<label>
    <input onclick="rowClick('alignContent',value)" type="radio" name="choose" value="space-around"> space-around
</label>
<label>
    <input onclick="rowClick('alignContent',value)" type="radio" name="choose" value="space-evenly"> space-evenly
</label>
    <div id="alignContent">
    <div data-index="1">
        <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2">
        <img width="110px" src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    <div data-index="4">
        <img width="200px" src="/images/Flex/4.jpg"/>
    </div>
    <div data-index="5">
        <img width="200px" src="/images/Flex/5.jpg"/>
    </div>
    <div data-index="6">
        <img width="200px" src="/images/Flex/6.jpg"/>
    </div>
    <div data-index="7">
        <img width="200px" src="/images/Flex/7.jpg"/>
    </div>
    </div>
</div>


### 属性

- `flex-start`：与交叉轴的起点对齐。
- `flex-end`：与交叉轴的终点对齐。
- `center`：与交叉轴的中点对齐。
- `space-between`：与交叉轴两端对齐，轴线之间的间隔平均分布。
- `space-around`：每根轴线两侧的间隔都相等。所以，轴线之间的间隔比轴线与边框的间隔大一倍。
- `stretch`（默认值）：轴线占满整个交叉轴。

# 子项上的属性

## order

`order`属性定义项目的排列顺序。数值越小，排列越靠前，默认为0。

```css
.item {
  order: <integer>;
}
```

点一点看效果

<div class="box1">
    <label>
        <input onclick="childClick('order',value)" type="radio" name="order" value="0" checked> order: 0
    </label>
    <label>
    <input onclick="childClick('order',value)" type="radio" name="order" value="1"> order: 1
    </label>
    <label>
    <input onclick="childClick('order',value)" type="radio" name="order" value="2"> order: 2
    </label>
    <div id="row">
    <div data-index="1" id="flex-order">
    <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2" style="order: 0">
        <img width="110px" src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3" style="order: 1">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    </div>
</div>

## flex-grow

`flex-grow`属性定义项目的放大比例，默认为`0`，即如果存在剩余空间，也不放大。

```css
.item {
  flex-grow: <number>; /* default 0 */
}
```

点一点看效果

<div class="box1">
    <label>
        <input onclick="childClick('flexGrow',value)" type="radio" name="flex-grow" value="0" checked> flex-grow: 0
    </label>
    <label>
    <input onclick="childClick('flexGrow',value)" type="radio" name="flex-grow" value="0.5"> flex-grow: 0.5
    </label>
    <label>
    <input onclick="childClick('flexGrow',value)" type="radio" name="flex-grow" value="1"> flex-grow: 1
    </label>
    <div class="flex-grow" style="height: 200px;" id="row">
    <div data-index="1">
    <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2" id="flexGrow">
        <img width="110px" src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    </div>

</div>

## flex-shrink

`flex-shrink`属性定义了项目的缩小比例，默认为1，即如果空间不足，该项目将缩小。

```css
.item {
  flex-shrink: <number>; /* default 1 */
}
```

点一点看效果

<div class="box1">
    <label>
        <input onclick="childClick('flexShrink',value)" type="radio" name="flex-shrink" value="0" checked> flexShrink: 0
    </label>
    <label>
    <input onclick="childClick('flexShrink',value)" type="radio" name="flex-shrink" value="3"> flexShrink: 1
    </label>
    <label>
    <input onclick="childClick('flexShrink',value)" type="radio" name="flex-shrink" value="4"> flexShrink: 2
    </label>
    <div id="row">
    <div data-index="1">
        <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2" id="flexShrink">
        <img src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    <div class="flex-shrink-not" data-index="4">
            <img width="300px" src="/images/Flex/4.jpg"/>
        </div>
    </div>
</div>

## flex-basis

`flex-basis`属性定义了在分配多余空间之前，项目占据的主轴空间（main size）。浏览器根据这个属性，计算主轴是否有多余空间。它的默认值为`auto`，即项目的本来大小。

```css
.item {
  flex-basis: <length> | auto; /* default auto */
}
```

点一点看效果

<div class="box1">
    <label>
        <input onclick="childClick('flexBasis',value)" type="radio" name="flex-basis" value="100px" checked> flex-basis: 100px
    </label>
    <label>
    <input onclick="childClick('flexBasis',value)" type="radio" name="flex-basis" value="150px"> flex-basis: 150px
    </label>
    <label>
    <input onclick="childClick('flexBasis',value)" type="radio" name="flex-basis" value="200px"> flex-basis: 200px
    </label>
    <div id="row">
    <div data-index="1">
    <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div data-index="2" id="flexBasis">
        <img src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    </div>
</div>

## flex

`flex`属性是`flex-grow`, `flex-shrink` 和 `flex-basis`的简写，默认值为`0 1 auto`。后两个属性可选。

```css
.item {
  flex: none | [ <'flex-grow'> <'flex-shrink'>? || <'flex-basis'> ]
}
```

该属性有两个快捷值：`auto (1 1 auto)` 和 `none (0 0 auto)`。

建议优先使用这个属性，而不是单独写三个分离的属性，因为浏览器会推算相关值。

因为是上面3项的简写，所以这里就不再提供案例了。

## align-self

`align-self`属性允许单个项目有与其他项目不一样的对齐方式，可覆盖`align-items`属性。默认值为`auto`，表示继承父元素的`align-items`属性，如果没有父元素，则等同于`stretch`。

```css
.item {
  align-self: auto | flex-start | flex-end | center | baseline | stretch;
}
```

点一点看效果

<div class="box1">
    <label>
        <input onclick="childClick('alignSelf',value)" type="radio" name="align-self" value="auto" checked> auto
    </label>
    <label>
            <input onclick="childClick('alignSelf',value)" type="radio" name="align-self" value="flex-start"> flex-start
        </label>
        <label>
                <input onclick="childClick('alignSelf',value)" type="radio" name="align-self" value="flex-end"> flex-end
            </label>
            <label>
                    <input onclick="childClick('alignSelf',value)" type="radio" name="align-self" value="center" > center
                </label>
    <label>
    <input onclick="childClick('alignSelf',value)" type="radio" name="align-self" value="baseline"> baseline
    </label>
    <label>
    <input onclick="childClick('alignSelf',value)" type="radio" name="align-self" value="stretch"> stretch
    </label>
    <div style="height:200px;align-items: baseline"  id="row">
    <div data-index="1">
    <img width="100px" src="/images/Flex/1.jpg"/>
    </div>
    <div  data-index="2" id="alignSelf">
        <img height="170px" src="/images/Flex/2.jpg"/>
    </div>
    <div data-index="3">
        <img width="200px" src="/images/Flex/3.jpg"/>
    </div>
    </div>
</div>

# 总结

我之前在做网页中会大量使用`Flex`，因为比起`position`或者`float`来讲实在是太过于方便了，其次用的比较多的就是`position`，`float`基本不怎么用到。

不过这些都要根据实际情况，因为老版本的`IE`仍然有一部分用户在使用，如果要兼容老版本的`IE`的话...那实在是很蛋疼。


<script>
    function rowClick(type,value) {
        if(type==='row')
        {
           $('#row').css('flexDirection',value);
        }
        if(type==='wrap')
        {
           $('#wrap').css('flexWrap',value);
        }
        if(type==='content')
        {
           $('#content').css('justifyContent',value);
        }
        if(type==='items')
        {
           $('#items').css('alignItems',value);
        }
        if(type==='alignContent')
        {
           $('#alignContent').css('alignContent',value);
        }
    }
    function childClick(type,value) {
      if(type==='order'){
        $('#flex-order').css('order',value);
      }
      if(type==='flexGrow'){
          $('#flexGrow').css('flexGrow',value);
      }
      if(type==='flexShrink'){
          $('#flexShrink').css('flexShrink',value);
      }
      if(type==='flexBasis'){
          $('#flexBasis').css('flexBasis',value);
      }
      if(type==='alignSelf'){
         $('#alignSelf').css('alignSelf',value);
      }
    }
</script>


