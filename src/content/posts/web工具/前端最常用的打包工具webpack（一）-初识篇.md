---
title: "前端最常用的打包工具webpack（一）-初识篇"
date: 2021-2-5 23:11:30
categories:
  - "webpack"
tags:
  - "打包工具"
---


这是重新学习webpack的第一篇文章，之前我也写过一篇关于webpack的文章：[目前最流行的前端打包工具-webpack](/2020/07/09/面试/目前最流行的前端打包工具-webpack/)。

问题是过去快半年了，~~我已经将webpack忘得差不多，~~回头一看那篇文章完全是一头雾水，也没有办法，那个时候我才刚刚开始写前端方面的博客，对于知识梳理、文字表达都有一些欠缺，啪啪啪一下扔出来几千字，全是重点，没有任何过渡性的语言，看个一会就没什么兴趣。

综上所述，本次决定给webpack写一个比较详细的系列文章，目的是如果我半年后又忘记了webpack的具体内容，再来翻阅文章，不会像这次一样一头雾水。

**什么情况适合学习webpack？**

答：已经对前端主流框架（Vue、React、Angular等）有一定认识，并且想要进一步学习框架相关的知识，比如Vue框架，是如何将`.vue`编译成为js文件并且将虚拟DOM渲染到界面上，`.scss`文件，是如何自动调用sass编译器将它编译成`.css`文件，让浏览器能够正确识别，其实这些都是资源构建工具所干的事。

# 1. 简介

> webpack 是一种前端资源构建工具，一个静态模块打包器(module bundler)。

首先我们需要明白一点的就是，浏览器只认识HTML、CSS、JavaScript这一类的文件，至于什么Sass、Less、jsx、ts等一系列的文件浏览器都不认识，如果你需要让浏览器能够正确识别这些文件，你就需要使用工具将Sass、Less等文件转为CSS，而TypeScript语法转换为JavaScript语法。

如果你不使用构建工具，那么你就必须手动将这些文件进行转换，可能一个文件中有好多个`.scss`、`.ts`文件，你都需要一个一个的去转，这样不光效率低不说，还很容易出错。

**资源构建工具**的出现就是为了解决这一痛点，只要你配置好构建工具，它就会自动的将浏览器无法识别的文件转换为浏览器能够识别的文件，浏览器无法识别的语言转换为浏览器能够识别的语言。

**静态模块打包器**即webpack会找到你指定的入口文件，通过入口文件将你引入的模块进行打包，如果没有在项目中引入的模块则不会打包进最终的项目，这大大降低了代码的体积。

---

你可能想问：为什么我用Vue、React这一类的框架也没有配置过什么资源构建工具呀，不是照样可以写Sass、TypeScript吗？

答：其实现在比较流行的前端框架，它的资源构建工具都是由发布方已经帮你配置好了，你直接拿来用就行。

你可能又想问：既然人家已经配置好了，为什么我还要学？

答：大部分情况我们都不需要再手动进行配置构建工具，但是如果你想要对项目进行优化时，你就必须了解甚至懂得构建工具相关的知识，你才能够完成项目优化的任务，而且有一部分面试官也喜欢考察这方面的问题。**更重要的是，打包工具是前端架构绕不开的一环，如果你要写一个框架，肯定要学习构建工具，当然你也可以自己写一个构建工具（比如尤雨溪的vite）。所以学习webpack也是前端进阶的一环。**

相信看到这里，你已经对webpack有一定的了解，知道webpack到底是做什么的。

# 2. 五个核心概念

其实webpack仅有下面5个比较核心的概念，掌握了这5个概念，你就能够很轻松的配置webpack。

## 2.1 Entry

入口(`Entry`)指示 `webpack` 以哪个文件为入口起点开始打包，分析构建内部依赖图。

## 2.2 Output

输出(`Output`)指示 `webpack` 打包后的资源 `bundles` 输出到哪里去，以及如何命名。

## 2.3 Loader

`Loader` 让 `webpack` 能 够 去 处 理 那 些 非 `JavaScript` 文 件 (`webpack`自身只理解`JavaScript`)。

## 2.4 Plugins

插件(`Plugins`)可以用于执行范围更广的任务。插件的范围包括，从打包优化和压缩， 一直到重新定义环境中的变量等。

## 2.5 Mode

模式(`Mode`)指示 `webpack` 使用相应模式的配置。

---

在这5项内容中，我们需要着重学习的就是`Loader、Plugins`。可以说对一个项目的功能拓展来说，这两项内容起着决定性的作用，在一定的情况下，我们甚至还需要手写Loader和Plugins。

不过**如果不是自己研发框架或者学习webpack的时候，很少会去写这些**，因为市面上已经有了非常多的成熟的开源Loader和Plugins，我们需要的只是拿过来用就可以了，一些主流的框架甚至你连webpack都不用配置，官方已经帮你写好了配置文件。

根据我的工作经验来看，在实际的项目中，可能对于webpack多多少少会有一些手动配置，比如配置开发环境下启动的端口号，比如通过webpack解决跨域，比如要让一个`.scss`文件在全项目中不用引入就可以使用，再比如`.svg`文件的一些简便引入方式，都需要手动去配置webpack。

但是这些配置都不难，只要网上随便看两篇文章，就算不知道webpack的原理，都能比较顺利的完成这方面的配置。

# 3. 体验

那么怎么创建一个webpack项目呢？

## 3.1 创建项目

先新建一个文件夹，进入到文件夹中，创建一个Node项目。

```shell
npm init -y
```

可以看到出现了`package.json`文件，这就说明一个Node项目创建完成。

## 3.2 引入webpack

```shell
npm install webpack webpack-cli --save-dev
```

有一定经验的同学一定知道上面的代码是将webpack设置为开发依赖，因为生产环境是不需要webpack的，只需要webpack打包后的文件。

新建一个`main.js`文件。当然这里文件名随意取就好了，该文件就是整个项目的入口文件，即`Entry`。

随意在`main.js`文件中写上下面的代码。

```js
function add(x, y) {
  return x + y;
}

console.log(add(1, 2));
```

## 3.3 打包

在终端中运行：

```shell
webpack ./main.js -o ./build/ --mode=development
```

可以看到项目下多了一个build文件夹，点击去发现还有一个`main.js`文件，该文件就是你打包后的`main.js`文件。

到这里，你就可以使用webpack进行打包了~

**语句解释**

上面运行的语句webpack后面的`./main.js`代表入口文件。

`-o`代表`Output`，而`./build/`代表将打包后的资源输出到build文件夹中，`--mode=development`则代表打包模式为开发模式。

## 3.1 Mode

打包模式：

1. **development**：开发模式，代码不会经过压缩。
2. **production**：生产模式，代码会经过压缩。
3. **none**：不使用任何默认优化选项

那么development、production两种模式有什么区别呢，我们可以先看一下上面的代码经过开发模式打包的最终输出结果：

```js
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/*!*****************!*\
  !*** ./main.js ***!
  \*****************/
eval("function add(x, y) {\n  return x + y;\n}\n\nconsole.log(add(1, 2));\n\n\n//# sourceURL=webpack://webpack-cli/./main.js?");
/******/ })()
;
```

那么我们再用生产模式打包一次：

```shell
webpack ./main.js -o ./build/ --mode=production
```

打包结果：

```js
console.log(3);
```

额...其实得到这个结果我还是非常意外的，因为我知道webpack5升级了摇树（Tree Shaking，即把项目中无用的代码去除掉）。但是没有想到摇树已经变得如此智能。

好吧，更好的**Tree Shaking**也是webpack5主打的功能之一。

# 4. 最后

到目前为止，相信你对webpack已经有一定的了解，知道webpack是一种前端资源构建工具，一个静态模块打包器，已经可以使用webpack进行简单的js文件打包，注意，如果要使用webpack打包css、图片、HTML文件，还需要配置之后需要重点讲解的**Loader**和**Plugins**。









