---
title: "公众号自定义样式，令人惊叹的文章编辑器，SVG篇"
date: 2020-06-30
categories:
  - "微信公众号"
tags:
  - "样式"
---


这个系列居然还有第二期，我是没有想到的，虽然我的公众号已经放弃维护了，现在在搞我的博客。

[公众号自定义样式，令人惊叹的文章编辑器](/2020/09/10/javascript/使用正则表达式/)

# 起因

事情的起因是因为我无意之中看到了人家微信公众号的文章中有这样的效果：

![click](/images/click.gif)

一个公众号中的文章，居然还能够实现这种效果，太不可思议了，于是马上开始研究起来。

# 研究

使用Chrome浏览器打开这篇文章的链接，然后通过开发者工具进行检查，发现了下面的代码：

```html
<section style="height: 0px; box-sizing: border-box;">
    <svg
    opacity="1"
    style="
        box-sizing: border-box;
        transform: rotateZ(0deg);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: 50% 50%;
        background-color: rgb(254, 254, 254);
        background-image: url('https://mmbiz.qpic.cn/sz_mmbiz_jpg/9NT6Pkus6TMhBVMGv3j06B9VLRMjicpOibfjdbNsRjcXH13pkfZNStyX67yH3QcgppNiaPOQYrMvGGfnP7sVmcaqw/640?wx_fmt=jpeg');
        width: 100%;
    "
    viewBox="0 0 100 120.2"
    width="1"
    xmlns="http://www.w3.org/2000/svg"
    >
    <animate
        attributeName="width"
        style="box-sizing: border-box;"
        fill="freeze"
        to="0"
        from="1"
        duration="0.01"
        begin="click + 0.5s"
    ></animate>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="100%"
        height="100%"
        style="box-sizing: border-box;"
    >
        <svg width="100%" height="100%" style="box-sizing: border-box;">
        <rect
            width="100%"
            height="100%"
            style="box-sizing: border-box; opacity: 1; fill: transparent;"
            x="0%"
        ></rect>
        </svg
        >
        <svg
        style="width: 100%; height: 100%; box-sizing: border-box;"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        >
    </svg> 
    </svg>
    <animate
        style="box-sizing: border-box;"
        fill="freeze"
        dur="0.5"
        attributeName="opacity"
        begin="click"
        from="1"
        to="0"
    ></animate>
    </svg>
</section>
```

是的，在微信提供的编辑器中还可以使用`svg`标签，果然不是一个普通的文字编辑器啊！

将上面的那段代码新建一个`html`文件。

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
<section style="height: 0px; box-sizing: border-box;">
    <svg
    opacity="1"
    style="
        box-sizing: border-box;
        transform: rotateZ(0deg);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: 50% 50%;
        background-color: rgb(254, 254, 254);
        background-image: url('https://mmbiz.qpic.cn/sz_mmbiz_jpg/9NT6Pkus6TMhBVMGv3j06B9VLRMjicpOibfjdbNsRjcXH13pkfZNStyX67yH3QcgppNiaPOQYrMvGGfnP7sVmcaqw/640?wx_fmt=jpeg');
        width: 100%;
    "
    viewBox="0 0 100 120.2"
    width="1"
    xmlns="http://www.w3.org/2000/svg"
    >
    <animate
        attributeName="width"
        style="box-sizing: border-box;"
        fill="freeze"
        to="0"
        from="1"
        duration="0.01"
        begin="click + 0.5s"
    ></animate>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="100%"
        height="100%"
        style="box-sizing: border-box;"
    >
        <svg width="100%" height="100%" style="box-sizing: border-box;">
        <rect
            width="100%"
            height="100%"
            style="box-sizing: border-box; opacity: 1; fill: transparent;"
            x="0%"
        ></rect>
        </svg
        >
        <svg
        style="width: 100%; height: 100%; box-sizing: border-box;"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        >
    </svg> 
    </svg>
    <animate
        style="box-sizing: border-box;"
        fill="freeze"
        dur="0.5"
                attributeName="opacity"
        begin="click"
        from="1"
        to="0"
    ></animate>
    </svg>
</section>
  </body>
</html>
```

会出现下面的效果：

![click1](/images/click1.gif)

可以看到，点击浮现的效果即是使用了一个SVG遮住了下面的图片。

# SVG

如果想要学习SVG，可以看这篇文章[SVG 图像入门教程](https://www.ruanyifeng.com/blog/2018/08/svg.html) 因为这种前端知识，不是本篇的重点，那么接下来我们看一下公众号的编辑器可以实现哪些酷炫的效果吧。

![](/images/效果.gif)

# 文章链接

[公众号文章能够实现的酷炫效果](https://mp.weixin.qq.com/s/fYTeLpJ3_RuPxumk6l6uxg)

亲测如果使用Chrome打开链接是无法触发SVG的点击事件，只有通过微信浏览器打开才能触发点击事件，不知道是不是我的问题。
