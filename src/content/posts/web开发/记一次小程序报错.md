---
title: "记一次小程序报错undefined is not an object (evaluating ‘document.getElementsByTagName‘)"
date: 2021-1-24 19:59:43
categories:
  - "web开发"
tags:
  - "uni-app"
  - "小程序"
---


我的小程序是使用uni-app进行开发，开发完毕后使用微信开发者工具进行调试发现没有问题，然后真机调试也没有问题，于是就打包送审。

但是却被通知审核失败。

![image-20210124200246173](/images/web/记一次小程序报错/image-20210124200246173.png)

小程序打开居然白屏了，我扫描了一下线上体验版的二维码，发现打开真的是白屏，并且打开控制台后报出下面的错误：

![image-20210124200403379](/images/web/记一次小程序报错/image-20210124200403379.png)

网上搜索了一圈，很多人说这是因为图标包`iconfont.js`文件引起的，但是我删除了该文件后，再次重新打包发现依然是白屏。

在我反复的捣鼓下，我发现小程序是带有缓存的，需要点击右上角的三个点，然后选择重新进入小程序：

![image-20210124200757025](/images/web/记一次小程序报错/image-20210124200757025.png)

这个时候小程序就不再白屏。

因为没有清除缓存，我捣鼓了1个小时左右，我翻阅了很多资料也仅仅是只有前面的一步，没有讲解要清除一下缓存。



