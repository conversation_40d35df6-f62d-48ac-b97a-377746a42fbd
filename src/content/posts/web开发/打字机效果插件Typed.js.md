---
title: "打字机效果插件Typed.js"
date: 2020-06-15
categories:
  - "web开发"
tags:
  - "Web 开发"
  - "打字机"
  - "动画"
---


打字机效果是非常酷的文字显示效果，视觉表现极佳，而通过`Typed.js`可以很简单的在web开发中实现打字机效果。

# 1. 官网

[JavaScript Animated Typing with Typed.jsmattboldt](https://mattboldt.com/demos/typed-js/)

# 2. 安装

```powershell
# With NPM
npm install typed.js

# With Yarn
yarn add typed.js

# With Bower
bower install typed.js
```

## 2.1 CDN

```html
<script src="https://cdn.jsdelivr.net/npm/typed.js@2.0.11"></script>
```

# 3. 使用

## 3.1 HTML部分

```html
<span id="typed" style="white-space: pre-wrap;line-height: 30px;"></span>
```

**经过测试文字放在`span`标签里面，输入的光标才会正常显示。**

## 3.2 JavaScript部分

```js
const options = {
  strings: [
    'First test.',
    'Second test, \nit will pause for three seconds. ^3000',
    "Second test, \nthe last sentence will go back to 'Second test, ', \noh no, this is the third test. ^1000",
    "It's going to start repeating.",
  ],
  typeSpeed: 50, // 打印速度
  startDelay: 300, // 开始之前的延迟300毫秒
  loop: true, // 是否循环
};

const typed = new Typed('#typed', options);
```

## 3.3 更多参数

该插件可以配置很多参数，具体的参数可以参考官方文档，或者参考下面的文章：

https://blog.csdn.net/weixin_41000111/article/details/78725784

# 4. vue中的使用

该插件对`vue`做了一次封装：

[vue-typed-jsgithub](hhttps://github.com/Orlandster/vue-typed-js)

## 4.1 安装

```powershell
npm install --save vue-typed-js
```

## 4.2 使用

非常简单，按照文档一步一步集成就可以了。

**注意：`vue-typed-js`没有声明文件，所以在`TypeScript`中使用会报错，而`Typed.js`是有声明文件的，可以直接在TypeScript中使用。**

# **5. 总结**

总而言之使用`Typed.js`实现打字机效果非常的简单，简单到不需要用太多语言去进行描述。
