---
title: "GSAP动画插件-TextPlugin"
date: 2021-2-3 20:49:50
categories:
  - "web开发"
tags:
  - "动画"
---


本篇文章是GSAP系列的一篇文章，关于web动画，目前我已经发布了以下文章：

web动画篇：

- [用CSS做出漂亮的字体动画](/2021/01/31/web开发/用css做出漂亮的字体动画/)
- [HTML动画的那些事-车轮旋转篇](/2021/01/31/web开发/html动画的那些事-车轮旋转篇/)

GSAP篇：

- [GSAP（GreenSock）：最健全的web动画库之一](/2020/06/03/web开发/gsapgreensock最健全的web动画库之一/)
- [GSAP动画插件-ScrollTrigger（一）](/2021/01/23/web开发/gsap动画插件-scrolltrigger一/)
- [GSAP动画插件-Flip Plugin（一）](/2021/02/03/web开发/gsap动画插件-flip-plugin一/)

其中web动画也可能会用到一定的GSAP动画库的知识。

GSAP是一个非常优秀的动画库，但是不知道为什么中文资料非常少，几乎很少有人会提到GSAP，并且它拥有非常宽裕的使用协议，几乎大部分商业用途都可以免费使用，它的英文文档也非常健全，可以说只要是你见过的web动画，都可以使用GSAP进行实现。

---

今天的主角是**TextPlugin插件**，听名字都知道它是一个专门处理文字相关的插件，平时在我们的网页制作中，有时候需要使用打字机效果，就可以通过该插件进行实现。

之前我有一篇文章介绍了一个易用的打字机插件：[打字机效果插件Typed.js](/2020/06/15/web开发/打字机效果插件typedjs/)，使用方法非常简单。

今天介绍的这个GSAP插件，使用方法也同样简单。

# 1. 使用

基础用法如下：

```html
<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="ie=edge" http-equiv="X-UA-Compatible" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.0/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.0/TextPlugin.min.js"></script>
    <title>Title</title>
    <style>
     body {
       display: flex;
       justify-content: center;
     }
    </style>
  </head>
  <body>
  <h1 id="myText"></h1>
    <script>
      // 进行操作
      gsap.to("#myText", {
        duration: 2,
        text:"TextPlugin的使用",
        delay: 1,
      });
    </script>
  </body>
</html>
```

最核心的代码即为`text: xxx`。

## 1.1 value

需要替换的值。

## 1.2 delimiter

设置分隔符，如果不设置该属性，则会将每个字符作为单独的一个字符，一个一个的进行显示。

如果设置了该属性，会将给定的字符按照分隔符进行分割，再进行显示。

没有设置该属性：

![gsap-nodelimiter](/images/web/GSAP动画插件-TextPlugin/gsap-nodelimiter.gif)

设置该属性为：`delimiter: " "`，即以空格进行分割。

```js
gsap.to("#myText", {
  duration: 2,
  text: { value: "thank you for waiting", delimiter: " " },
  delay: 1,
});
```

![gsap-delimiter](/images/web/GSAP动画插件-TextPlugin/gsap-delimiter.gif)

## 1.3 newClass

如果设置了该属性，**替换后的文字会被包含在`<span>`中**，同时添加新的类名：

```js
gsap.to("#myText", {
  duration: 2,
  text: {
    value: "Text",
    padSpace: true,
    newClass:"blue",
  },
  delay: 1,
});
```

## 1.4 oldClass

跟上面一样，不过这是**被替换的文字会包含在`<span>`中**，同时添加指定的类名。

## 1.5 padSpace

如果新文本比旧文本短，则可以设置`padSpace: true`，它会用空格替代差值属性，以保证格式正确。

## 1.6 speed

控制字体的替换速度，**1为最慢，20为最快。**

如果没有该属性就必须设置gsap下的`delay`即持续时间属性。

## 1.7 type

```html
<h1 id="myText">TextPlugin收看</h1>
<script>
  gsap.to("#myText", {
    duration: 2,
    text: {
      value: "TextPlugin的使用",
      type: "diff",
    },
    delay: 1,
  });
</script>
```

设置`type: "diff"`后，只会替换不同的字符。

# 2. 最后

插件官方文档：[TextPlugin插件文档](https://greensock.com/docs/v3/Plugins/TextPlugin)。

HTML中动画的制作是一件非常耗时的过程，同时也非常考验一个开发者对于动画的积累程度，因为大多数关于动画的CSS属性在我们平时编写网页的过程中都不会用到。

所以平时积累HTML动画相关的知识就显得尤为重要，万一哪一天你要开发的某个网页就会用到这些知识呢。
