---
title: "一个完全不懂的编程的新人如何入门前端"
date: 2021-2-1 16:35:58
categories:
  - "web开发"
tags:
  - "前端开发"
---


# 1. 前言

我之前是一个完全没有接触过前端技术的人，身边也没有特别熟悉的人在做这一行，所以我在学习前端的路上走了非常多的弯路，**本篇文章就进行总结一下一个完全不懂编程的新人如何入门前端**，希望能够给那些想要入门前端行业的人一定的指引，不像我一样再走很多弯路。

- 前端：一般指小前端，即开发web相关东西：网页，微信小程序。
- 大前端：即开发包括小前端的一切东西+APP+其它展示相关的东西。

本篇文章会从小前端讲到大前端，甚至全栈。

## 1.1 为什么选择前端

前端在前些年不受重视，往往在行业内被称为**切图仔**，但从Chorme被研发出来以后，得力于V8引擎，前端出现了改变命运的东西**Nodejs**。得力于Nodejs，现在前后端分离已是大势所趋，并且**现在前端的深度和广度在短短几年的时间翻了好多倍。**

同时因为移动平台的崛起，响应式布局和自适应布局的兴起，微信小程序的强势降临，这些都给广大的前端人员创造了大量的就业岗位。

正因为前几年的前端不受重视，到现在为止还有非常多的人愿意选择后端开发而不愿意选择前端，所以这对新学习前端的人来说也是一个机会。

# 2. 前端三剑客

入门前端首先得学习下面这3种语言：

1. **HTML**：网页的骨架，组成网页的基本元素。
2. **CSS**：网页的皮肉，一个网页好不好看全看CSS编写的好不好。
3. **JavaScript**：负责网页的交互，如果一个网页中没有JavaScript那它就不能实现点击等一些列互动。

学习的难易程度**由上到下依次递增**，目前在前端的面试中，JavaScript是被问的最多的，几乎百分之80的题都是关于JavaScript，所以学习的重点也应该放在JavaScript上。

从下面开始，就正式介绍前端的学习路线。

## 2.1 HTML、CSS

这两个一般都是放在一起学习的，一般情况下这两项不难，CSS比较难调，每个人写CSS样式都会调很久，尤其是写响应式布局的时候。

在当下，由于兼容IE是一个成本非常高的事情，所以很多项目都会放弃兼容IE浏览器，而不兼容IE，CSS几乎就不用考虑兼容性问题。

推荐视频：[黑马程序员pink老师前端入门视频教程](https://www.bilibili.com/video/BV14J4114768)。

**该视频讲解是非常全面的，我当时就是根据这个视频入门的前端。**

## 2.2 JavaScript

JavaScript通过这么多年的发展，已经不再仅仅是一个脚本语言，现在有些企业甚至会用Node.js开发后端，甚至开发桌面应用，比如著名的VSCode就是JavaScript进行开发的。

JS的学习需要分为两步，一个是ES5，一个是ES6+，ES5是早期JavaScript规范，一直用了10多年，直到2015年出ES6的规范，才完善了很多JavaScript当时设计不合理的地方。

JS里面的讲究可就太多了，例如**Promise，变量的作用域，this指针，原型和原型链...**这些概念都是需要去了解的，并且在面试的时候非常容易被问到。

推荐视频：[尚硅谷最新版JavaScript基础全套教程完整版](https://www.bilibili.com/video/BV1YW411T7GX)。

推荐书籍：**你不知道的JavaScript**。

## 2.3 入门完成

学习完了上面前端三剑客后，恭喜你，你已经正式入门前端行业，这个时候你就可以开始写一些小网站，也正是因为到了这一步，前端的魅力也才刚刚开始散发出来，接下来就要开始讲前端的进阶部分。

# 3. 前端框架

## 3.1 jQuery

前朝余孽系列，虽然目前已经不推荐使用jQuery进行网页编写，但是因为它驰骋前端十来年，还是有一部分大公司的网站基于jQuery进行开发，比如京东。

**目前该项技术在前端开发中几乎已经不用了，可以不用学习**，但是，jQuery在JavaScript编写网页脚本中还是占有一席之地，比如油猴脚本的编写。

推荐视频：[「李南江」jQuery+Ajax从放弃到知根知底](https://www.bilibili.com/video/BV17W41137jn?from=search&seid=6802758493596450743)。

## 3.2 Vue

前端三大框架之一，目前国内最火热的前端框架，没有之一！

**推荐重点学习，几乎你学会了Vue，你就可以开始找工作。**

因为Vue是需要重点学习的，所以这里就重点讲解。

### 3.2.1 Vue Router

因为Vue是单页面应用，如果你要从一个页面跳转到另一个页面你就需要使用Vue Router，同时Vue Router还存在路由守卫，即可以判断你是否有权限进入该页面，如果没有权限则给你跳转到其它页面。

动态路由：后端给你传来路由信息，前端进行动态加载，如果后端没有传路由的界面则无法进行访问，这也是用来做权限判断。

### 3.2.2 Vuex

> Vuex 是一个专为 Vue.js 应用程序开发的状态管理模式。它采用集中式存储管理应用的所有组件的状态，并以相应的规则保证状态以一种可预测的方式发生变化。

在项目中，由于组件化的原因经常会涉及到数据传递：子传父、父传子、爷传孙、孙传爷、兄弟之间互传等等情况。

其中兄弟之间的值传递是最麻烦的，而使用Vuex就可以完美解决这个问题。

### 3.2.3 Element UI

现在Vue中使用最广泛的UI框架，由饿了么团队开源。

Element UI的学习重点和难点是**表单验证！**在开发中经常会用到。

![image-20210201175435767](/images/web/一个完全不懂的编程的新人如何入门前端/image-20210201175435767.png)

### 3.2.4 Vue 3.x

时隔几年Vue发布的新版本，受到了React Hook的启发，从Vue 3.x开始也进入了函数式编程时代，推荐学习，Vue 3.x替代Vue 2.x也只是时间问题。

## 3.3 React

前端三大框架之一，大公司的最爱，由大名鼎鼎的Facebook进行开源，目前国外最火爆的前端框架，也没有之一，在国外React的使用广泛度远高于Vue，虽然不知道Vue 3.0发布后Vue是否已经追了上来。

上手难度比起Vue来说难一点，因为很多东西都需要自己去配置，不像Vue一样官方脚手架已经帮你配置好了。

推荐视频：[尚硅谷2021版React技术全家桶全套完整版](https://www.bilibili.com/video/BV1wy4y1D7JT?from=search&seid=4180350080267972113)。

## 3.4 Angular

前端三大框架之一，由Google研发并且开源，是前端框架的开山鼻祖，后面的React和Vue都受到了它的启发。

目前发布了Angular2，语法已经发生了翻天覆地的变化，很多Angular的原开发者已经开始投入Vue和React的怀抱。

因为国内公司对于Angular的需求较少，所以如果在你有精力的情况下可以进行学习，而我到现在都还没有来得及学...

## 3.5 jest

目前最火的前端测试框架，由Facebook发布。

但是就目前来说，前端编写测试代码还是非常复杂和繁琐的，而且大部分外包项目由于赶工期，并没有时间让你编写测试代码。

推荐视频：[前端自动化测试从基础+实战（Jest TDD BDD）](https://www.bilibili.com/video/BV1yA411b7EV?from=search&seid=7846829966624799734)。

# 4. 进阶

## 4.1 TypeScript

用JavaScript编写项目，尤其是当项目过大并且几个人同时开发时，因为JavaScript是弱类型语言，又因为ajax的出现，很多数据都是通过后端返回到前端，**随着时间一长或者原开发人员的离职，后面接手项目的人员可能并不知道后端传来的数据中到底有什么。**

JavaScript因为是弱类型，提示也非常不智能，后端返回的数据往往是一个JSON格式，而将它解析成一个对象并且在项目中调用这个对象时，使用`xxx.xxx`的形式，你不会得到智能提示，这也导致在开发过程中很可能将属性名字打错，尤其是在字段名较长的情况下，即使是经验丰富的程序员可能都会在这上面犯错。

有时候一个有值的属性却打印出来是undefined，检查了半天发现是自己的属性名称打错，场面非常尴尬。

而TypeScript的出现就完美解决了这个问题，其实为了解决这个问题有很多公司都推出了它们的语言，比如Google的Dart，只是到**最后TypeScript成功突围而已。**

![img](/images/web/一个完全不懂的编程的新人如何入门前端/v2-4bbcde85a72cf9950c58238846f2be56_720w.png)

可以看到TypeScript的下载量持续走高，说明越来越多的人开始使用TypeScript，而我就是一个TypeScript的忠实用户。

推荐视频：[尚硅谷2021版TypeScript教程（李立超老师TS新课）](https://www.bilibili.com/video/BV1Xy4y1v7S2?from=search&seid=4522140681153316495)。

## 4.2 Echarts

![image-20210201171859160](/images/web/一个完全不懂的编程的新人如何入门前端/image-20210201171859160.png)

**必学技能。**

> ECharts，一个使用 JavaScript 实现的开源可视化库，可以流畅的运行在 PC 和移动设备上，兼容当前绝大部分浏览器（IE8/9/10/11，Chrome，Firefox，Safari等），底层依赖矢量图形库 ZRender，提供直观，交互丰富，可高度个性化定制的数据可视化图表。

主要是用来做数据展示，在开发后台系统的时候百分百会用到。

## 4.3 uni-app

开发多端小程序的不二选择，目前市场上有非常多的小程序类别，比如微信/支付宝/百度/头条/QQ/钉钉/淘宝，如果你不使用这类的多端框架，每个平台的小程序都要开发一次，那也太费时费力了吧。

uni-app的出现就解决了这些问题，虽然官方说它可以用来开发原生APP，但是这里还是**不太推荐使用它开发原生APP**，因为就社区的反馈来讲，开发一个小项目只展示几个页面的APP没有问题，但是要开发那些中大型项目，就会遇到非常多的坑。

推荐视频：[uniapp从入门到精通](https://www.bilibili.com/video/BV1CC4y1476y)。

## 4.4 webpack

目前前端项目使用的最多的打包方式，虽然现在主流的框架已经是给你配置好的，但是项目优化的时候会用到webpack相关知识，而且有些面试官喜欢问一些webpack上的问题，所以了解一下也没坏处。

视频教程：[尚硅谷最新版Webpack](https://www.bilibili.com/video/BV1e7411j7T5?from=search&seid=7923951550698211675)。

## 4.5 vite

由Vue作者发布的Vue 3.0打包工具，特点是毫秒级启动，但是目前还存在不少缺陷。

## 4.6 SASS

**必学技能之一。**

因为CSS在早期不支持变量编写（现在已经支持），SASS就是CSS的扩展语言，支持变量、函数等功能，使得编写界面样式的繁琐度大大降低。

## 4.7 canvas

HTML5新增标签，用于在网页实时生成图像，并且可以操作图像内容，基本上它是一个可以用JavaScript操作的位图（bitmap）。

目前的H5游戏几乎都是用canvas进行实现的，不过对于一个前端开发来说不需要了解那么多，除非你要做3D建模或者H5游戏。

# 5. 全栈路线

## 5.1 Nodejs和Express

Nodejs使得前端可以开始开发工程化项目。

而Express让Nodejs开发后端项目变得更简单。

推荐视频：[nodejs教程_2020年Nodejs+Express+Koa2入门实战视频教程](https://www.bilibili.com/video/BV11t411k79h?from=search&seid=8938246747112482278)。

## 5.2 Nest.js

Nodejs平台的“springboot”框架，主要语言是用TypeScript进行编写，也推荐使用TypeScript作为开发语言。

目前Nodejs做企业级后端的最好选择，支持分布式。

推荐教程：[Nestjs教程_Nest入门视频教程（IT营大地）](https://www.bilibili.com/video/BV1jJ411u7ju?from=search&seid=8476625855316810832)。

## 5.3 Golang

高性能、分布式、区块链这一系列当下最火的理论，让Google公司开发的这款语言在最近火了起来，现在一大部分PHP开发者已经开始转战Golang。

由于Golang的语法简单的特点，非常适合前端开发者进行学习，它不像Java一样拥有巨大的历史包袱。

推荐视频：[尚硅谷Go语言核心编程教程全套完整版](https://www.bilibili.com/video/BV1ME411Y71o?from=search&seid=5373316733431277363)。

该视频有些地方讲解的非常深以及非常好，推荐有兴趣的可以看看。

## 5.4 C++和Rust

因为JavaScript是单线程的原因，所以性能上会出现瓶颈，而Nodejs是建立在V8引擎之上的，而V8引擎又是使用C++进行编写，所以当JavaScript遇到性能上的问题时，就可以使用C++和Rust进行解决。

**新入门的时候不要去学习！新入门的时候不要去学习！新入门的时候不要去学习！**因为这已经涉及到架构层面的知识，我也只是听说过可以这样做。

# 6. 大前端路线

## 6.1 React Native

使用React语法开发跨端项目，开发的项目可以在IOS和Android上运行，但是自从发布到现在依然有着非常多的坑没有填补，以至于到目前为止版本号都没有上1.x。

又因为Airbnb宣布放弃使用React Native，给React Native造成了沉重的打击，以至于Flutter迅速超越React Native。

## 6.2 Flutter

Google公司推出的一套代码运行在IOS和Android上的框架，它实现了双端展示几乎完全一致，同时有着更接近于原生的性能，短短发布几年来受到了广泛的关注。

唯一一个不好的一点是它的开发语言用的是Dart，不过如果你会TypeScript，再去上手Dart是几乎没有什么难度。

推荐视频：[Dart Flutter教程](https://www.bilibili.com/video/BV1S4411E7LY?from=search&seid=17715444732857923611)。

## 6.3 uni-app

不推荐跨端开发...我之前开发的一个微信小程序我尝试打包成APP进行运行，发现会有兼容性上面的问题，如果要处理兼容性问题需要花费大量的时间，更推荐上面的Flutter。

# 7. Adobe XD

> 使用逼真的设计分享您的故事。无论是线框、动画制作、原型创建、协作等，它都可实现，是一体式 UI/UX 设计工具。

因为前端人员时不时会自己想要给自己做一个博客、主页等，Photoshop、Sketch、AI这种专业的设计工具貌似又显得不是那么容易上手，这个时候Adobe XD就是你的不二之选，上手特别简单，并且还支持蓝湖，用来处理SVG也是特别的方便。

推荐视频：[Adobe XD软件入门视频教程](https://www.bilibili.com/video/BV1a4411v7G4?from=search&seid=17499908889803430609)。

# 8. 最后

前端需要学习的内容是非常多的，但其实**前端三剑客（HTML+CSS+JavaScript）+Vue+Echarts**学习完成之后你就可以开始找工作了。

后面说了那么一大堆如果你有兴趣就学习一下，没有兴趣就放到后面有兴趣或者职业发展出现瓶颈的时候再学习也不迟。

本篇文章就讲到这里，希望对你学习前端有一定的帮助。
