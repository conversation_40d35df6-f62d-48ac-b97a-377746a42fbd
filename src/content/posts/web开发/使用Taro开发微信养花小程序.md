---
title: "使用Taro开发微信养花小程序"
date: 2020-05-07
categories:
  - "web开发"
tags:
  - "Taro"
  - "小程序"
---


# Taro简介

Taro是一套遵循 React 语法规范的**多端开发**解决方案。

使用Taro，只需要书写一套代码，再通过 Taro 的编译工具，将源代码分别编译出可以在不同端（微信 / 京东 / 百度 / 支付宝 / 字节跳动 小程序、快应用、H5、React-Native 等）运行的代码。

注意：**如果不需要进行多端开发并且需要对小程序深度定制，建议还是直接使用原生开发。**

# 安装Taro

```
npm install -g @tarojs/cli
```

# 初始化项目

```
taro init myApp
```

模板拉取完毕后会给你一些选项，根据自身喜好选择就行了，我这里使用TypeScript。

![img](/images/v2-6f735d69e4c55ab67eb0ab09edc0f877_b.png)

其中因为node-sass的原因，运行npm install时可能会报错。这个时候就将npm换成淘宝镜像应该就可以解决。

```
npm config set registry https://registry.npm.taobao.org
```

# 运行项目

经过漫长的等待后，你还需要准备微信开发者工具，因为必须要通过开发者工具进行预览。

打开工程目录下的package.json，可以看到下面的命令就是构建不同平台的小程序。使用方法是在前面加上npm run或者yarn比如预览微信小程序就是npm run dev:weapp 

```
"scripts": {
  "build:weapp": "taro build --type weapp",
  "build:swan": "taro build --type swan",
  "build:alipay": "taro build --type alipay",
  "build:tt": "taro build --type tt",
  "build:h5": "taro build --type h5",
  "build:rn": "taro build --type rn",
  "build:qq": "taro build --type qq",
  "build:quickapp": "taro build --type quickapp",
  "dev:weapp": "npm run build:weapp -- --watch",
  "dev:swan": "npm run build:swan -- --watch",
  "dev:alipay": "npm run build:alipay -- --watch",
  "dev:tt": "npm run build:tt -- --watch",
  "dev:h5": "npm run build:h5 -- --watch",
  "dev:rn": "npm run build:rn -- --watch",
  "dev:qq": "npm run build:qq -- --watch",
  "dev:quickapp": "npm run build:quickapp -- --watch"
},
```

编译完成后会出现一个dist文件夹，我们将工程文件夹导入到微信开发者工具中。 如果导入成功就会看到下面的界面：

![img](/images/v2-9f95a15fefdb30dc3a45d66d2953d83e_b.png)

我们再次引入Taro-ui。这个是UI组件库，有些组件如果自己去写的话不仅非常消耗时间，而且可能会有BUG，所以这个时候就需要选用UI组件库。

https://github.com/NervJS/taro-uigithub.com

引入

```
npm install taro-ui
```

# 正式开发

由于想要开发的代码逻辑还是比较多的，所以这里就不一步一步的讲解代码。

![img](/images/v2-9dad95b98abfa221827866e965591e11_b.png)

# 大体思路

先简单的搭出这样一个界面，其中植物有3种状态，一种是**需要浇水**，一种是**正在成长**，最后一种是**已经枯萎**。同时还设计了一个添加植物的按钮，通过这个按钮，能够新增植物。并且植物的数据是从数据库中进行读取，以保证下次打开小程序后这些数据仍然存在。

![img](/images/v2-502cdcdd83bfd5c6091b600f3e246d99_b.gif)

搭建中的界面

# 植物管理

由于我这里使用了TypeScript我就直接创建了一个植物类。

```
export default class Flower {
  flowerName: string  // 植物名字
  overTime: number    // 浇水间隔
  imgUrl: string      // 植物图片地址

  constructor(flowerName: string, overTime: number, imgUrl: string) {
    this.flowerName = flowerName
    this.overTime = overTime
    this.imgUrl = imgUrl
  }
}
```

然后实例化这个类，并且存到数组中供页面调用。

```
import Flower from './Flower'

const flowers: Flower[] = []
flowers.push(new Flower('栀子花', 3600, '图片地址'))
flowers.push(new Flower('丁香花', 1200, '图片地址'))
flowers.push(new Flower('茉莉花', 2400, '图片地址'))
flowers.push(new Flower('仙人掌', 3600, '图片地址'))


export default flowers
```

# 后端和数据库

由于我本人对后端内容不是太熟悉，所以大体搭建了一个数据库，意思一下。

这里我选用的Spring boot搭建后端，mySql作为数据库。如果是前端搭建数据库，还是推荐更适合前端的Nodejs和MongoDB。

首先我们需要一个数据表：已经添加的植物表。那么它应该就具有：名称，添加时间，上次浇水时间，浇水间隔时间这些属性。

# 网络请求

Taro对小程序的网络请求API进行了Promise封装，具体可以参考官方文档：

https://nervjs.github.io/taro/docs/apis/network/request/request/nervjs.github.io

这里我们再次将Taro提供的api进行封装，封装的原因是因为如果我们想要引入其他的网络请求库，就不用再去页面中用到的地方一个一个的更改api，直接修改封装文件中的api就可以了。

```
import Taro from "@tarojs/taro";

export default function ajax(url = "", params = {}, type = "GET") {
  let promise;
  return new Promise((resolve, reject) => {
    if (type.toUpperCase() === "GET") {
      let paramsStr = "";
      Object.keys(params).forEach(key => {
        paramsStr += key + "=" + params[key] + "&";
      });
      if (paramsStr) {
        paramsStr = paramsStr.substr(0, paramsStr.lastIndexOf("&"));
      }
      url += "?" + paramsStr;
      //发起GET请求
      promise = Taro.request({
        url: url,
        header: {"content-type": "application/json"},
      });
    } else if (type.toUpperCase() === "POST") {
      promise = Taro.request({
        method: "POST",
        url: url,
        data: params,
        header: {"content-type": "application/json"}
      });
    }

    promise.then((response) => {
      resolve(response.data);
    }).catch(error => {
      reject(error);
    });
  });
}
```

到这一步，我们已经完成了数据库和后端的配置，剩下的就是需要在前端进行调用这些接口。

# 数据管理

虽然组件之间的参数传递并不复杂，但是我这里为了方便，还是使用redux和redux-thunk。

使用 Redux | Taro 文档nervjs.github.io

https://github.com/reduxjs/redux-thunkgithub.com

由于我对redux-thunk不是很熟，所以又去翻找了一下资料，在redux和后端数据这里花费了不少时间。

![img](/images/v2-fc800fd70f2bfc6b700e6b4b09aa0d31_b.gif)

从上面的图可以看到，这里已经可以添加花了，但是由于每次点击添加不仅要向后端发送请求将点击的花添加到数据库，并且还需要将花的列表进行重新请求。

剩下的就是实现浇花功能了，该功能的实现必须也要后端配合实现，实现过程就是当你**点击浇水的时候，将数据库中对应的花的上一次浇水时间改成当前时间。**这个的实现几乎全是由后端实现，跟前端没啥关系，最多只是调用一下接口将点击浇水的花的ID传送过去。

最后增加删除枯萎的花的功能，也是需要后端提供接口。

![img](/images/v2-de5dfd4f7d2d85936c7094d65e37ec6c_b.gif)

到这里基本上小程序就已经开发完成了，但是不知道是因为我的后端传输数据很慢还是其它的原因，可以看到点击删除后会卡一会。

之后我调整了一下代码格式，因为Taro创建工程的时候会自动集成ESLint代码检测，再次尝试添加删除植物，发现这次没有上面那么卡顿了，所以也不知道之前是哪儿出现了问题。

![img](/images/v2-dd399d409804cc37c9b76e53c745a946_b.gif)

可以看到，还是比较流畅

# 最终效果

![img](/images/v2-b679caf5c262af8a528ebeac5a38d42f_720w.gif)

# 总结

整个项目的难点在于redux之间的数据传递，还有时间的换算，除此之外也没有什么难点了，虽然看似简单的一个小程序，但是可以很好的理解redux。
