---
title: "前端需要了解的Nginx知识"
date: 2020-9-5 16:40:10
categories:
  - "后端"
tags:
  - "Nginx"
---


# 1. 基本概念

## 1.1 正向代理

Nginx不仅可以做反向代理，实现负载均衡。还能用作正向代理来进行上网等功能。

正向代理：如果把局域网外的Internet想象成一个巨大的资源库，则局域网中的客户端要访问Internet，则需要通过代理服务器来访问，这种代理服务就称为正向代理。

![image-20200905112454660](/images/web/image-20200905112454660.png)

## 1.2 反向代理

反向代理，其实客户端对代理是无感知的，因为客户端不需要任何配置就可以访问，我们只需要将请求发送到反向代理服务器，由反向代理服务器去选择目标服务器获取数据后，在返回给客户端，此时反向代理服务器和目标服务器对外就是一个服务器，暴露的是代理服务器地址，隐藏了真实服务器 IP 地址。

![image-20200905112642925](/images/web/image-20200905112642925.png)

## 1.3 负载均衡

单个服务器解决不了，我们增加服务器的数量，然后将请求分发到各个服务器上，将原先请求集中到单个服务器上的情况改为将请求分发到多个服务器上，将负载分发到不同的服务器，也就是我们所说的**负载均衡**。

![image-20200905112730918](/images/web/image-20200905112730918.png)

## 1.4 动静分离

为了加快网站的解析速度，可以把动态页面和静态页面由不同的服务器来解析，加快解析速度。降低原来单个服务器的压力。

![image-20200905112745047](/images/web/image-20200905112745047.png)

# 2. 入门

## 2.1 安装

## 2.2 常用命令

```bash
# 需要先进入到 /usr/local/nginx/sbin 文件夹下

./nginx  # 启动命令
./nginx s stop # 关闭命令
./nginx s reload # 重新加载命令
```


## 2.3 配置文件

`/usr/local/nginx/conf/nginx.conf`  Nginx配置文件地址

### 2.3.1 全局块

从配置文件开始到 events 块之间的内容，主要会设置一些影响nginx 服务器整体运行的配置指令，主要包括配置运行 Nginx 服务器的用户（组）、允许生成的 worker process 数，进程 PID 存放路径、日志存放路径和类型以及配置文件的引入等。

比如`worker_processes 1;` 

这是 Nginx 服务器并发处理服务的关键配置，worker_processes 值越大，可以支持的并发处理量也越多，但是会受到硬件、软件等设备的制约

### 2.3.2 events 块 

events 块涉及的指令主要影响 Nginx 服务器与用户的网络连接，常用的设置包括是否开启对多 work process 下的网络连接进行序列化，是否允许同时接收多个网络连接，选取哪种事件驱动模型来处理连接请求，每个 word process 可以同时支持的最大连接数等。

比如`worker_connections 1024;` 

上述例子就表示每个 work process 支持的最大连接数为 1024. 这部分的配置对 Nginx 的性能影响较大，在实际中应该灵活配置。

### 2.3.3 http 块

这算是 Nginx 服务器配置中最频繁的部分，代理、缓存和日志定义等绝大多数功能和第三方模块的配置都在这里。 需要注意的是：http 块也可以包括 http全局块、server 块。

#### 2.3.3.1 http 全局块

http全局块配置的指令包括文件引入、MIME-TYPE 定义、日志自定义、连接超时时间、单链接请求数上限等。

#### 2.3.3.2 server 块

这块和虚拟主机有密切关系，虚拟主机从用户角度看，和一台独立的硬件主机是完全一样的，该技术的产生是为了节省互联网服务器硬件成本。

每个 http 块可以包括多个 server 块，而每个 server 块就相当于一个虚拟主机。 而每个 server 块也分为全局 server 块，以及可以同时包含多个 locaton 块。

**全局 server 块**

最常见的配置是本虚拟机主机的监听配置和本虚拟主机的名称或IP配置。

**location 块**

一个 server 块可以配置多个 location 块。

这块的主要作用是基于 Nginx 服务器接收到的请求字符串（例如 server_name/uri-string），对虚拟主机名称（也可以是IP别名）之外的字符串（例如 前面的 /uri-string）进行匹配，对特定的请求进行处理。地址定向、数据缓存和应答控制等功能，还有许多第三方模块的配置也在这里进行。

# 3. Nginx 配置

## 3.1 反向代理

![image-20200905163028436](/images/web/image-20200905163028436.png)

**server_name**：为监视的服务地址

## 3.2 location 指令说明

该指令用于匹配 URL。 语法如下：

`=` ：用于不含正则表达式的 uri 前，要求请求字符串与 uri 严格匹配，如果匹配成功，就停止继续向下搜索并立即处理该请求。

`~`：用于表示 uri 包含正则表达式，并且区分大小写。

`~*`：用于表示 uri 包含正则表达式，并且不区分大小写。

`^~`：用于不含正则表达式的 uri 前，要求 Nginx 服务器找到标识 uri 和请求字符串匹配度最高的 location 后，立即使用此 location 处理请求，而不再使用 location 块中的正则 uri 和请求字符串做匹配。

 注意：如果 uri 包含正则表达式，则必须要有 `~` 或者`~*`标识。

## 3.3 负载均衡

随着互联网信息的爆炸性增长，负载均衡（load balance ）已经不再是一个很陌生的话题顾名思义，负载均衡即是将负载分摊到不同的服务单元，既保证服务的可用性，又保证响应足够快，给用户很好的体验。快速增长的访问量和数据流量催生了各式各样的负载均衡产品。

### 3.3.1 轮询（默认）

每个请求按时间顺序逐一分配到不同的后端服务器，如果后端服务器down掉，能自动剔除。

### 3.3.2 weight

代表权 重默认为 1, 权重越高被分配的客户端越多

### 3.3.3 ip_hash

每个请求按访问ip的hash结果分配，这样每个访客固定访问一个后端服务器，可以解决session的问题。

### 3.3.4 fair（第三方）

按后端服务器的响应时间来分配请求，响应时间短的优先分配。

## 3.4 动静分离

Nginx 动静分离简单来说就是把动态跟静态请求分开，不能理解成只是单纯的把动态页面和静态页面物理分离。严格意义上说应该是动态请求跟静态请求分开，可以理解成使用Nginx 处理静态页面，Tomcat处理动态页面。动静分离从目前实现角度来讲大致分为两种，一种是纯粹把静态文件独立成单独的域名，放在独立的服务器上，也是目前主流推崇的方案； 另外一种方法就是动态跟静态文件混合在一起发布，通过 nginx 来分开。

![image-20200905163832210](/images/web/image-20200905163832210.png)

设置 3d，表示在这 3 天之内访问这个 URL，发送一个请求，比对服务器该文件最后更新时间没有变化，则不会从服务器抓取，返回状态码 304，如果有修改，则直接从服务器重新下载，返回状态码 200。

