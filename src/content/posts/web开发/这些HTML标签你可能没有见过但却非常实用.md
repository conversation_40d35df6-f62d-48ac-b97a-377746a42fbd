---
title: "这些HTML标签你可能没有见过但却非常实用"
date: 2021-3-2 09:24:36
categories:
  - "web开发"
tags:
  - "HTML"
---


在进行前端开发的时候，我们大部分的学习点都在JavaScript上面，花费在CSS上面的时间非常少，更别说花费在HTML上的时间，简直是少的可怜，可能到目前为止如果让你不使用UI组件库写出一个表单都要写一会~~（其实我也是）~~。

总而言之，由于UI组件库的原因，我们基本上只认识一些最普通的HTML标签就可以写出一个完整的界面，但本篇文章就专门介绍一下那些你没有见过但是有一定实用性的标签。

因为HTML标签的特殊性，所以跟CSS和JavaScript这两种不一样，浏览器不认识这个标签就是不认识，没有办法去做兼容性，所以有些看似实用的标签，可能因为兼容性问题还是得慎用（**没错，说的就是IE**）。

# 1. `<fieldset />`

该标签的作用是**组合表单中的相关元素。**

![image-20210301170754514](/images/web/这些HTML标签你可能没有见过但却非常实用/image-20210301170754514.png)

可以看到最关键的就是两个标签`<fieldset />`和`<legend />`。

# 2. `<input />`

input应该是一个非常常用的标签了，但是它input有非常多的`type`属性。

可以参考：[HTML` <input>` type 属性](https://www.runoob.com/tags/att-input-type.html)

| 值                     | 描述                                                         |
| :--------------------- | :----------------------------------------------------------- |
| button                 | 定义可点击的按钮（通常与 JavaScript 一起使用来启动脚本）。   |
| checkbox               | 定义复选框。                                                 |
| color **New**          | 定义拾色器。                                                 |
| date **New**           | 定义 date 控件（包括年、月、日，不包括时间）。               |
| datetime **New**       | 定义 date 和 time 控件（包括年、月、日、时、分、秒、几分之一秒，基于 UTC 时区）。 |
| datetime-local **New** | 定义 date 和 time 控件（包括年、月、日、时、分、秒、几分之一秒，不带时区）。 |
| email **New**          | 定义用于 e-mail 地址的字段。                                 |
| file                   | 定义文件选择字段和 "浏览..." 按钮，供文件上传。              |
| hidden                 | 定义隐藏输入字段。                                           |
| image                  | 定义图像作为提交按钮。                                       |
| month **New**          | 定义 month 和 year 控件（不带时区）。                        |
| number **New**         | 定义用于输入数字的字段。                                     |
| password               | 定义密码字段（字段中的字符会被遮蔽）。                       |
| radio                  | 定义单选按钮。                                               |
| range **New**          | 定义用于精确值不重要的输入数字的控件（比如 slider 控件）。   |
| reset                  | 定义重置按钮（重置所有的表单值为默认值）。                   |
| search **New**         | 定义用于输入搜索字符串的文本字段。                           |
| submit                 | 定义提交按钮。                                               |
| tel **New**            | 定义用于输入电话号码的字段。                                 |
| text                   | 默认。定义一个单行的文本字段（默认宽度为 20 个字符）。       |
| time **New**           | 定义用于输入时间的控件（不带时区）。                         |
| url **New**            | 定义用于输入 URL 的字段。                                    |
| week **New**           | 定义 week 和 year 控件（不带时区）。                         |

例如：`<input type="color" />`。

![color](/images/web/这些HTML标签你可能没有见过但却非常实用/color.gif)

上面那些属性各位都可以自行尝试一下。

# 3. `<progress />`

进度条标签，在平时我们要实现一个进度条怎么办？使用CSS调整盒子的宽度，达到进度条的效果，而且大部分UI框架都已经附带了进度条，**但其实可以通过`<progress />`HTML标签直接实现进度条。**

![image-20210228183914015](/images/web/这些HTML标签你可能没有见过但却非常实用/image-20210228183914015.png)

通过使用JavaScript设置`max`属性和`value`属性就可以控制进度条的显示。

# 4. `<sub />`、`<sup />`

- sup：上标
- sub：下标

使用起来很简单，比如H<sub>2</sub>O、O<sub>2</sub>、2<sup>32</sup>这类似的上下标都可以使用这两个标签来完成，当然如果要使用CSS自行实现也是可以的，就是稍微麻烦一点。

代码：`H<sub>2</sub>O、O<sub>2</sub>、2<sup>32</sup>`。

# 5. `<picture />`

> 根据屏幕匹配的不同尺寸显示不同图片，如果没有匹配到或浏览器不支持 picture 属性则使用 img 元素。

使用起来也非常简单，直接来看一下效果：

![picture](/images/web/这些HTML标签你可能没有见过但却非常实用/picture.gif)

可以看到根据屏幕尺寸的变化，最后呈现出来的图片也是不一样的，该标签在做响应式界面的时候非常好用。

演示代码：

```html
<picture>
  <source media="(min-width:650px)" srcset="https://ss1.bdstatic.com/70cFvXSh_Q1YnxGkpoWK1HF6hhy/it/u=1091405991,859863778&fm=26&gp=0.jpg">
  <source media="(min-width:465px)" srcset="https://ss0.bdstatic.com/70cFvHSh_Q1YnxGkpoWK1HF6hhy/it/u=137628589,3436980029&fm=26&gp=0.jpg">
  <img alt="Flowers" src="https://ss3.bdstatic.com/70cFv8Sh_Q1YnxGkpoWK1HF6hhy/it/u=3791918726,2864900975&fm=26&gp=0.jpg" style="width:auto;">
</picture>
```

# 6. `<template />`

> **HTML内容模板（`<template>`）元素**是一种用于保存客户端内容机制，该内容在加载页面时不会呈现，但随后可以在运行时使用JavaScript实例化。

在HTML中的template就相当于是一个模板，一个已经写好的模板。

首先我们在HTML中写入下面代码：

```html
<template>
  <div class="box">
    <h2 class="title">测试template标签</h2>
    <p>这是随便的内容</p>
  </div>
</template>
```

在Chrome开发者工具中我们检查元素：**可以发现是存在template标签，只是template标签天生自带了`display: none`这个属性导致它不可见。**

![image-20210301222119289](/images/web/这些HTML标签你可能没有见过但却非常实用/image-20210301222119289.png)

那么它是如何使用的呢？

我们先看一下如果我们不使用template，要创建一个相应的HTML标签应该如何创建，当然这是在不考虑使用jQuery的情况下，因为jQuery大大减少了操作DOM的难度。

```js
document.addEventListener("DOMContentLoaded", () => {
  const df = document.createDocumentFragment();
  const div = document.createElement("div");
  const h2 = document.createElement("h2");
  const p = document.createElement("p");
  h2.textContent = "测试template标签";
  p.textContent = "这是随便的内容";
  div.className = "box";
  h2.className = "title";
  df.appendChild(div);
  div.appendChild(h2);
  div.appendChild(p);
  document.body.appendChild(df);
});
```

界面上的显示：

![image-20210301222842537](/images/web/这些HTML标签你可能没有见过但却非常实用/image-20210301222842537.png)

可以看到，**要使用JavaScript原生创建一个DOM元素是一件比较麻烦的事情**，那么如果使用template如何进行创建呢。

```js
document.addEventListener("DOMContentLoaded", () => {
  const temp = document.querySelector("template");
  const content = temp.content;
  document.body.appendChild(content);
});
```

搞定，使用template创建的DOM元素有一个`content`属性，其中就包含了template中的所有元素，只需要将这个属性挂载到DOM中就可以动态创建一个DOM元素，同时也可以通过它来操作其中的子节点。

那么如果要创建多个模板元素呢？

```js
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const temp = document.querySelector("template");
    const content = temp.content;
    document.body.appendChild(content.cloneNode(true));
    document.body.appendChild(content.cloneNode(true));
  });
</script>
```

**在template上还有一个`cloneNode`属性，使用它就可以创建多个模板。**

![image-20210301223559861](/images/web/这些HTML标签你可能没有见过但却非常实用/image-20210301223559861.png)

**注：该标签和Vue中的template标签作用不同。**

# 7. 最后

其实不知道这些HTML标签对我们的开发也造成不了任何影响，因为现在有太多的UI库已经帮我们完成了普通组件的搭建，我们要做的只是将它们组合起来，并且将精力主要放在业务逻辑的实现上面。

参考：

[Using HTML5 Templates](https://www.youtube.com/watch?v=mfN-EOkj13Q&ab_channel=SteveGriffith)

[5 Must Know HTML Tags That Almost Nobody Knows](https://www.youtube.com/watch?v=iX_QyjdctsQ&ab_channel=WebDevSimplified)
