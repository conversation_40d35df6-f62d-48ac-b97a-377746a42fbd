---
title: "前端开发环境下的新工具：vite"
date: 2020-9-6 22:20:53
categories:
  - "Vue"
tags:
  - "web开发"
---


# 1. 前言

随着Vue3.0的最终测试版的发布，尤大又发布了一个新的小工具：Vite。

如果对前端技术稍微敏感一点的朋友就会发现，在掘金这一类的论坛上，Vite经常被提起。

**Vite是在开发环境下的一种新的打包方式。**

- 面向现代浏览器的一个更轻、更快的 Web 应用开发工具，
- 基于ECMAScript 标准原生模块系统（ES Modules）实现。

[Vite GitHub地址](https://github.com/vitejs/vite)，可以看到Vite发布后，受到的关注度非常高，短短几个月，已经有过万的star，只能说这玩意，太厉害了。

# 2. 本地开启一个服务

在Vue和React项目打包后生成的静态文件，在本地如果直接打开`index.html`整个项目的样式一般很奇怪，主要是因为引用模块的位置错误导致的，解决这个问题，除了部署到服务器查看之外，还可以使用下面的命令:

```bash
npx serve
```

该命令相当于启动了一个服务器，访问地址是`localhost:5000`。这个功能我也是最近才知道，以前在本地模拟服务器一般都要依赖VScode的Live Server插件，但是我自身用的是WebStorm，就没有这个插件。

# 3. Vite

在Webpack中，要启动一个服务需要先编译，再启动。

![img](/images/web/webpack.png)

在Vite中，是先启动服务，如果你需要某一个文件，再进行编译。

这就造成Vite启动服务的速度比Webpack快了太多，即“按需编译”。

![img](/images/web/vite.png)

对于Webpack来说，不管模块是否执行，都要打包进bundle里，如果你的项目很大，那在打包上面花费的时间就会非常久。

Vite则利用现代浏览器原生支持ESM特性，省略了对模块的打包，**只有具体请求某一个文件时，才会进行打包**。

## 3.1 HMR

热更新的时候：Vite：只需要立即编译当前所修改的文件即可，所以响应速度非常快。

而 Webpack修改某个文件过后，会自动以这个文件为入口重写build一次，所有的涉及到的依赖也都会被加载一遍，所以反应速度会慢很多。

## 3.2 Build

Vite内部采用的是Rollup完成应用打包，最终还是会把文件都提前编译并打包到一起。

## 3.3 开箱即用

- **TypeScript - 内置支持，内置支持，内置支持！直接就可以使用TypeScript。**
- less/sass/stylus/postcss - 内置支持（需要单独安装所对应的编译器）

# 4. 推荐

强烈推荐看一下下面的视频，讲解的很全面，视频中手写了一个简单的Vite，本篇文章也是对视频重点的总结。

- 视频：[前端新工具Vite，快速上手vue 3.0+ Vite开发](https://www.bilibili.com/video/BV1LC4y1h7BF)。
- 视频资料：[vite-essentials](https://github.com/zce/vite-essentials)。
