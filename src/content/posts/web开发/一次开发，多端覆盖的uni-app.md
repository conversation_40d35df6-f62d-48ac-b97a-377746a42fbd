---
title: "一次开发，多端覆盖的uni-app"
date: 2021-1-26 16:18:15
categories:
  - "web开发"
tags:
  - "uni-app"
---


# 1. 前言

这篇文章的大体内容其实我在去年8月份就已经完成了，但是为啥一直没有发，因为那个时候我只是看了一遍官方文档，没有仔细去研究，直到最近使用uni-app重写我去年年初做的小程序，对uni-app有了一个大致的体验，所以就决定完善这一篇文章。

![miniprogram](/images/web/一次开发，多端覆盖的uni-app/miniprogram.gif)

目前已知在电脑端打开该小程序，布局会出现错误。

该小程序可以扫描下面的二维码查看：

![mini](/images/web/一次开发，多端覆盖的uni-app/mini.png)

该小程序前端界面从重写到最后发布可能用了20小时，大多数时间都是花在页面布局上面，因为没有现成的UI，所以我只有东找找西凑凑，到处“借鉴”一下布局。

---

好了，接下来是正文

`uni-app`是一个使用Vue.js开发所有前端应用的框架，开发者编写一套代码，可发布到iOS、Android、H5、以及各种小程序（微信/支付宝/百度/头条/QQ/钉钉/淘宝）、快应用等多个平台。

> 1. 在h5端，它的性能、包体积与直接使用vue.js开发一致。
> 2. 在小程序端，它的性能比大多数开发框架更好，uni-app底层自动处理的setdata差量同步机制，比开发者手动写setdata更好，就像使用vue.js更新界面比手动写js修改dom更高效一样。
> 3. 在App，uni-app支持webview渲染和原生渲染双引擎，启用原生渲染时，css写法受限，但性能是很接近原生开发的效果的，在当前的手机环境下，千万日活以下的应用在App使用uni-app也不会遇到任何压力。当然也可以在已经做好的原生App中将部分页面改为uni-app实现。
>

上面这些话是官方说的，从目前大多数开发者的评价来看，uni-app目前存在着社区回复不及时，开发多端时遇到的坑略多，某些BUG过了很久都没有修复的问题。

# 2. 快速上手

有两种开发方式，一种是使用HBuilderX创建项目，一种是使用vue-cli，这里着重介绍vue-cli。

**使用vue-cli创建的项目，可以在WebStorm和VScode上面进行开发，而使用HBuilderX创建的项目默认是没有`package.json`文件的。**

因为HBuilderX默认自带了项目依赖，并且这些项目依赖会随着HBuilderX的升级而升级。这就会造成，如果有两个开发者，他们手中的HBuilderX版本不一样，就可能造成运行项目后得到的效果不同，甚至项目都跑不起来。

所以这里强烈建议使用vue-cli进行创建项目，不光可以在更多的IDE上面进行开发，还解决了环境不同的问题。

## 2.1 全局安装vue-cli

```bash
npm install -g @vue/cli
```

## 2.2 创建uni-app

**使用正式版**（对应HBuilderX最新正式版）

```bash
vue create -p dcloudio/uni-preset-vue my-project
```

**使用alpha版**（对应HBuilderX最新alpha版）

```bash
vue create -p dcloudio/uni-preset-vue#alpha my-alpha-project
```

推荐新手使用正式版

模板选择：官方推荐初次体验选择`hello uni-app`。

![image-20200820171424400](/images/web/一次开发，多端覆盖的uni-app/image-20200820171424400.png)

当然，如果你想要使用TypeScript，就可以选择**默认模板（TypeScript）**。

# 3. 目录结构

```
┌─cloudfunctions        云函数目录（阿里云为aliyun，腾讯云为tcb，详见uniCloud）
│─components            符合vue组件规范的uni-app组件目录
│  └─comp-a.vue         可复用的a组件
├─hybrid                存放本地网页的目录
├─platforms             存放各平台专用页面的目录
├─pages                 业务页面文件存放的目录
│  ├─index
│  │  └─index.vue       index页面
│  └─list
│     └─list.vue        list页面
├─static                存放应用引用静态资源（如图片、视频等）的目录，注意：静态资源只能存放于此
├─wxcomponents          存放小程序组件的目录，详见
├─main.js               Vue初始化入口文件
├─App.vue               应用配置，用来配置App全局样式以及监听
├─manifest.json         配置应用名称、appid、logo、版本等打包信息
└─pages.json            配置页面路由、导航条、选项卡等页面类信息
```

可以看到，在过去我用HBuilderX创建的项目目录下没有`package.json`文件，这就直接导致无法在HBuilderX之外的IDE进行开发。

其中有两个非常重要的文件：

1. manifest.json：配置应用名称、appid、logo、版本等打包信息，**一般来说，项目开发完毕需要上线时，就需要对这个文件进行配置。**
2. pages.json：配置页面路由、导航条、选项卡等页面类信息，**该文件几乎新增一个界面就需要进行配置。**

# 4. CSS

+ 在uni-app中推荐使用rpx替代px，rpx是一种根据屏幕宽度自适应的动态单位。以750宽的屏幕为基准，750rpx恰好为屏幕宽度。屏幕变宽，rpx实际显示效果会等比放大，在iPhone6的屏幕下，`1px=2rpx`；

+ 使用`@import`语句可以导入外联样式表，`@import`后跟需要导入的外联样式表的相对路径，用`;`表示语句结束。

+ 支持基本常用的选择器class、id、element等。

+ 在 `uni-app` 中不能使用 `*` 选择器。

+ `page` 相当于 `body` 节点。

+ **定义在App.vue中的样式为全局样式，作用于每一个页面。在pages目录下的vue文件中定义的样式为局部样式，只作用在对应的页面，并会覆盖App.vue中相同的选择器。**

+ `uni-app` 支持使用字体图标，使用方式与普通 `web` 项目相同，需要注意以下几点：

  - 字体文件小于 40kb，`uni-app` 会自动将其转化为 base64 格式；

  - 字体文件大于等于 40kb， 需开发者自己转换，否则使用将不生效；

  - 字体文件的引用路径推荐使用以 ~@ 开头的绝对路径。

    ```css
     @font-face {
         font-family: test1-icon;
         src: url('~@/static/iconfont.ttf');
     }
    ```


# 5. 开发时需要注意的点

1. **尽量使用uni-app官方提供的函数以及官方明确指定的HTML标签**，比如`<view />`、`<image />`、`<text />`这些标签，坚决不要使用官方没有提到的标签，比如`<div />`、`<img />`，这些标签可能在Web端可以正常显示，但是在其它端可能会显示异常。
2. 网络请求需要放弃我们平时使用的`axios`（因为兼容性，同样可能Web端支持，其它端不支持），需要使用uni-app官方提供的[uni.request](https://uniapp.dcloud.io/api/request/request?id=request)，你可以自行将它封装成Promise。
3. 跳转页面使用官方提供的[uni.navigateTo](https://uniapp.dcloud.io/api/router?id=navigateto)进行跳转，不要使用Vue原生的跳转方式。
4. 数据缓存使用[uni.setStorage](https://uniapp.dcloud.io/api/storage/storage?id=setstorage)。不要使用Window localStorage属性。
5. **尽量全部使用官方提供的方法，不要轻易使用第三方库，即便是开发Web端很常用的库，这些库引用后都有可能带来兼容性问题。**

综上所述，uni-app开发中有一种束手束脚的感觉，毕竟需要一套代码在多个终端上面运行，所以就失去了非常多的Vue特性，同时根据社区的反馈来看，Vue有很多特性都存在兼容性问题，所以尽量使用最简单的Vue语法。

# 6. 最后

uni-app目前在国内的关注度非常高，很多公司都开始招uni-app开发者，因为uni-app官方说的一套代码可以在多个平台的小程序甚至IOS和Android上面运行。

但根据我目前了解是有一些兼容性问题，比如某些情况能在Web端和小程序端跑起来，但是在APP上面会出现卡顿，甚至还可能出现多个客户端表现不一致的情况。

就目前来看，使用uni-app开发小程序是完全没有问题的，但是要开发IOS和Android端，还是得慎重考虑，尤其是APP需要使用大量原生功能时。

uni-app的学习成本低，几乎没有什么学习成本，一个会Vue的开发者几乎可以直接上手uni-app的开发，但是其中会有非常多的兼容性问题，一个习惯了Vue的开发者使用uni-app进行开发会有种束手束脚的感觉。

**如果使用uni-app来开发Web端和小程序是完全没有问题的，但是如果要开发APP，那么选用该技术栈的时候一定要慎重。**

因为用JavaScript来开发APP，大名鼎鼎的Facebook旗下的React Native仅支持双端，并且社区比uni-app活跃很多，这么多年版本号都没有上`1.x`，更别说支持多端的uni-app，如果APP仅仅是简单的页面，那无所谓，但是如果涉及拍照、上传视频、地图等等这些功能，就要仔细考虑一下是否有那个精力和实力去踩那些兼容性问题的坑。

# 7. 推荐资料

[黑马视频](https://www.bilibili.com/video/BV1BJ411W7pX)

[uniapp官网](https://uniapp.dcloud.io/)
