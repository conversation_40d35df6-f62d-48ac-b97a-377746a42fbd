---
title: "Viewer.js图片查看器"
date: 2020-06-15
categories:
  - "web开发"
tags:
  - "Viewer.js"
  - "图片查看器"
---


做界面时可能会需要图片查看的功能，就比如QQ空间查看照片的功能。

虽然这个功能自己实现也不是太难，但是直接引入第三方库有个最大的好处是第三方库一般都经过很多个版本的更新迭代，不管是性能还是体验上一般都优于自己动手写（大佬除外）。

[Viewer.js](https://github.com/fengyuanchen/viewerjs)几乎是开源出来的图片查看器中功能最全的库。 

# 1. 安装

## 1.1 npm

```shell
npm install viewerjs
```

## 1.2 browser

```html
<link  href="/path/to/viewer.css" rel="stylesheet">
<script src="/path/to/viewer.js"></script>
```

# 2. 使用

```react
<div>
  <ul id="images">
    <li><img src="picture-1.jpg" alt="Picture 1"></li>
    <li><img src="picture-2.jpg" alt="Picture 2"></li>
    <li><img src="picture-3.jpg" alt="Picture 3"></li>
  </ul>
</div>
import Viewer from 'viewerjs';
import 'viewerjs/dist/viewer.css';

const viewer = new Viewer(document.getElementById('images'), {
  // 配置选项
});
```

## 2.1 inline模式

官方给出的实例中有一个属性inline: true,这个属性指的是**在当前的容器中进行图片展示。**推荐根据情况进行开启。

![img](/images/v2-40a418dc45c96d9d51910555c8dc7c3e_720w.png)

`inline: true`

 

![img](/images/v2-dc6e7ac0818674df6b673a0bda91a0fa_720w.png)

`inline: false`时，在全屏范围内进行显示。 

## 2.2 缩略图和原图

```html
<div class="grid-item" v-for="(image,index) in imageInfo" :key="index">
  <el-card :body-style="{padding:0}" shadow="hover">
    <img :data-original="image.original"
         :src="image.thumbnail" style="margin-left: 5px;cursor: zoom-in" alt="">
  </el-card>
</div>
```

上面的代码是我在`Vue`中使用`Viewer.js`，其中`img`标签中的`src`是缩放图的地址，而` data-original`中是原图的地址。

```javascript
const viewer = new Viewer(document.getElementById('images'), {
  url: 'data-original',
});
```

在声明时将`data-original`赋值给`url`这样点击查看时就会根据原图的地址进行加载原图。 

# 3. 兼容性

- Chrome (最新)
- Firefox (最新)
- Safari (最新)
- Opera (最新)
- Edge (最新)
- Internet Explorer 9+

# 4. 键盘支持

- Esc：退出全屏、关闭查看器、退出模式、停止播放。
- Space：停止播放。
- ←：查看上一张图像。
- →：查看下一张图像。
- ↑：放大图像。
- ↓：缩小图像。
- Ctrl + 0：缩小到初始大小。
- Ctrl + 1：放大至自然大小。

# 5. 配置参数

配置参数就不详细讲解了，需要用到什么再去找寻就好了，也可以参考下面这篇文章：

https://segmentfault.com/a/1190000016584946

# 6. 最终效果

![img](/images/v2-92538727955d76a616a4d07cde8bb176_720w.gif)

# 7. 总结

Viewer.js非常非常的简单易用，简直不需要过多的配置就能集成一个拥有非常多功能的图片查看器，最关键的是该图片查看器还支持移动端，如果是自己想要写出一个这样的图片查看器，还是需要花费大量功夫。
