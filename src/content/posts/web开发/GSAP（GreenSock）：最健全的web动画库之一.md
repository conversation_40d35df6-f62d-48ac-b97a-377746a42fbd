---
title: "GSAP（GreenSock）：最健全的web动画库之一"
date: 2020-06-03
categories:
  - "web开发"
tags:
  - "前端开发"
  - "HTML5"
  - "动画"
---


# 官方的介绍

> GreenSock动画平台（GSAP）可以对JavaScript可以操作的所有内容进行动画处理（CSS属性，SVG，React，画布，通用对象等），同时解决了不同浏览器上存在的兼容性问题，而且速度极快（比jQuery 快20倍）。大约有1000万个站点和许多主要品牌都使用 GSAP。

# 官网

https://greensock.com/

动画其实是每秒多次改变元素属性值，元素看起来就仿佛在动一样，比如淡入淡出，旋转，移动等。而GSAP捕捉一个起始值和一个结束值，然后每秒在它们之间插值60次。

如果从技术上面来讲，GSAP其实应该被称为“GreenSock属性操纵器”（GSPM）。

# 可以添加动画的元素

GSAP非常的灵活，几乎可以处理所有页面上可以通过JS进行改变的元素，例如：

- CSS：2D和3D变换，颜色，`width`，`opacity`，`border-radius`，`margin`，和几乎所有CSS值。
- SVG属性：`viewBox`，`width`，`height`，`fill`，`stroke`，`cx`，`r`，`opacity`，等插件，像MorphSVG和DrawSVG可用于高级特效。
- 任何数值，例如，呈现为的对象`<canvas>`。对3D场景中的相机位置进行动画处理或过滤值。GSAP通常与Three.js和Pixi.js一起使用。

# 安装

## 下载直接引入

[greensock Version 3.3.0greensock.com](https://link.zhihu.com/?target=https%3A//greensock.com/files/file/20-gsap-3-public-files/%3Fdo%3Ddownload%26csrfKey%3D15944fce35b951ce3142b11661536cdd)

## CDN

官网上提供的CDN非常的多，可以按照自己的需要进行引入。

```html
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/CSSRulePlugin.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/Draggable.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/EaselPlugin.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/MotionPathPlugin.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/PixiPlugin.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/TextPlugin.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/ScrollToPlugin.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.3.0/ScrollTrigger.min.js"></script>
```

NPM

```shell
npm install gsap

yarn add gsap
```

# 核心模块

## **使用**

`gsap.to(targets,vars)`：从开始的位置到结束的位置。

- `targets` - 你需要添加动画的对象，可以是`object`,`array`和选择器`".myClass"`。
- `vars` - 一个对象，里面包含你想要改变的属性，延时，已经回调函数等。

[vars所有的参数](https://greensock.com/docs/v3/GSAP/Tween/vars)

`gsap.from(targets,vars)`：与上面的`gsap.to`相反，这个是从结束的位置到开始的位置。

`Easing`：运动状态。属于`vars`中的一个属性。

```js
var tween = gsap.from("#app", {
    duration: 5,
    x: 500,
    ease:"elastic.in(1,1)" // 运动状态
});
Draggable.create("#app");
```

https://greensock.com/docs/v3/Eases

官网上面对于ease属性给出了很多值，如果有需要的话可以参考官网的属性。

![img](/images/web/GSAP（GreenSock）：最健全的web动画库之一/v2-6adaec0fbbd60764e60aa2d389a1a829_b.webp)

只需要点一点，就直观的体验

# 回调函数

- **onComplete**：动画完成时调用。
- **onStart**：动画开始时调用
- **onUpdate**：每次动画更新时调用（在动画处于活动状态时每帧调用）。
- **onRepeat**：每次动画重复时调用一次。
- **onReverseComplete**：动画反转后再次到达其起点时调用。

```js
var tween = gsap.from("#app", {
    duration: 5,
    x: 500,
    ease:"elastic.in(1,1)",
    onComplete:function () { // 动画播放完成时调用
        console.log("111");
    }
});
```

回调参数

```js
var tween = gsap.to("#app", {
    duration: 1,
    x: 100,
    onComplete: tweenComplete,
    onCompleteParams: ["done!"]
});

function tweenComplete(message) {
    console.log(message);
}
```

使用上面这种方式调用回调函数时，**如果需要传递参数，必须以数组方式传递，即使只有一个参数。**

# 控制动画

- `tween.pause();` 暂停
- `tween.resume(); `恢复
- `tween.reverse(); `反向播放
- `tween.seek(0.5);` 跳到0.5s
- `tween.progress(0.25);` 跳到4分之1处
- `tween.timeScale(0.5);` 速度减慢
- `tween.timeScale(2); `速度翻倍
- `tween.kill();` 删除动画

# Timeline

- 从整体上控制一组动画。
- 在不使用很多`delay`的情况下构建一个动画序列。（如果对前一个动画进行时间调整后一个动画的触发时间也会改变，从而大大简化了实验和维护工作）。
- 对动画进行模块化。
- 可以进行非常复杂的动画编排。
- 要基于一组动画触发回调（例如“在完成所有这些动画之后，调用`myFunction()`”）。

生命时间线变量`var t1 = gsap.timeline();`

然后将需要依次触发的动画添加入时间线里即可，例如：

```js
var tl = gsap.timeline();
tl.add(gsap.to("#app", {
    duration: 1,
    delay: 1,
    x: 500,
}));
tl.to("#app", {
    duration: 1,
    y: 500,
});
```

![img](/images/web/GSAP（GreenSock）：最健全的web动画库之一/v2-c2ee2519115a9bceb5623f5b3674fad4_b.webp)

时间轴的特殊属性：

- `repeat`：动画重复的次数。
- `repeatDelay`：两次重复之间的间隔时间（以秒为单位）。
- `yoyo`：如果为`true`，则每次重复播放都会前后交替进行。
- `delay`：时间轴开始之前的延迟（以秒为单位）。
- `onComplete`：时间线播放完毕后调用的函数。

```js
var tl = gsap.timeline({
  repeat: 1, 
  yoyo: true, 
  onRepeat: onRepeatHandler,
  onComplete: onCompleteHandler
});
```

**Getter / Setter 方法**

- `time()` 播放头的本地位置（当前时间，以秒为单位），不包括任何重复或repeatDelays。
- `progress()` 它是介于0和1之间的值，指示播放头的位置，其中0处在开始位置，0.5处在中途完成，1处在结束位置。
- `duration()` 动画的持续时间（以秒为单位），不包括任何重复或repeatDelays。
- `delay()` 动画的初始延迟（动画开始之前的时间长度，以秒为单位）。

# 总结

GSAP的功能可远远不止上面这些，它几乎可以做到你想要的任何动画，包括DOM的拖动，滚动触发，运动路径插件等等，唯一的缺点就是它包含可以实现的动画实在是太多了，需要花费一定的时间进行学习，不像`Animate.css`和`Anime.js`这些动画库一样简单易用，不过如果你的网页需要大量动画，`GSAP`不失为一个很好的选择。
