---
title: "学习前端如何将自己的页面部署到服务器"
date: 2020-07-03
categories:
  - "web开发"
tags:
  - "域名"
  - "GitHub Pages"
  - "个人博客"
---


本篇文章的视频地址：

<div style="position: relative; padding: 35% 45%;">
<iframe style="position: absolute; width: 100%; height: 100%; left: 0; top: 0;"  src="//player.bilibili.com/player.html?aid=926161323&bvid=BV1HT4y1774Y&cid=208735231&page=1" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"> ></iframe>
</div>

# 前言

相信前端的初学者在学习到一定的地步时，都会想将自己做的网页向人家进行展示，但是如何进行展示呢？是直接将网页的静态文件发送给别人？还是说给他地址，让他直接在浏览器上面进行查看。

当然是直接通过域名访问你做的网页更加的人性化！因为你就算给他静态文件，他也不一定会使用。

那么如何将自己的网页部署上线呢？

## GitHub Pages

这种方法无成本，只需要注册一个GitHub账号，具体可以看这篇文章：

[部署到Github-Pages上的博客，自定义域名，和免费域名如何申请](/2020/05/04/部署到github-pages上的博客自定义域名和免费域名如何申请/)

<br/>当然，这种方式虽然免费，但是有一个严重的缺陷，就是**国内的访问速度有时候会非常慢，图片会有一个很长的加载时间，或者打开网页的响应时间会很长。**

## 虚拟服务器

> 虚拟专用服务器，是将一台服务器分割成多个虚拟专用服务器的服务。实现VPS的技术分为容器技术和虚拟机技术。在容器或虚拟机中，每个VPS都可分配独立公网IP地址、独立操作系统、实现不同VPS间磁盘空间、内存、CPU资源、进程和系统配置的隔离，为用户和应用程序模拟出“独占”使用计算资源的体验。

国内有腾讯云，阿里云，京东云等云服务器，但是国内的服务器如果要解析域名就必须进行备案。如果不进行备案，你通过服务器的公网IP也是能够访问到该服务器的，但是公网IP不便于记忆的同时，还显得跟**暗网**一样，让人家不敢打开。

# 阿里云

因为我有一台阿里云服务器，也曾经备案过，简单的说就是阿里云在备案时需要填写相关的资料并且提交后，如果不合格，会有客服打电话过来告诉你哪儿不合格，然后你按照他所描述的地方进行修改后再提交。

如果第一关客服关过了后，就需要等待相关部门的审核，当时我的服务器审核了大概1个多星期。

# 购买VPS

如果购买的是国外的VPS，那么可以直接进行域名解析，是不用备案的，同时`VPS`的系统推荐使用`centos`。

但是国外的VPS可能存在连接**不稳定，丢包，甚至无法进行访问的情况**，而国内的VPS**访问速度还是有保障的，基本可以跑满VPS的带宽**。

所以是选择国内的VPS还是选择国外的VPS，还是根据自己的情况进行判断。

# 连接VPS

那么服务器有了，我们就需要连接VPS的工具，这里推荐使用`FinalShell`，`Xshell`两种工具。

[FinalShell下载](https://www.hostbuf.com/t/988.html)

[Xshell下载](https://www.netsarang.com/zh/xshell-download/)

# 安装宝塔面板

首先我们需要通过`FinalShell`工具连接一下我们的VPS。

![image-20200704110550648](/images/image-20200704110550648.png)

然后，安装一下宝塔面板：[宝塔面板的安装方法](https://bt.cn/bbs/thread-19376-1-1.html)。

宝塔面板是非常好用的一个面板，可以大大简化我们部署个人博客的难度。根据文档中的提示，一步步进行安装，当安装结束后会弹出链接，用户名，密码。

![image-20200704103025773](/images/image-20200704103025773.png)

最后通过浏览器打开面板地址。

# 域名

域名可以选择免费的域名，至于免费的域名如何申请，在[部署到Github-Pages上的博客，自定义域名，和免费域名如何申请](/2020/05/04/部署到github-pages上的博客自定义域名和免费域名如何申请/)中有提到。

# 部署网页

在宝塔面板中`网站`-`添加网站`。

![image-20200704105145466](/images/image-20200704105145466.png)

填写进自己的域名。提交后访问一下域名，如果出现：

![image-20200704105003618](/images/image-20200704105003618.png)

上面这个界面，说明域名解析已经成功，并且可以开始上传自己的网页。

![image-20200704105242692](/images/image-20200704105242692.png)

通过Finalshell工具，来到添加站点中的根目录，将自己的网页放到这个目录就大功告成了。

