---
title: "用CSS做出漂亮的字体动画"
date: 2021-1-31 12:26:59
categories:
  - "web开发"
tags:
  - "CSS"
  - "动画"
---


SVG在我们做网页的过程中会大量的进行使用，但是给SVG添加动画就不是那么经常会去做的一件事，本篇文章就来讲解如何用SVG制作炫酷的文字动画。

首先我们来看一下最终效果：

![animate](/images/web/用CSS做出漂亮的字体动画/animate.gif)

在制作该动画的时候，需要使用到SVG的下面两个属性：

- stroke-dasharray：控制用来描边的点划线的图案范式。
- stroke-dashoffset：指定了dash模式到路径开始的距离。

# 1. 制作SVG字体

制作SVG字体的办法有很多，我这里选择了Adobe XD进行制作。

至于Adobe XD具体是怎么使用的，因为我也不熟悉，所以这里就不进行过多的讲解了...

![create_svg](/images/web/用CSS做出漂亮的字体动画/create_svg.gif)

将字体样式设置好后，需要点击**转换为路径**，然后点击**复制SVG代码**。

粘贴到HTML项目中，就类似于下面这个样子。

```html
<svg height="138.541" id="logo" viewBox="0 0 1011.042 138.541" width="1011.042" xmlns="http://www.w3.org/2000/svg">
    <g transform="translate(-585.687 -259.098)">
        <path d="M113.15-12.41q.438-2.628.657-5.548t.219-5.4l18.834,5.694q0,1.168-.146,2.92t-.365,3.577q-.219,1.825-.438,3.5t-.365,2.7a22.432,22.432,0,0,1-2.7,8.4,14.908,14.908,0,0,1-5.329,5.256,24.855,24.855,0,0,1-8.176,2.847A85.881,85.881,0,0,1,103.952,12.7q-15.038.73-30.076,0a42.773,42.773,0,0,1-9.49-1.168,14.471,14.471,0,0,1-6.424-3.358,14.382,14.382,0,0,1-3.723-6.059A30.238,30.238,0,0,1,53-7.3V-57.086a27.11,27.11,0,0,1-2.7,2.117q-1.387.949-2.847,2.117l-5.4-7.738L34.748-47.6Q32.12-49.932,28.4-52.852t-7.592-5.767q-3.869-2.847-7.446-5.256T7.446-67.744L16.79-82.636q3.942,2.482,10,6.5t11.461,7.957a141.6,141.6,0,0,0,11.607-10A165.948,165.948,0,0,0,61.539-90.52a166.854,166.854,0,0,0,10.512-13.651,114.5,114.5,0,0,0,8.249-13.8H94.17a68.743,68.743,0,0,0,8.1,13.87A147.629,147.629,0,0,0,113.661-90.52a139.957,139.957,0,0,0,13.067,12.191,138.171,138.171,0,0,0,13.286,9.709L128.188-53.874q-3.066-2.19-6.059-4.453t-5.913-4.6q0,3.358-.073,7.519T116-47.45q-.073,3.8-.146,6.643t-.073,3.723a28.977,28.977,0,0,1-1.022,8.322,11.393,11.393,0,0,1-3.358,5.4,15.841,15.841,0,0,1-6.132,3.139,55.324,55.324,0,0,1-9.49,1.679l-8.614.876-5.11-16.206,8.614-.876q2.628-.292,4.161-.584a4.667,4.667,0,0,0,2.263-.949,3.5,3.5,0,0,0,1.022-1.752,17.867,17.867,0,0,0,.438-3.139q.292-2.628.438-5.767t.292-6.351H71.1v40.88A29.529,29.529,0,0,0,71.321-8.4a4,4,0,0,0,1.022,2.336,4.861,4.861,0,0,0,2.409,1.168,27.142,27.142,0,0,0,4.38.511,180.118,180.118,0,0,0,22.922,0q2.92-.146,4.818-.438a8.82,8.82,0,0,0,3.139-1.022A5.038,5.038,0,0,0,111.982-8.1,16.234,16.234,0,0,0,113.15-12.41ZM42.194-80.446q-2.482-2.336-5.767-5.329t-6.789-5.986q-3.5-2.993-6.935-5.694t-6.059-4.453L26.718-116.07q2.482,1.606,5.913,4.161t7.008,5.4q3.577,2.847,6.789,5.621t5.4,4.818ZM87.6-94.9Q81.322-83.366,67.89-70.226h40.15A137.018,137.018,0,0,1,96-82.928,83.523,83.523,0,0,1,87.6-94.9ZM38.836-38.4l7.738,3.65q-.438,1.314-1.9,5.183t-3.5,8.906q-2.044,5.037-4.38,10.585T32.412.365q-2.044,4.891-3.577,8.4t-1.971,4.234L9.928,4.964Q10.512,3.8,12.264.073t3.942-8.614q2.19-4.891,4.6-10.366t4.526-10.366q2.117-4.891,3.5-8.468t1.679-4.745Z"
              stroke="#fff" stroke-width="4" transform="translate(581 380)"/>
        <path d="M113.15-12.41q.438-2.628.657-5.548t.219-5.4l18.834,5.694q0,1.168-.146,2.92t-.365,3.577q-.219,1.825-.438,3.5t-.365,2.7a22.432,22.432,0,0,1-2.7,8.4,14.908,14.908,0,0,1-5.329,5.256,24.855,24.855,0,0,1-8.176,2.847A85.881,85.881,0,0,1,103.952,12.7q-15.038.73-30.076,0a42.773,42.773,0,0,1-9.49-1.168,14.471,14.471,0,0,1-6.424-3.358,14.382,14.382,0,0,1-3.723-6.059A30.238,30.238,0,0,1,53-7.3V-57.086a27.11,27.11,0,0,1-2.7,2.117q-1.387.949-2.847,2.117l-5.4-7.738L34.748-47.6Q32.12-49.932,28.4-52.852t-7.592-5.767q-3.869-2.847-7.446-5.256T7.446-67.744L16.79-82.636q3.942,2.482,10,6.5t11.461,7.957a141.6,141.6,0,0,0,11.607-10A165.948,165.948,0,0,0,61.539-90.52a166.854,166.854,0,0,0,10.512-13.651,114.5,114.5,0,0,0,8.249-13.8H94.17a68.743,68.743,0,0,0,8.1,13.87A147.629,147.629,0,0,0,113.661-90.52a139.957,139.957,0,0,0,13.067,12.191,138.171,138.171,0,0,0,13.286,9.709L128.188-53.874q-3.066-2.19-6.059-4.453t-5.913-4.6q0,3.358-.073,7.519T116-47.45q-.073,3.8-.146,6.643t-.073,3.723a28.977,28.977,0,0,1-1.022,8.322,11.393,11.393,0,0,1-3.358,5.4,15.841,15.841,0,0,1-6.132,3.139,55.324,55.324,0,0,1-9.49,1.679l-8.614.876-5.11-16.206,8.614-.876q2.628-.292,4.161-.584a4.667,4.667,0,0,0,2.263-.949,3.5,3.5,0,0,0,1.022-1.752,17.867,17.867,0,0,0,.438-3.139q.292-2.628.438-5.767t.292-6.351H71.1v40.88A29.529,29.529,0,0,0,71.321-8.4a4,4,0,0,0,1.022,2.336,4.861,4.861,0,0,0,2.409,1.168,27.142,27.142,0,0,0,4.38.511,180.118,180.118,0,0,0,22.922,0q2.92-.146,4.818-.438a8.82,8.82,0,0,0,3.139-1.022A5.038,5.038,0,0,0,111.982-8.1,16.234,16.234,0,0,0,113.15-12.41ZM42.194-80.446q-2.482-2.336-5.767-5.329t-6.789-5.986q-3.5-2.993-6.935-5.694t-6.059-4.453L26.718-116.07q2.482,1.606,5.913,4.161t7.008,5.4q3.577,2.847,6.789,5.621t5.4,4.818ZM87.6-94.9Q81.322-83.366,67.89-70.226h40.15A137.018,137.018,0,0,1,96-82.928,83.523,83.523,0,0,1,87.6-94.9ZM38.836-38.4l7.738,3.65q-.438,1.314-1.9,5.183t-3.5,8.906q-2.044,5.037-4.38,10.585T32.412.365q-2.044,4.891-3.577,8.4t-1.971,4.234L9.928,4.964Q10.512,3.8,12.264.073t3.942-8.614q2.19-4.891,4.6-10.366t4.526-10.366q2.117-4.891,3.5-8.468t1.679-4.745Z"
              stroke="#fff" stroke-width="4" transform="translate(727 380)"/>
        <path d="M132.276-87.454H43.946v-16.79h34.31l-2.774-11.534,19.126-1.314,2.628,12.848h35.04Zm-106,20.732q-1.168-3.5-3.358-8.541t-4.6-10.366q-2.409-5.329-4.745-10.147t-3.8-7.738l15.622-8.03q1.9,3.5,4.234,8.176t4.672,9.636q2.336,4.964,4.526,9.782t3.8,8.614ZM97.674-32.85V-5.11a48.72,48.72,0,0,1-.657,8.76,11.467,11.467,0,0,1-2.628,5.694,11.981,11.981,0,0,1-5.548,3.285,49.982,49.982,0,0,1-9.271,1.679L69.934,15.33,64.678-1.606l9.344-.876q3.358-.292,4.526-1.825A7.25,7.25,0,0,0,79.716-8.76V-32.85H48.034V-79.862h77.818V-32.85ZM65.846-49.932H108.04V-62.78H65.846Zm-22.338.876q-2.774,8.614-5.256,15.549T33.215-20.075q-2.555,6.5-5.4,13.14T21.462,7.592L4.526.438q7.154-14.892,12.7-28.4t9.928-27.667Zm27.3,26.718q-2.336,4.38-5.256,9.052T59.422-4.015Q56.21.584,52.779,4.891t-6.789,8.1L31.828,1.168q3.066-3.5,6.5-7.519t6.643-8.395q3.212-4.38,6.132-8.833a98,98,0,0,0,5.11-8.687Zm45.114-9.636q2.628,2.92,6.059,6.935t6.789,8.4q3.358,4.38,6.57,8.687t5.4,7.811L127.02,11.972q-2.19-3.5-5.4-7.884t-6.643-8.906q-3.431-4.526-6.789-8.833t-6.132-7.519Z"
              stroke="#fff" stroke-width="4" transform="translate(873 380)"/>
        <path d="M132.276-87.454H43.946v-16.79h34.31l-2.774-11.534,19.126-1.314,2.628,12.848h35.04Zm-106,20.732q-1.168-3.5-3.358-8.541t-4.6-10.366q-2.409-5.329-4.745-10.147t-3.8-7.738l15.622-8.03q1.9,3.5,4.234,8.176t4.672,9.636q2.336,4.964,4.526,9.782t3.8,8.614ZM97.674-32.85V-5.11a48.72,48.72,0,0,1-.657,8.76,11.467,11.467,0,0,1-2.628,5.694,11.981,11.981,0,0,1-5.548,3.285,49.982,49.982,0,0,1-9.271,1.679L69.934,15.33,64.678-1.606l9.344-.876q3.358-.292,4.526-1.825A7.25,7.25,0,0,0,79.716-8.76V-32.85H48.034V-79.862h77.818V-32.85ZM65.846-49.932H108.04V-62.78H65.846Zm-22.338.876q-2.774,8.614-5.256,15.549T33.215-20.075q-2.555,6.5-5.4,13.14T21.462,7.592L4.526.438q7.154-14.892,12.7-28.4t9.928-27.667Zm27.3,26.718q-2.336,4.38-5.256,9.052T59.422-4.015Q56.21.584,52.779,4.891t-6.789,8.1L31.828,1.168q3.066-3.5,6.5-7.519t6.643-8.395q3.212-4.38,6.132-8.833a98,98,0,0,0,5.11-8.687Zm45.114-9.636q2.628,2.92,6.059,6.935t6.789,8.4q3.358,4.38,6.57,8.687t5.4,7.811L127.02,11.972q-2.19-3.5-5.4-7.884t-6.643-8.906q-3.431-4.526-6.789-8.833t-6.132-7.519Z"
              stroke="#fff" stroke-width="4" transform="translate(1019 380)"/>
        <path d="M132.276-97.82q-.584,19.71-1.168,37.668-.292,7.738-.584,15.768t-.511,15.549q-.219,7.519-.511,14.089t-.584,11.388a25.83,25.83,0,0,1-1.46,7.665A11.625,11.625,0,0,1,124.1,9.125a13.359,13.359,0,0,1-5.4,2.628,53.81,53.81,0,0,1-7.446,1.241L95.63,14.454,89.936-4.672l15.038-1.46a13.47,13.47,0,0,0,2.774-.511,3.744,3.744,0,0,0,1.9-1.314,6.582,6.582,0,0,0,1.1-2.628,29.714,29.714,0,0,0,.511-4.6q.438-6.716.73-15.038t.584-17.009q.292-8.687.438-17.155t.292-15.33H88.476q-2.044,4.672-4.307,9.052t-4.6,8.322L68.036-68.328V9.782l-54.458.146V-97.82H27.3l6.132-19.71,18.834,1.9L46.282-97.82H68.036v18.4Q73-89.5,77.234-100.156A186.973,186.973,0,0,0,83.658-118.7l18.25,2.336q-1.314,4.38-2.847,9.052t-3.285,9.49ZM50.954-80.008h-20v25.7h20ZM94.316-25.7A178.866,178.866,0,0,0,84.68-41.464Q79.57-48.91,74.9-55.042l13.286-10.8Q92.418-60.3,97.966-53A141.5,141.5,0,0,1,108.04-37.814ZM50.954-8.468v-27.74h-20v27.74Z"
              stroke="#fff" stroke-width="4" transform="translate(1165 380)"/>
        <path d="M135.634-14.016H118.552v11.1a33.6,33.6,0,0,1-.8,8.1,10.92,10.92,0,0,1-2.555,4.964,10.507,10.507,0,0,1-4.672,2.7,39.4,39.4,0,0,1-7.154,1.314L91.4,15.476,86.14-.146l8.03-.73q5.11-.438,6.643-1.9t1.533-5.256v-5.986H71.686q2.92,2.336,5.767,4.672t5.183,4.526L73.73,7.008q-1.752-1.752-4.088-3.942t-4.891-4.38Q62.2-3.5,59.714-5.548t-4.38-3.358l4.38-5.11H42.048V-28.47h60.3v-5.256l9.052.292v-3.65H95.63v4.38H80.592v-4.38H64.532v4.526H48.618v-52.56H80.592v-5.84H47.158v-13.578H80.592V-116.07l15.038.73v10.8h12.264l-3.65-10.22,14.016-2.19q1.168,3.358,2.19,6.424t2.044,5.986h9.782v13.578H95.63v5.84h32.12v52.56h-9.2v4.088h17.082ZM36.354-66.138V14.6H20.294V-66.138H8.468v-18.25H20.294v-30.66l16.06.584v30.076h8.614v18.25Zm28.178-6.424v5.548h16.06v-5.548Zm31.1,0v5.548H111.4v-5.548ZM80.592-49.494V-54.75H64.532v5.256Zm30.806,0V-54.75H95.63v5.256Z"
              stroke="#fff" stroke-width="4" transform="translate(1311 380)"/>
        <path d="M133.152-26.718q-4.234-.73-8.395-1.387t-8.1-1.387v40.88H27.886v-40q-4.38,1.022-8.833,1.825T9.928-25.258l-4.964-16.5a251.513,251.513,0,0,0,25.769-4.307q12.045-2.7,22.557-6.059a128.412,128.412,0,0,1-13.14-8.76q-8.322,6.424-16.5,11.68L13.578-62.488q4.38-2.482,9.2-5.767t9.49-7.154q4.672-3.869,9.052-8.176a75.6,75.6,0,0,0,7.738-8.833H31.828v13.87h-18.4v-29.2H61.174l-1.9-9.928L78.84-118.7l1.9,10.95h50.516v29.2H115.048v10.512h.146a71.4,71.4,0,0,1-9.636,7.446Q99.426-56.5,91.1-51.83a177.927,177.927,0,0,0,21.462,5.037q11.388,1.971,24.674,3.139Zm-78.84-65.7,11.1,5.986q-.73,1.022-1.46,1.9t-1.46,1.9h50.37v-9.782ZM56.94-68.036a113.566,113.566,0,0,0,15.038,8.76,131.733,131.733,0,0,0,16.5-8.76ZM104.974-32.12q-9.052-2.19-17.228-4.891T72.124-42.778Q65.116-39.858,57.6-37.23T41.61-32.12ZM98.842-16.352h-53V-4.38h53Z"
              stroke="#fff" stroke-width="4" transform="translate(1457 380)"/>
    </g>
</svg>
```

# 2. 测量路径长度

```js
// 获取所有path
const path = document.querySelectorAll("path");
path.forEach(item => {
  // 获取路径的长度
  console.log(item.getTotalLength());
});
```

上面这一步是为了获取SVG的路径长度，因为每一个字是由一个一个的path组成，所以它们存在着长度，通过这些长度我们就可以开始进行动画制作。

# 3. 制作动画

首先通过`:nth-child`后代选择器选择每一个path元素：

```css
#logo path:nth-child(1) {
}
```

有几个就要选择几个。

然后填上最开始说的那两个SVG属性：`stroke-dasharray`、`stroke-dashoffset`，后面的数字是路径长度，值为第二步用JavaScript获取到的值。

```css
#logo path:nth-child(1) {
    stroke-dasharray: 1066;
    stroke-dashoffset: 1066;
}
```

接着挨个将对应的值进行填入。

```css
#logo path:nth-child(1) {
    stroke-dasharray: 1066;
    stroke-dashoffset: 1066;
}

#logo path:nth-child(2) {
    stroke-dasharray: 1066;
    stroke-dashoffset: 1066;
}

#logo path:nth-child(3) {
    stroke-dasharray: 1219;
    stroke-dashoffset: 1219;
}

#logo path:nth-child(4) {
    stroke-dasharray: 1219;
    stroke-dashoffset: 1219;
}

#logo path:nth-child(5) {
    stroke-dasharray: 1046;
    stroke-dashoffset: 1046;
}

#logo path:nth-child(6) {
    stroke-dasharray: 1343;
    stroke-dashoffset: 1343;
}

#logo path:nth-child(7) {
    stroke-dasharray: 1202;
    stroke-dashoffset: 1202;
}
```

接下来就是创建两个动画：

```css
/* 将文字逐步进行显示 */
@keyframes line-anim {
    to {
        stroke-dashoffset: 0;
    }
}

/* 将文字填充颜色改为白色 */
@keyframes fill {
    to {
        fill: white;
    }
}
```

到了这一步几乎就已经全部完成了。

完整代码：

```html
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="ie=edge" http-equiv="X-UA-Compatible">
    <title>SVG文字动画</title>
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background-color: rgb(32, 35, 48);
        }

        #logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            fill: transparent;
            animation: fill 2s ease forwards 2.5s;
        }


        #logo path:nth-child(1) {
            stroke-dasharray: 1066;
            stroke-dashoffset: 1066;
            animation: line-anim 2s ease forwards;
        }

        #logo path:nth-child(2) {
            stroke-dasharray: 1066;
            stroke-dashoffset: 1066;
            animation: line-anim 2s ease forwards 0.3s;
        }

        #logo path:nth-child(3) {
            stroke-dasharray: 1219;
            stroke-dashoffset: 1219;
            animation: line-anim 2s ease forwards 0.6s;
        }

        #logo path:nth-child(4) {
            stroke-dasharray: 1219;
            stroke-dashoffset: 1219;
            animation: line-anim 2s ease forwards 0.9s;
        }

        #logo path:nth-child(5) {
            stroke-dasharray: 1046;
            stroke-dashoffset: 1046;
            animation: line-anim 2s ease forwards 1.2s;
        }

        #logo path:nth-child(6) {
            stroke-dasharray: 1343;
            stroke-dashoffset: 1343;
            animation: line-anim 2s ease forwards 1.5s;
        }

        #logo path:nth-child(7) {
            stroke-dasharray: 1202;
            stroke-dashoffset: 1202;
            animation: line-anim 2s ease forwards 1.8s;
        }

        /* 将文字逐步进行显示 */
        @keyframes line-anim {
            to {
                stroke-dashoffset: 0;
            }
        }

        /* 将文字填充颜色改为白色 */
        @keyframes fill {
            to {
                fill: white;
            }
        }
    </style>
</head>
<body>
<svg height="138.541" id="logo" viewBox="0 0 1011.042 138.541" width="1011.042" xmlns="http://www.w3.org/2000/svg">
    <g transform="translate(-585.687 -259.098)">
        <path d="M113.15-12.41q.438-2.628.657-5.548t.219-5.4l18.834,5.694q0,1.168-.146,2.92t-.365,3.577q-.219,1.825-.438,3.5t-.365,2.7a22.432,22.432,0,0,1-2.7,8.4,14.908,14.908,0,0,1-5.329,5.256,24.855,24.855,0,0,1-8.176,2.847A85.881,85.881,0,0,1,103.952,12.7q-15.038.73-30.076,0a42.773,42.773,0,0,1-9.49-1.168,14.471,14.471,0,0,1-6.424-3.358,14.382,14.382,0,0,1-3.723-6.059A30.238,30.238,0,0,1,53-7.3V-57.086a27.11,27.11,0,0,1-2.7,2.117q-1.387.949-2.847,2.117l-5.4-7.738L34.748-47.6Q32.12-49.932,28.4-52.852t-7.592-5.767q-3.869-2.847-7.446-5.256T7.446-67.744L16.79-82.636q3.942,2.482,10,6.5t11.461,7.957a141.6,141.6,0,0,0,11.607-10A165.948,165.948,0,0,0,61.539-90.52a166.854,166.854,0,0,0,10.512-13.651,114.5,114.5,0,0,0,8.249-13.8H94.17a68.743,68.743,0,0,0,8.1,13.87A147.629,147.629,0,0,0,113.661-90.52a139.957,139.957,0,0,0,13.067,12.191,138.171,138.171,0,0,0,13.286,9.709L128.188-53.874q-3.066-2.19-6.059-4.453t-5.913-4.6q0,3.358-.073,7.519T116-47.45q-.073,3.8-.146,6.643t-.073,3.723a28.977,28.977,0,0,1-1.022,8.322,11.393,11.393,0,0,1-3.358,5.4,15.841,15.841,0,0,1-6.132,3.139,55.324,55.324,0,0,1-9.49,1.679l-8.614.876-5.11-16.206,8.614-.876q2.628-.292,4.161-.584a4.667,4.667,0,0,0,2.263-.949,3.5,3.5,0,0,0,1.022-1.752,17.867,17.867,0,0,0,.438-3.139q.292-2.628.438-5.767t.292-6.351H71.1v40.88A29.529,29.529,0,0,0,71.321-8.4a4,4,0,0,0,1.022,2.336,4.861,4.861,0,0,0,2.409,1.168,27.142,27.142,0,0,0,4.38.511,180.118,180.118,0,0,0,22.922,0q2.92-.146,4.818-.438a8.82,8.82,0,0,0,3.139-1.022A5.038,5.038,0,0,0,111.982-8.1,16.234,16.234,0,0,0,113.15-12.41ZM42.194-80.446q-2.482-2.336-5.767-5.329t-6.789-5.986q-3.5-2.993-6.935-5.694t-6.059-4.453L26.718-116.07q2.482,1.606,5.913,4.161t7.008,5.4q3.577,2.847,6.789,5.621t5.4,4.818ZM87.6-94.9Q81.322-83.366,67.89-70.226h40.15A137.018,137.018,0,0,1,96-82.928,83.523,83.523,0,0,1,87.6-94.9ZM38.836-38.4l7.738,3.65q-.438,1.314-1.9,5.183t-3.5,8.906q-2.044,5.037-4.38,10.585T32.412.365q-2.044,4.891-3.577,8.4t-1.971,4.234L9.928,4.964Q10.512,3.8,12.264.073t3.942-8.614q2.19-4.891,4.6-10.366t4.526-10.366q2.117-4.891,3.5-8.468t1.679-4.745Z"
              stroke="#fff" stroke-width="4" transform="translate(581 380)"/>
        <path d="M113.15-12.41q.438-2.628.657-5.548t.219-5.4l18.834,5.694q0,1.168-.146,2.92t-.365,3.577q-.219,1.825-.438,3.5t-.365,2.7a22.432,22.432,0,0,1-2.7,8.4,14.908,14.908,0,0,1-5.329,5.256,24.855,24.855,0,0,1-8.176,2.847A85.881,85.881,0,0,1,103.952,12.7q-15.038.73-30.076,0a42.773,42.773,0,0,1-9.49-1.168,14.471,14.471,0,0,1-6.424-3.358,14.382,14.382,0,0,1-3.723-6.059A30.238,30.238,0,0,1,53-7.3V-57.086a27.11,27.11,0,0,1-2.7,2.117q-1.387.949-2.847,2.117l-5.4-7.738L34.748-47.6Q32.12-49.932,28.4-52.852t-7.592-5.767q-3.869-2.847-7.446-5.256T7.446-67.744L16.79-82.636q3.942,2.482,10,6.5t11.461,7.957a141.6,141.6,0,0,0,11.607-10A165.948,165.948,0,0,0,61.539-90.52a166.854,166.854,0,0,0,10.512-13.651,114.5,114.5,0,0,0,8.249-13.8H94.17a68.743,68.743,0,0,0,8.1,13.87A147.629,147.629,0,0,0,113.661-90.52a139.957,139.957,0,0,0,13.067,12.191,138.171,138.171,0,0,0,13.286,9.709L128.188-53.874q-3.066-2.19-6.059-4.453t-5.913-4.6q0,3.358-.073,7.519T116-47.45q-.073,3.8-.146,6.643t-.073,3.723a28.977,28.977,0,0,1-1.022,8.322,11.393,11.393,0,0,1-3.358,5.4,15.841,15.841,0,0,1-6.132,3.139,55.324,55.324,0,0,1-9.49,1.679l-8.614.876-5.11-16.206,8.614-.876q2.628-.292,4.161-.584a4.667,4.667,0,0,0,2.263-.949,3.5,3.5,0,0,0,1.022-1.752,17.867,17.867,0,0,0,.438-3.139q.292-2.628.438-5.767t.292-6.351H71.1v40.88A29.529,29.529,0,0,0,71.321-8.4a4,4,0,0,0,1.022,2.336,4.861,4.861,0,0,0,2.409,1.168,27.142,27.142,0,0,0,4.38.511,180.118,180.118,0,0,0,22.922,0q2.92-.146,4.818-.438a8.82,8.82,0,0,0,3.139-1.022A5.038,5.038,0,0,0,111.982-8.1,16.234,16.234,0,0,0,113.15-12.41ZM42.194-80.446q-2.482-2.336-5.767-5.329t-6.789-5.986q-3.5-2.993-6.935-5.694t-6.059-4.453L26.718-116.07q2.482,1.606,5.913,4.161t7.008,5.4q3.577,2.847,6.789,5.621t5.4,4.818ZM87.6-94.9Q81.322-83.366,67.89-70.226h40.15A137.018,137.018,0,0,1,96-82.928,83.523,83.523,0,0,1,87.6-94.9ZM38.836-38.4l7.738,3.65q-.438,1.314-1.9,5.183t-3.5,8.906q-2.044,5.037-4.38,10.585T32.412.365q-2.044,4.891-3.577,8.4t-1.971,4.234L9.928,4.964Q10.512,3.8,12.264.073t3.942-8.614q2.19-4.891,4.6-10.366t4.526-10.366q2.117-4.891,3.5-8.468t1.679-4.745Z"
              stroke="#fff" stroke-width="4" transform="translate(727 380)"/>
        <path d="M132.276-87.454H43.946v-16.79h34.31l-2.774-11.534,19.126-1.314,2.628,12.848h35.04Zm-106,20.732q-1.168-3.5-3.358-8.541t-4.6-10.366q-2.409-5.329-4.745-10.147t-3.8-7.738l15.622-8.03q1.9,3.5,4.234,8.176t4.672,9.636q2.336,4.964,4.526,9.782t3.8,8.614ZM97.674-32.85V-5.11a48.72,48.72,0,0,1-.657,8.76,11.467,11.467,0,0,1-2.628,5.694,11.981,11.981,0,0,1-5.548,3.285,49.982,49.982,0,0,1-9.271,1.679L69.934,15.33,64.678-1.606l9.344-.876q3.358-.292,4.526-1.825A7.25,7.25,0,0,0,79.716-8.76V-32.85H48.034V-79.862h77.818V-32.85ZM65.846-49.932H108.04V-62.78H65.846Zm-22.338.876q-2.774,8.614-5.256,15.549T33.215-20.075q-2.555,6.5-5.4,13.14T21.462,7.592L4.526.438q7.154-14.892,12.7-28.4t9.928-27.667Zm27.3,26.718q-2.336,4.38-5.256,9.052T59.422-4.015Q56.21.584,52.779,4.891t-6.789,8.1L31.828,1.168q3.066-3.5,6.5-7.519t6.643-8.395q3.212-4.38,6.132-8.833a98,98,0,0,0,5.11-8.687Zm45.114-9.636q2.628,2.92,6.059,6.935t6.789,8.4q3.358,4.38,6.57,8.687t5.4,7.811L127.02,11.972q-2.19-3.5-5.4-7.884t-6.643-8.906q-3.431-4.526-6.789-8.833t-6.132-7.519Z"
              stroke="#fff" stroke-width="4" transform="translate(873 380)"/>
        <path d="M132.276-87.454H43.946v-16.79h34.31l-2.774-11.534,19.126-1.314,2.628,12.848h35.04Zm-106,20.732q-1.168-3.5-3.358-8.541t-4.6-10.366q-2.409-5.329-4.745-10.147t-3.8-7.738l15.622-8.03q1.9,3.5,4.234,8.176t4.672,9.636q2.336,4.964,4.526,9.782t3.8,8.614ZM97.674-32.85V-5.11a48.72,48.72,0,0,1-.657,8.76,11.467,11.467,0,0,1-2.628,5.694,11.981,11.981,0,0,1-5.548,3.285,49.982,49.982,0,0,1-9.271,1.679L69.934,15.33,64.678-1.606l9.344-.876q3.358-.292,4.526-1.825A7.25,7.25,0,0,0,79.716-8.76V-32.85H48.034V-79.862h77.818V-32.85ZM65.846-49.932H108.04V-62.78H65.846Zm-22.338.876q-2.774,8.614-5.256,15.549T33.215-20.075q-2.555,6.5-5.4,13.14T21.462,7.592L4.526.438q7.154-14.892,12.7-28.4t9.928-27.667Zm27.3,26.718q-2.336,4.38-5.256,9.052T59.422-4.015Q56.21.584,52.779,4.891t-6.789,8.1L31.828,1.168q3.066-3.5,6.5-7.519t6.643-8.395q3.212-4.38,6.132-8.833a98,98,0,0,0,5.11-8.687Zm45.114-9.636q2.628,2.92,6.059,6.935t6.789,8.4q3.358,4.38,6.57,8.687t5.4,7.811L127.02,11.972q-2.19-3.5-5.4-7.884t-6.643-8.906q-3.431-4.526-6.789-8.833t-6.132-7.519Z"
              stroke="#fff" stroke-width="4" transform="translate(1019 380)"/>
        <path d="M132.276-97.82q-.584,19.71-1.168,37.668-.292,7.738-.584,15.768t-.511,15.549q-.219,7.519-.511,14.089t-.584,11.388a25.83,25.83,0,0,1-1.46,7.665A11.625,11.625,0,0,1,124.1,9.125a13.359,13.359,0,0,1-5.4,2.628,53.81,53.81,0,0,1-7.446,1.241L95.63,14.454,89.936-4.672l15.038-1.46a13.47,13.47,0,0,0,2.774-.511,3.744,3.744,0,0,0,1.9-1.314,6.582,6.582,0,0,0,1.1-2.628,29.714,29.714,0,0,0,.511-4.6q.438-6.716.73-15.038t.584-17.009q.292-8.687.438-17.155t.292-15.33H88.476q-2.044,4.672-4.307,9.052t-4.6,8.322L68.036-68.328V9.782l-54.458.146V-97.82H27.3l6.132-19.71,18.834,1.9L46.282-97.82H68.036v18.4Q73-89.5,77.234-100.156A186.973,186.973,0,0,0,83.658-118.7l18.25,2.336q-1.314,4.38-2.847,9.052t-3.285,9.49ZM50.954-80.008h-20v25.7h20ZM94.316-25.7A178.866,178.866,0,0,0,84.68-41.464Q79.57-48.91,74.9-55.042l13.286-10.8Q92.418-60.3,97.966-53A141.5,141.5,0,0,1,108.04-37.814ZM50.954-8.468v-27.74h-20v27.74Z"
              stroke="#fff" stroke-width="4" transform="translate(1165 380)"/>
        <path d="M135.634-14.016H118.552v11.1a33.6,33.6,0,0,1-.8,8.1,10.92,10.92,0,0,1-2.555,4.964,10.507,10.507,0,0,1-4.672,2.7,39.4,39.4,0,0,1-7.154,1.314L91.4,15.476,86.14-.146l8.03-.73q5.11-.438,6.643-1.9t1.533-5.256v-5.986H71.686q2.92,2.336,5.767,4.672t5.183,4.526L73.73,7.008q-1.752-1.752-4.088-3.942t-4.891-4.38Q62.2-3.5,59.714-5.548t-4.38-3.358l4.38-5.11H42.048V-28.47h60.3v-5.256l9.052.292v-3.65H95.63v4.38H80.592v-4.38H64.532v4.526H48.618v-52.56H80.592v-5.84H47.158v-13.578H80.592V-116.07l15.038.73v10.8h12.264l-3.65-10.22,14.016-2.19q1.168,3.358,2.19,6.424t2.044,5.986h9.782v13.578H95.63v5.84h32.12v52.56h-9.2v4.088h17.082ZM36.354-66.138V14.6H20.294V-66.138H8.468v-18.25H20.294v-30.66l16.06.584v30.076h8.614v18.25Zm28.178-6.424v5.548h16.06v-5.548Zm31.1,0v5.548H111.4v-5.548ZM80.592-49.494V-54.75H64.532v5.256Zm30.806,0V-54.75H95.63v5.256Z"
              stroke="#fff" stroke-width="4" transform="translate(1311 380)"/>
        <path d="M133.152-26.718q-4.234-.73-8.395-1.387t-8.1-1.387v40.88H27.886v-40q-4.38,1.022-8.833,1.825T9.928-25.258l-4.964-16.5a251.513,251.513,0,0,0,25.769-4.307q12.045-2.7,22.557-6.059a128.412,128.412,0,0,1-13.14-8.76q-8.322,6.424-16.5,11.68L13.578-62.488q4.38-2.482,9.2-5.767t9.49-7.154q4.672-3.869,9.052-8.176a75.6,75.6,0,0,0,7.738-8.833H31.828v13.87h-18.4v-29.2H61.174l-1.9-9.928L78.84-118.7l1.9,10.95h50.516v29.2H115.048v10.512h.146a71.4,71.4,0,0,1-9.636,7.446Q99.426-56.5,91.1-51.83a177.927,177.927,0,0,0,21.462,5.037q11.388,1.971,24.674,3.139Zm-78.84-65.7,11.1,5.986q-.73,1.022-1.46,1.9t-1.46,1.9h50.37v-9.782ZM56.94-68.036a113.566,113.566,0,0,0,15.038,8.76,131.733,131.733,0,0,0,16.5-8.76ZM104.974-32.12q-9.052-2.19-17.228-4.891T72.124-42.778Q65.116-39.858,57.6-37.23T41.61-32.12ZM98.842-16.352h-53V-4.38h53Z"
              stroke="#fff" stroke-width="4" transform="translate(1457 380)"/>
    </g>
</svg>
<script>
  // 获取所有path
  const path = document.querySelectorAll("path");
  path.forEach(item => {
    // 获取路径的长度
    console.log(item.getTotalLength());
  });
</script>
</body>
</html>
```

# 4. 最后

有时候短短几行代码就可以制作出很惊艳的动画，所以制作网页动画也是一个需要大量积累的过程，在今天之前我根本不知道SVG还有`stroke-dasharray`、`stroke-dashoffset`这两个属性。
