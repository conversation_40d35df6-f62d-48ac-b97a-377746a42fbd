---
title: "全屏滚动插件fullpage.js"
date: 2020-06-01
categories:
  - "web开发"
tags:
  - "HTML5"
  - "网页设计"
---


随着前端的飞速发展，现在制作界面时出现了非常多的好看的样式，比如说全屏滚动效果，能够带来很舒服的视觉体验。先来看一下什么是全屏滚动：

![img](/images/v2-432af4e9a4a61d6a7d615dca0b11e98c_b.webp)

这是`fullpage.js`的官网，只需要滑动一下滚轮，就能实现翻页的效果。

# 1. 安装

[fullPage.js中文文档github](https://github.com/alvarotrigo/fullPage.js/tree/master/lang/chinese#fullpagejs)

如何安装其实文档上面已经说得很清楚了，即：

```powershell
// 使用 bower
bower install fullpage.js

// 使用 npm
npm install fullpage.js
```

包含文件：

```html
<link rel="stylesheet" type="text/css" href="fullpage.css" />

<!-- 以下行是可选的。 只有在使用选项 css3:false ，并且您希望使用其他缓动效果，而非 linear 、 swing 或 easeInOutCubic 时才有必要。 -->
<script src="vendors/easings.min.js"></script>

<!-- 以下行仅在使用选项 scrollOverflow:true 的情况下是必需的 -->
<script type="text/javascript" src="vendors/scrolloverflow.min.js"></script>
<script type="text/javascript" src="fullpage.js"></script>
```

因为现在的单页面应用几乎都是使用的webpack打包工具，所以直接看一下webpack怎么引入，如果有使用其它工具，可以直接到下面的网址进行查看如何引入。

https://github.com/alvarotrigo/fullPage.js/wiki/Use-module-loaders-for-fullPage.js

```js
// Optional. When using fullPage extensions
//import scrollHorizontally from './fullpage.scrollHorizontally.min';

// Optional. When using scrollOverflow:true
//import IScroll from 'fullpage.js/vendors/scrolloverflow';

// Importing fullpage.js
import fullpage from 'fullpage.js';

// When using fullPage extensions replace the previous import
// of fullpage.js for this file
//import fullpage from 'fullpage.js/dist/fullpage.extensions.min';

// Initializing it
var fullPageInstance = new fullpage('#myFullpage', {
    navigation: true,
    sectionsColor:['#ff5f45', '#0798ec', '#fc6c7c', 'grey']
});
```

# 2. 应用于React

根据上面的方法，我们很容易就可以在React中进行使用`fullpage.js`，只需要创建：

```html
<div id="myFullpage">
  <div className="section">Some section</div>
  <div className="section">Some section</div>
  <div className="section">Some section</div>
  <div className="section">Some section</div>
</div>


componentDidMount() {
  let fullPageInstance = new fullpage('#myFullpage', {
    navigation: true,
    sectionsColor:['#ff5f45', '#0798ec', '#fc6c7c', 'grey']
  });
}
```

![img](/images/v2-a5cb4934a0e9c2bc6ea72850bf96f494_b.webp)

可以看到通过上面的集成后，初步效果已经出来了，剩下的就是填充自己的内容，并且根据需要进行配置。

```js
var myFullpage = new fullpage('#fullpage', {
	//导航
	menu: '#menu',
	lockAnchors: false,
	anchors:['firstPage', 'secondPage'],
	navigation: false,
	navigationPosition: 'right',
	navigationTooltips: ['firstSlide', 'secondSlide'],
	showActiveTooltip: false,
	slidesNavigation: false,
	slidesNavPosition: 'bottom',

	//滚动
	css3: true,
	scrollingSpeed: 700,
	autoScrolling: true,
	fitToSection: true,
	fitToSectionDelay: 1000,
	scrollBar: false,
	easing: 'easeInOutCubic',
	easingcss3: 'ease',
	loopBottom: false,
	loopTop: false,
	loopHorizontal: true,
	continuousVertical: false,
	continuousHorizontal: false,
	scrollHorizontally: false,
	interlockedSlides: false,
	dragAndMove: false,
	offsetSections: false,
	resetSliders: false,
	fadingEffect: false,
	normalScrollElements: '#element1, .element2',
	scrollOverflow: false,
	scrollOverflowReset: false,
	scrollOverflowOptions: null,
	touchSensitivity: 15,
	bigSectionsDestination: null,

	//可访问
	keyboardScrolling: true,
	animateAnchor: true,
	recordHistory: true,

	//布局
	controlArrows: true,
	verticalCentered: true,
	sectionsColor : ['#ccc', '#fff'],
	paddingTop: '3em',
	paddingBottom: '10px',
	fixedElements: '#header, .footer',
	responsiveWidth: 0,
	responsiveHeight: 0,
	responsiveSlides: false,
	parallax: false,
	parallaxOptions: {type: 'reveal', percentage: 62, property: 'translate'},
	cards: false,
	cardsOptions: {perspective: 100, fadeContent: true, fadeBackground: true},


	//自定义选择器
	sectionSelector: '.section',
	slideSelector: '.slide',

	lazyLoading: true,

	//事件
	onLeave: function(origin, destination, direction){},
	afterLoad: function(origin, destination, direction){},
	afterRender: function(){},
	afterResize: function(width, height){},
	afterReBuild: function(){},
	afterResponsive: function(isResponsive){},
	afterSlideLoad: function(section, origin, destination, direction){},
	onSlideLeave: function(section, origin, destination, direction){}
});
```

**如果你想将一个页面设置为默认页，只需要给它添加`active`类，例如`<div class="section active">Some section</div>`。**

# 3. 导航小圆点

![img](/images/v2-f5d2b9a8deb690dc4c796bdb2182484a_b.webp)

这种导航小圆点是非常实用的一个功能，引入也很简单，

```js
import 'fullpage.js/dist/fullpage.min.css'; //引入CSS样式，如果不引入是无法显示小圆点。

new fullpage('#fullpage', {
  navigation:true, // 开启小圆点
});
```

# 4. 锚点

通常我们都需要一些锚点，点击锚点直接进行切换页面：

根据官方文档，如果要使用锚点功能只需要在HTML标签中使用属性`data-anchor`，例：

```html
<div class="section">
	<div class="slide" data-anchor="slide1"> slide 1 </div>
	<div class="slide" data-anchor="slide2"> slide 2 </div>
	<div class="slide" data-anchor="slide3"> slide 3 </div>
	<div class="slide" data-anchor="slide4"> slide 4 </div>
</div>
```

或者也可以直接在声明时进行初始化：

```js
new fullpage('#fullpage', {
	anchors:['firstPage', 'secondPage', 'thirdPage']
});
```

但是我发现了一个新问题：在`react-router`的`HashRouter`中会把`#`当做是hash来处理。也就是说**锚点在`HashRouter`中无法使用。**`HashRouter`即地址栏中包含`#`的模式，例如：`https://www.xxx.cn/#/`这种地址，既然这种地址这么丑，为什么还要使用呢？

因为项目部署到服务器后，如果没有后端进行转发或者重定向，不使用`HashRouter`模式就可能存在你**刷新一下页面会出现页面未找到的情况，但是我没有找到太好的解决办法。**目前仅想到了使用`fullpage_api.moveTo();`方法来进行控制，但是远远没有锚点方便。

# 5. react-fullpage

都写到这里了，我才发现原来这个插件有React版本的，不仅有React版本，甚至还有Vue和Angular，不过应该仅仅是在该插件的基础上做了封装。

[react-fullpagegithub](https://github.com/alvarotrigo/react-fullpage)

## 5.1 安装

```powershell
npm install @fullpage/react-fullpage
```

## 5.2 使用

使用方法大体相同，如果将上面的例子改为`react-fullpage`写法即为：

```js
import React, {Component} from 'react';
import ReactFullpage from '@fullpage/react-fullpage';

class Test extends Component {
  render() {
    return (
      <ReactFullpage
        //上面的参数设置都写在这里，不用再写在构造函数里面
        navigation={true}
        sectionsColor={['#ff5f45', '#0798ec', '#fc6c7c', 'grey']}

        render={({state, fullpageApi}) => {
          return (
            <ReactFullpage.Wrapper>
              <div className="section">Some section</div>
              <div className="section">Some section</div>
              <div className="section">Some section</div>
              <div className="section">Some section</div>
            </ReactFullpage.Wrapper>
          );
        }}
      />
    );
  }
}
export default Test;
```

# 6. 视频推荐

https://www.bilibili.com/video/BV1Xx411d7rc
