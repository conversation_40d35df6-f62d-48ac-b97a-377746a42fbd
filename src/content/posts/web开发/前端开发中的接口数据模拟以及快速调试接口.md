---
title: "前端开发中的接口数据模拟以及快速调试接口"
date: 2021-4-17 19:09:25
categories:
  - "web开发"
tags:
  - "API"
---


在当今前后端分离的情况下，几乎所有的数据都是从后端来的，而界面上所有的交互数据又要去到后端，所以我们在前端开发中有极大的一部分时间是在跟接口打交道，尤其是跟调试接口打交道，那么如何快速的进行调试接口呢？

我研究了一番后发现有下面3个神器：

1. json-server：后端还未出接口时，我们可以通过json-server来制造出一些假接口。
2. fakerjs：快速应对各种情况，随机生成一些假数据来测试接口。
3. Fiddler Everywhere：在Windows平台上面很火的抓包工具Fiddler4的整合版本，该版本比Fiddler4拥有更好的界面。

# 1. json-server

有时候后端没有出接口，但是前段又需要数据来进行布局怎么办，将伪数据放在工程中其实是一个不太明智的举动，第一个是可能你上线发布代码的时候忘记删除掉这些伪数据，第二个原因是通过js手动引入一些伪数据调用不是那么的方便。

所以这个时候就可以使用json-server来制造一些假数据，同时还可以使用ajax对这些数据进行请求。

# 2. fakerjs

前端开发对接口的测试肯定是少不了的一个环节，只有测试了增加接口才能去测试删改查接口，如果后端不给数据的话，那就只有前端去新增一些数据才能进行接下来的测试。

那么这里就会出现一个问题，如果我们要一个一个的手动去增加数据，比如一个表单需要填写的数据非常多，有姓名、电话、邮编、地址等等几十个字段，如果每次我们都要手动去输入这些字段那可是太麻烦了，而且你手动输入的要像那么一回事就更麻烦了，大多数情况下，我们都是直接脸滚键盘，随便输入一些字段进行测试。

所以这个时候我们就需要fakerjs的帮忙，它的作用是帮助我们生成一些随机的值，其中包含了各种各样的值，比如姓名、电话、邮编、地址等等这些信息都是能够随机生成的。

推荐引入：`@types/faker`这个类型声明包来获得更好的代码提示。

## 2.1 爬虫

同时，我们甚至可以创建一个Nodejs项目，通过爬虫来大量增加数据，因为在Node环境中，fakerjs也是能够使用的，其实后端也可以直接向数据库导入数据，但是有时候可能你并不太需要太多的数据，甚至后端也没有数据的情况下也只有你手动去新增。

所以这种机械式的动作交给爬虫去做就好了，通过fakerjs生成一些模拟数据。

```js
const faker = require("faker");

// 设置语言
faker.locale = "zh_CN";

console.log("姓名：" + faker.name.firstName() + faker.name.lastName());
console.log("地址：" + faker.address.state() + faker.address.streetName() + faker.address.secondaryAddress());
console.log("电子邮箱：" + faker.internet.email());
console.log("手机号：" + faker.phone.phoneNumber());
console.log("公司：" + faker.company.companyName());
```

使用方法也很简单，至于具体的API可以直接参考[官方给出的示例](https://rawgit.com/Marak/faker.js/master/examples/browser/index.html#address)进行调用。

## 2.2 Python

不得不说一下Python环境下的faker库，同样是模拟数据，差距咋个就这么大呢？

我们来看看同样的伪数据Python的faker库和js的fakerjs生成的模拟数据的真实度差距到底有多少。

我们生成的数据主要分为以下几个属性：**姓名、电话、地址、电子邮箱、手机号。**

JavaScript环境下最终输出结果：

![image-20210404184100456](/images/web/前端开发中的接口数据模拟以及快速调试接口/image-20210404184100456.png)



Python环境下最终输出结果：

![image-20210404184134541](/images/web/前端开发中的接口数据模拟以及快速调试接口/image-20210404184134541.png)



可以看出这个差距是非常明显了，不知道是我的问题还是fakerjs这个库就这样，我看了一下它官方上的那些中文示例跟我实际生成的示例也没有太大的区别，所以如果你会一点点Python的话，推荐还是使用Python来添加一些伪数据，因为Python的库生成的伪数据更加真实一些。

# 3. Fiddler Everywhere

Fiddler4的界面怎么看都像是上个世纪的产物，而Fiddler Everywhere的界面观感就好了很多，并且Fiddler Everywhere还拥有一部分PostMan的功能，它可以捕获你发送的请求，并且可以很方便的将请求的某个属性进行修改再次进行请求。

就我个人的感觉来讲：Fiddler4更加适合测试人员，它可以同时对一个接口发起很多次请求，可以对接口进行压力测试，而Fiddler Everywhere更适合前端人员，因为它的请求参数修改界面更加的友好。

## 3.1 为什么要使用Fiddler

在前端开发中，我们观察通过ajax（或者fetch）发起的请求都是在浏览器的调试界面，在这里也可以重复发起请求，但是无法对已经发起的请求做编辑操作，然后再进行发送。

1. 在平时前端开发中，我们大部分的表单都是一次性的，即填完后会有一些处理事件，比如将表单清空、页面跳转等等事件。
2. 在开发中，前端与后端对接口的时候，你的某个传值方式可能不对而后端返回异常，你并不太能确定后端的接值方式，如果你要通过改写代码去测后端的接值方式那会花费很多的时间。

而Fiddler可以抓获我们发起的请求数据包，可以直接对请求的数据进行编辑，做到快速测试接口的接值方式。

![image-20210404220452571](/images/web/前端开发中的接口数据模拟以及快速调试接口/image-20210404220452571.png)

打开后会自动抓取所有请求，找到对应的请求点击右键，选择Edit in Composer，可以直接编辑该条请求，如果你填写表单的时候遇到调用接口不成功，后端验证数据失败，你有时候总不能再填一次表单吧，填一次要花几分钟，这个时候你就可以使用这个功能，直接更改你觉得有问题的那条数据，然后点击EXECUTE重新进行请求：

![image-20210404221332602](/images/web/前端开发中的接口数据模拟以及快速调试接口/image-20210404221332602.png)

# 4. 最后

其实很多软件可以提升我们在开发中的效率，要有一颗善于发现的心，毕竟要想划水就必须要提高自己的工作效率，这样才可能留出更多的时间来划水，你有更多的时间划水就会有更多的时间去学习然后充电自己，你才不会因为好几年都在原地踏步然后被淘汰。
