---
title: "GSAP动画插件-ScrollTrigger（一）"
date: 2021-1-23 22:27:33
categories:
  - "web开发"
tags:
  - "动画"
---


最近在做个人主页，因为我之前的工作一般是做后台管理界面，或者表单填写界面，没有制作过主页相关的项目，所以对动画的了解非常少，这次就想在自己的主页上面使用动画。

动画领域有一个比较知名的CSS库：Animate.css，它提供了60多种动画，满足一般网页的需求，比如淡入淡出、闪现等等一系列日常动画，不过虽然它能满足日常需求，但是一些复杂的场景就需要靠JS手动去操作，比如界面滚动到某个元素才开始播放动画，比如拖拽、比如滚动界面时，动态调整元素。

# 1. 目标

而我想实现当页面滚动到该元素时，元素才开始播放动画的效果。

来看看我最终通过GSAP实现的效果：

**注：因为Gif帧数低的原因，所以动画效果并不是太明显，你也可以直接到：[我的个人主页](https://cclliang.com/)进行查看动画效果。**

![GSAP-ScrollTrigger](/images/web/GSAP动画插件-ScrollTrigger（一）/GSAP-ScrollTrigger.gif)

如果自己要手动实现这个需求，其实也不难，只需要监听页面滚动，当要滚动到该元素时，动态添加已经设置好动画的CSS类名，或者直接使用JS动态添加动画。

**但自己实现会存在一些响应式界面造成元素高度不一致带来的兼容性问题，所以这个时候使用已经成熟的第三方动画框架就是最好的选择。**

单纯的CSS动画库是不可能完成我预想的功能，而JS库我考虑过使用[scrollreveal](https://github.com/jlmakes/scrollreveal)和[WOWJS](https://github.com/matthieua/WOW)。

下面是我最后放弃这两个库的原因：

- WOWJS：依赖Animate.css，可以配合使用Animate.css动画，但是很久没有进行过维护，**并且不支持TypeScript**。
- scrollreveal：不依赖任何第三方库，使用方便，但是商业使用需要购买凭证，同时支持的功能较少。

这个时候我就想起很久之前我写过的一篇关于一个动画库（GSAP），其实我甚至觉得它可以称为一个动画框架，因为它的生态实在是太健全了，从简单动画，到拖拽，到滚动触发，应有尽有，几乎你能想象到的网页动画在它这里都可以实现，并且只需要使用它一个框架。

但是不知道为什么，这么厉害的东西，在国内很少有关于它的资料，并且GitHub上面的star也仅仅只有1万，同时它的官方还声明了大部分商业用途都可以免费使用。

![image-20210103163849283](/images/web/GSAP动画插件-ScrollTrigger（一）/image-20210103163849283.png)

我觉得它不火的原因可能是因为功能太多太复杂，往往一个界面不需要这么多动画，使用简单的Animate.css库和animejs库就可以满足日常开发的需求，毕竟这两个库的上手难度比起GreenSock低太多了。

# 2. ScrollTrigger插件

前景回顾：[GSAP（GreenSock）：最健全的web动画库之一](/2020/06/03/web开发/gsapgreensock最健全的web动画库之一/)。

之前的这篇文章几乎是我水过去的...很多东西都介绍的非常模糊，因为官方文档全是英文，而国内又找不到几篇介绍这个动画库的文章，所以我只有按照官方文档一步一步的摸索，不过说实话，官方文档写的确实是非常详细，甚至还附带了教学视频。

ScrollTrigger这个插件是要基于GSAP的，相当于ScrollTrigger仅仅是用来控制触发动画，而GSAP才是用来操作元素。

# 3. 开始使用

首先，我这个项目是使用了React框架，因为GSAP是基于原生JavaScript进行编写，所以无论你用的是什么框架，都可以使用。

当然...类似于小程序和React Native这种，我没有试过，各位可以自行尝试一下看是否能用。

## 3.1 引入

官方介绍引入的方法非常有意思，直接到[官方提供的网站](https://greensock.com/docs/v3/Installation?checked=core#modules)。

![image-20210123224325525](/images/web/GSAP动画插件-ScrollTrigger（一）/image-20210123224325525.png)

**这里列出了所有可用的插件，需要哪个插件，就勾选哪个插件，下面会列出引入插件的代码**，相信既然已经开始学习动画编写，那么你一定对Web开发有一定了解，那么这里就不再细说引入步骤。

## 3.2 基础讲解

该插件提供的功能非常丰富，我也没有进行深入研究，所以仅能讲解一下最基础的东西，如果后面我使用到了更复杂的功能，到时候再进行使用记录。

先使用GSAP创建一个简单的动画：

![basic](/images/web/GSAP动画插件-ScrollTrigger（一）/basic.gif)

使用到的代码非常简单：

```html
<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.0/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.0/ScrollTrigger.min.js"></script>
    <style>
      #logo {
        display: flex;
        justify-content: center;
      }
    </style>
  </head>
  <body>
    <div style="height: 100vh"></div>
    <div id="logo">
      <img
        src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/16327/logo-man.svg"
        alt=""
      />
    </div>
    <div style="height: 100vh"></div>
    <script>
      gsap.from("#logo", { duration: 2, scale: 0.3 });
    </script>
  </body>
</html>
```

动画创建好了，但是这里存在一个问题，比如我们刷新界面。将屏幕滚动到这个图片所在的位置。

![demo1](/images/web/GSAP动画插件-ScrollTrigger（一）/demo1.gif)

可以看到当屏幕滚动到该图片时，动画已经快要播放完毕了，这是因为动画是从刷新的那一刻，DOM加载完毕后就开始播放，也就是说你的电脑性能越好，网速越快那么动画的播放几乎就在你刷新界面的那一刻就开始了，如果你再晚一点拖动窗口，等你看到该图片的时候动画就已经播放完毕了，那么这个时候动画就没有任何意义。

所以我们需要想出一种办法，有没有一种等到屏幕滚动到该图片所在的区域，这个时候才开始播放动画？

ScrollTrigger这个插件就完美的解决了这个问题，操作也十分简单，我们只需要稍微修改一下上面的代码：

```js
gsap.from("#logo", {
    scrollTrigger: "#logo", // 此行代码表示触发动画的元素，只需要增加该行代码，就可以实现想要的效果。
    duration: 2,
    scale: 0.3
});
```

![demo2](/images/web/GSAP动画插件-ScrollTrigger（一）/demo2.gif)

当我刷新界面后，专门等了一会才开始滚动界面，可以看到在图片进入屏幕中时，才开始播放动画。

# 4. 最后

讲道理，这种功能健全的库应该会很火才对，不知道为什么关于它的中文资料相当少，也许是国内的大部分前端开发者都是在做后台应用或者小程序，用不到太多的动画，使用Animate.css和anime.js这两个库就完全可以满足一些简单的需求。

网页动画是一项非常耗时的工程，并且制作的动画又非常吃性能，所以可能对动画需求这方面也比较少，现在好多家用电脑都讲究能用就行，配置甚至是“上个时代”的，而且使用手机上网的网民还占绝大多数，在手机上面会打开手机浏览器去浏览网页的也仅仅只占很少一部分，大多数人需要搜索东西就直接选择在头条或者微博这类APP上面搜索。

