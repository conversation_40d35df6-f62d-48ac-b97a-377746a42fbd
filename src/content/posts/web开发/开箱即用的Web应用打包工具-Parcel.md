---
title: "开箱即用的Web应用打包工具-Parcel"
date: 2021-4-17 23:44:23
categories:
  - "web开发"
tags:
  - "打包"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

在10年前，那时前端和后端还没有分离，前端写完了界面后统统都要将页面文件交给后端，然后由后端部署在Tomcat这一类的服务上，那个时代的前端受到了很大程度的轻视，甚至被戏称为-**切图仔**。

但后来发生了一件大事，那就是Chrome浏览器出现后，Google团队开源了V8引擎，**Ryan Dahl**这位大佬基于V8引擎开发了一款动荡整个前端行业的**JavaScript运行环境-Node.js**。由于它的出现，前端项目走向了工程化、前后端分离的大趋势，同时由于Node.js的原因，现在使用JavaScript不仅仅可以开发Web端，还可以开发手机端、桌面端、服务器端等等。

目前JavaScript有3款比较有名的打包框架，分别是**大名鼎鼎的Webpack、高级前端常用的Rollup、以及号称0配置的Parcel。**

它们各有各的优势，不过探究它们的区别并不是本篇文章的重点，经过我查阅了很多资料，这些资料一致认为，**Webpack比较适合打包工程化项目，Rollup比较适合打包库，而Parcel的优势在于它的0配置打包。**

例如之前我有一篇文件讲到：[强大的油猴Tampermonkey脚本开发环境搭建](/2021/02/22/脚本/强大的油猴tampermonkey脚本开发环境搭建/)，那个时候我还没有接触到Parcel，其实使用Webpack打包脚本不是不可以，只是Webpack需要经过一系列比较麻烦的配置，而像这一类脚本项目，使用开箱即用的Parcel打包就会更加的方便与快捷。

# 1. 使用Parcel

学习过Webpack的同学应该都清楚，Webpack有一个配置文件，如果在不进行任何配置的情况下，它默认只能打包`.js`和`.json`类型的文件，其它文件都无法直接进行打包，需要手动配置一个一个的loader。

而Parcel就不同，它几乎默认支持打包所有前端会用到的文件格式，什么`css`、`sass`、`less`、`TypeScript`、`img`等等，完全不需要你手动去写配置文件，几乎开发中所有需要用到的loader官方都已经在配置文件中帮你写好了，你要做的仅仅是需要下载一下编译环境，比如`sass`你就需要使用`npm install -D sass`下载`sass`编译环境，而`less`甚至都不需要安装，当Parcel检测到`less`文件时会自动进行安装编译环境！就是这么神奇！

## 1.1 简单打包

如果我们仅仅是为了简单打包一个js脚本文件，那么全局安装`parcel-bundler`就可以了。

```shell
npm install -g parcel-bundler

# 或者yarn
yarn global add parcel-bundler
```

然后在正在搭建的项目中生一个`package.json`文件（如果不需要使用npm包，不生成也可以进行打包）。

```shell
npm init -y
```

### 1.1.1 单入口打包

一般来讲，现代web框架生成的都是单页面应用，所以我们只需要一个`index.html`文件当做入口文件，然后在里面引入一个`index.js`文件。

例如：

**index.html：**

```html
<html>
  <body>
    <script src="./index.js"></script>
  </body>
</html>
```

**index.js：**

```js
console.log('hello world')
```

Parcel内置了一个**当你改动文件时能够自动重新构建应用的开发服务器**，在控制台输入下面的命令就可以打开服务器：

```shell
parcel index.html
```

该服务默认启动在`1234`端口上面，所以你访问`http://localhost:1234/`就可以访问到你的web应用，同时你也可以使用`-p 8080`来手动指定你想要运行的端口号。

```shell
parcel index.html -p 8080
```

在某些情况下你想要打包一个js脚本，比如开发油猴脚本，那么你根本不需要启动一个服务器，只需要监听js文件的改变，那么在参数中加上`watch`，**当文件改动时它仍然会自动重新构建并支持热替换，但是不会启动web服务。**

```shell
parcel watch index.html
```

上面这些命令运行后都会生成一个`dist`文件夹，这里就是放置开发环境下打包后的文件。

### 1.1.2 多入口打包

假设你有多个入口文件，比如是`index.html`和`about.html`，那么你有两种方式来打包：

指定当前文件的名字：

```html
parcel index.html about.html
```

使用tokens并创建一个glob：

```html
parcel ./*.html
```

通过这种方式打包后，如果要访问index.html则必须通过`http://localhost:1234/index.html`这种形式进行访问，直接访问`http://localhost:1234`是不行的。

### 1.1.3 生产环境

生产环境下**不会持续监听所需要构建的文件的变化，也就是说只会构建一次**，命令也很简单，直接使用：

```shell
parcel build index.html
```

该模式下，所有的代码都是经过压缩处理的。

当然，你也可以指定输出目录：

```shell
parcel build entry.js --out-dir build/output

# 或者
parcel build entry.js -d build/output
```

# 2. React

通过Parcel打包React项目是非常简单的一件事情：

```shell
npm install --save react react-dom
npm install --save-dev parcel-bundler

# 或者使用yarn
yarn add react react-dom
yarn add --dev parcel-bundler
```

在项目中安装上面的依赖后，就可以使用React来进行搭建项目了，是不是非常简单，好！收工~

什么？你说你不知道怎么开始编写React应用程序？别急，让我一步一步的告诉你：

还是一样的步骤：需要一个`index.html`文件当做入口文件，然后在里面引入一个`index.js`文件，而`index.js`文件中再引入一个`App.jsx`文件。

**index.html：**

```html
<body>
    <!-- 这里的id为什么都行，React官方设置的root -->
    <div id="root"></div>
    
    <script src="./index.js"></script>
</body>
```

**index.js：**

```js
import React from "react";
import ReactDOM from "react-dom";
import App from "./App";


ReactDOM.render(<App/>, document.getElementById("root"));
```

**App.jsx：**

```jsx
import React from "react";

const App = () => {
  return (<div>Hello React!</div>);
};

export default App;
```

这个时候再通过Parcel启动服务：

```shell
parcel index.html
```

访问`http://localhost:1234`就可以看见`Hello React!`了，接下来就跟正常的React项目的编写一样。

# 3. Vue

Vue就更简单了，只需要安装一个vue包，如下：

```shell
npm install --save vue
npm install --save-dev parcel-bundler

# 或者使用yarn
yarn add vue
yarn add --dev parcel-bundler
```

至于怎么开始编写Vue项目，**方式和上面的React差不多**，不过区别在于`index.js`的编写：

```js
import Vue from 'vue';
import App from './App';

new Vue({
  render: (h) => h(App)
}).$mount("#root")
```

接下来就可以在`App.vue`文件中编写Vue项目了！

# 4. Typescript

一样，需要引入下面的包：

```shell
npm install --save-dev typescript
npm install --save-dev parcel-bundler

# 或者使用yarn
yarn add --dev typescript
yarn add --dev parcel-bundler
```

收工！可以在项目中使用TypeScript语法了，当然，Parcel作为**0配置打包框架**，也可以直接编译TypeScript。

```shell
parcel myTypescriptFile.ts
```

是不是非常简单！

**经过我的实验发现，只要Parcel检测到相关的文件，会自动帮你安装相关的依赖，比如`.jsx`、`.vue`文件，所以你甚至可以连依赖都不用安装，直接用它打包就好了。**

也就是说`npm install --save react react-dom`、`npm install --save vue`这种命令你都不用敲了，Parcel会自动帮你敲。

注：当然，**上面的Vue和React项目的搭建仅仅是推荐编写一些脚本文件或者小型项目，中大型项目还是强烈推荐React和Vue官方的脚手架工具**，因为官方针对项目的脚手架做了很多配置和优化，所以不推荐从0搭建。

# 5. 最后

Parcel需要讲的东西不多，因为它宣传的就是0配置，所以不需要过多的去查看它的[官方文档](https://zh.parceljs.org/getting_started.html)，只需要用到的时候再进行查阅就行，这跟Webpack和Rollup这一类打包工具不一样，使用它们之前必须得进行配置，就因为简单易用这一点，在Webpack表现的如此强势的当下，Parcel还能占有一席之地。