---
title: "个人开发入门"
date: 2025-05-30 22:35:15
tags:
  - "个人开发"
  - "hide"
---


作为一个程序员，在工作之余，难免有一些想要独自实现的开发需求，或者是想要独立开发一个应用，带来额外的收入，我们不仅需要具备基础的编程能力，还得掌握一个程序从打包到发布到生产的全流程。

由于一个人的精力非常有限，所以独立开发者大多数都无法开发出大量的应用，一般一个公司的自研团队都会有不同的几个部门，一个越活越百万级别的应用，一般需要几十个人的团队才能完成，所以独立开发者想要开发出大型的应用，几乎是不可能的。

但是，独立开发者以创意赚的盆满钵满的例子也不是没有，比如：星露谷物语，这款游戏在2016年发布，至今已经卖了1000多万份，而它的作者，仅仅是一个人，他通过自己的创意，开发出了这款游戏，并且通过自己的努力，将这款游戏推广到了全球，成为了全球最受欢迎的游戏之一。

就是因为一个人的精力有限，所以大部分独立开发者都选择了独立开发三件套：日记、TODO、记账。

因为它们难度低，用户粘度高，但正是由于难度太低了，所以自从AI编程出来后，完全没有开发经验的人也通过AI大量的创造这种类似的应用，导致这种应用的竞争非常激烈。

当然这些都不是本篇文章探讨的重点，本篇文章的重点是：应用开发出来后，应该如何发布到生产环境，让用户可以使用。

## 网站

由于中国的Web环境不太好，大多数开发Web端都是手机端、后台应用、门户网站，其中个人开发者一般会用来开发自己的主页、博客等，所以个人开发者一般不会选择开发Web端。

如果你开发出一个网站，想要发布到生产环境，有很多种方法，但这里我只说我使用的方法，那就是购买一个服务器和域名，然后域名需要备案，备案域名的前提就是需要挂载到一个服务器上面，如果你使用的是阿里云、腾讯云这种云服务商，那么你点击到对应的域名备案，然后按照提示操作即可。

至于管理服务器，我推荐是使用宝塔面板，宝塔面板是一个非常方便的工具，它可以帮助你管理服务器，包括安装软件、管理网站、管理数据库等。

挂载网站的容器我推荐使用Nginx，Nginx是一个非常强大的Web服务器，它可以帮助你管理网站，包括反向代理、负载均衡、缓存等，并且它几乎不吃服务器资源，即便你的服务器内存很小，也能轻松的挂载多个网站。

SSL证书我推荐使用Let's Encrypt，Let's Encrypt是一个非常强大的SSL证书颁发机构，它可以帮助你免费颁发SSL证书，并且它非常方便，你只需要在宝塔面板中点击申请，然后按照提示操作即可。

如果你想要使用CDN，那么我推荐是使用Cloudflare，Cloudflare是一个非常强大的CDN服务商，它可以帮助你免费颁发SSL证书，并且它非常方便，你只需要在宝塔面板中点击申请，然后按照提示操作即可。

如果你使用的是Github，那么我推荐你使用Github Actions，Github Actions是一个非常强大的CI/CD工具，它可以帮助你自动化部署你的网站，并且它非常方便，你只需要在Github中点击创建一个Actions，然后按照提示操作即可。

Github Actions比起Jenkins来说，它更加适合个人开发者，因为Github Actions是免费的，使用的是Github的服务器资源，而Jenkins则需要服务器自行部署，占用的内存还不低，通常需要2G以上的服务器，而且Jenkins的配置非常复杂，对于个人开发者来说，非常不友好。

如果你购买的是境外的服务器与域名，那么即使你不进行备案，也可以正常使用。

## APP

APP是最适合独立开发者开发的，尤其是外区的IOS应用，因为外区的IOS应用只需要一个开发者账号，就可以发布到App Store。而且IOS应用很难被破解，不像安卓应用，不仅上架困难，而且很容易被破解。

而且安卓应用则需要公司资质，才能上架到应用市场，这对于独立开发者来说，往往得不偿失，辛辛苦苦开发的应用，如果浏览好，那很容易被破解。

所以大部分人开发APP，都只会开发IOS应用，而安卓应用则不会去开发，在小红书上面，百分之90的个人开发的APP都是IOS应用，而安卓应用则很少。

由于我个人没有过APP开发的实际经验，也没有上架经验，所以就一笔带过。

## 小程序

小程序作为APP的轻量级替代品，非常适合个人开发者开发，因为小程序的开发成本非常低，只需要一个微信开发者账号，就可以开发小程序。

而且小程序还有个天然的优势，它十分好接入广告，只要你的小程序流量高，你完全可以通过广告赚取收入，但是小程序需要受到微信的审核，如果你想要开发的类目比较特殊，那么可能会出现个人开发者无法上架的情况。

小程序里面如果以个人开发者的身份上架，那么是不能接入支付功能的，具体可以看[微信小程序支付接入指南](https://developers.weixin.qq.com/miniprogram/dev/framework/open/payment.html)

小程序的后端开发有两种方式，一种是直接使用微信官方提供的云开发或者第三方云开发，它们的好处就是不用自己购买服务器域名进行备案，这里需要注意的是，自己开发的服务器，必须通过备案的域名，小程序才能进行访问。而如果使用微信官方提供的云开发，则可以省去备案这一步，同时也不用担心服务器崩溃。

但是微信官方提供的云开发，它的功能非常有限，而且它的价格非常贵，如果有条件还是推荐自行开发服务器端。

## 数据存储

个人开发的软件不太建议使用数据库，因为数据库的维护成本非常高，而且数据库的备份成本也非常高，再加上可能出现数据丢失、服务器崩溃等问题，所以个人开发者慎重考虑使用数据库存储用户数据。

这也是为什么大多数独立开发的软件通常都会选择三件套的原因，因为三件套可以只用本地存储就能完成对应的需求。

## 总结

独立开发是一件很耗时耗精力的事，我曾经积极的维护了一个小程序好几个月，最后的收入也仅仅只有几百块，后面由于数据来源问题，我就直接下架了小程序。

就个人开发者而言，开发IOS端是最好的选择，因为IOS用户数量庞大，而且IOS用户对应用的付费意愿非常强，唯一的缺点就是要购买苹果的开发者账号，每年需要99美元。

微信小程序也是一个不错的选择，它接入广告很容易，而且用户量也很大，接入广告后也不需要你过多的操心收入问题，腾讯每半个月就会给你结算一次广告收入，主动将你的收入所得扣税后打入到你的银行卡中，可以说微信小程序只要你有流量，那么你就可以赚钱。

至于网页端，我最近看到大部分的开发者都是做的套壳AI，虽然需要投入一些成本，但是收益也很大，而且国内由于无法直接访问到某些AI，所以套壳AI的收益也非常可观。

但是套壳AI的竞争也非常激烈，而且套壳AI的维护成本也非常高，所以个人开发者想要做套壳AI，这是一个高成本、高收入的业务，如果你有一定的启动资金，不怕亏损，那么你可以尝试一下。

个人开发多多少少都要投入一些成本，比如域名、服务器、苹果开发者账号等，但是这对于其它行业来说，成本并不算高，值得闲暇之余进行探索。