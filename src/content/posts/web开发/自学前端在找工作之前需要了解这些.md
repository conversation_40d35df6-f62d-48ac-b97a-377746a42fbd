---
title: "自学前端在找工作之前需要了解这些"
date: 2022-1-15 09:53:55
categories:
  - "web开发"
tags:
  - "入门"
---


您好，我是**沧沧凉凉**，是一名前端开发者，目前在[掘金](https://juejin.cn/user/1380642337065421/posts)、[知乎](https://www.zhihu.com/people/hatsune-87)以及[个人博客](https://www.cclliang.com/)上同步发表一些学习前端时遇到的趣事和知识，欢迎关注。

---

我是完全通过转行来到前端岗位的，因为我之前是学土木工程的，所以我身边的朋友也没有做程序员的。

跌跌撞撞进入前端行业，之前我说了想要找前端工作应该会哪些技能，本篇文章要谈的就是进入到公司一般需要了解哪些知识。

你在网上找视频、找教程一般都会教你技术方面的东西，大部分视频不会告诉你公司里面如何去完成一个项目，怎么进行合作开发，怎么得到项目的需求，界面的交互应该怎么做，界面应该做成什么样子。

# 1. 原型图

画原型图是产品的事情，但是不管是前端还是后端，做项目的时候都要根据原型图去制作。

要学会如何去看原型图，原型图表达的是什么意思，一般原型图都是在线看的，通常产品设计完原型图会分享到墨刀、摹客、axure这些平台，然后发给你网址，你通过网址打开就能看到原型图。

关于原型图，我实在是找不到相应的示例...又不能拿公司项目的原型图来举例，上面这些平台给出的原型图示例又非常的精美，其实很多外包公司产品出的原型图根本不是那样，真实的原型图非常简陋，甚至可能连交互或逻辑都不会标注。

![image-20220115091912034](/images/web/自学前端在找工作之前需要了解这些/image-20220115091912034.png)

上面是摹客给的原型图例子，其实这个原型图都可以说是UI图了，如果各位进到一个小型外包公司，你就会明白我说的**真实的原型图和上图差距有多大。**

# 2. UI图

UI图就是美化后的原型图，后端是不会看UI图的，UI图完全是出给前端看的。

在**中后台项目中，由于项目界面大致相同，所以并不是每个界面都会出UI图，UI只会出个大概样式，确定整个项目的色彩基调就可以了，然后前端就根据这些色彩基调按照原型图去完成。**

而官网或小程序就不太一样，几乎所有的界面都需要UI出图，所以UI图对于前端是非常重要的。

这里值得一提的是，官网项目几乎90%的工作都在前端，后端的工作量非常少甚至不需要后端，一个外包的官网项目的价格大概在几千块钱左右，因为这种低价格，所以界面上不会有太多的动画，因为制作动画是一件非常麻烦的事情，甚至有些公司还有专门制作动画的岗位。

现在用的比较多的就是**蓝湖**，前端不用像过去那样需要去切图，所有的切图都交给UI去做，并且非常方便的可以看到元素尺寸，甚至还附带CSS代码，比起前几年前端拿到设计图然后由前端通过各种工具去切图，去计算元素大小方便太多。

一般来说要100%还原UI图是不太可能做到的，因为总会有那么几个`px`的差距，只能说做项目的时候尽量还原UI图，一般在项目中，只要非常贴近UI图，看起来比较舒服就可以。

![image-20220115091943939](/images/web/自学前端在找工作之前需要了解这些/image-20220115091943939.png)

这是我去年的时候做的个人主页的UI图，其实做这种设计图不难，像Adobe-XD、Figma这些工具即使你是不太懂设计的前端，你也可以做出一张张设计图，但是好不好看就另说了。

因为我之前和公司的UI交流的时候，他说设计其实是有很多规范的，所以做UI对设计师的素养要求非常高，因为设计师最主要做的就是各种色彩搭配，所以UI是比较吃经验的。

# 3. 代码管理

一般多人合作开发项目就需要一个代码管理仓库，往往是SVN、Git，它们各有各的优点和缺点。

## 3.1 SVN

- 优点：集中化项目管理仓库，操作简单，合代码时出现代码莫名其妙丢失的情况较少，自己搭建SVN管理仓库占用的服务器资源较少。
- 缺点：分支创建非常不方便，没有本地仓库，往往都在主分支上面进行开发，权限控制不方便。

SVN的使用非常简单，不会也没有关系，几乎别人给你讲解一次，你多操作几次你就会使用了。

## 3.2 Git

- 优点：分布式项目管理仓库，不光有个线上仓库，每一个本地项目都拥有一个本地Git仓库，可以随便创建分支，权限分配方便。
- 缺点：操作较为复杂，合代码的时候**一定要先提交代码到本地或者将代码暂存，再拉取线上代码，**不然非常容易出现代码丢失，我曾经就出现过写了1天的代码被我合没了的情况。

Git的使用复杂度远高于SVN不过上手同样也很简单，所以不用太担心，多操作几次就熟练了。

## 3.3 开发工具

开发工具一般是没有什么要求，哪样顺手就选哪样，不同的开发工具效率也不同，目前前端用的最多的是VScode，其次是WebStorm，不过我强烈推荐WebStorm，如果有能力推荐订阅正版，我也曾经发布了很多篇WebStorm相关文章。

# 4. 技术

一般来说，转行前端我推荐优先自学Vue，因为小公司需求Vue的非常多，即使你的技术不太好，可能也能进入到一家小公司，成功成为一个前端开发。

其实公司里面做项目和你平时学习的时候开发一些小项目没有太大的区别，只要你的基本功扎实，那进入公司你也完全可以胜任前端开发工作。就算最开始恼火一点，但是你只要咬咬牙忍一忍，总结经验，随着代码量的增加，你也可以做到独立面对一个项目。

# 5. 最后

本篇文章主要讲解了进入到公司的时候需要使用到的一些工具以及技术。

前端现在处于一个高速发展的阶段，从以前的切图仔到前端攻城狮，现在的前端完全是站在巨人的肩膀上进行开发。

