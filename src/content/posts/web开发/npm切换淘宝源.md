---
title: "npm切换淘宝源"
date: 2020-9-25 00:13:05
categories:
  - "web开发"
tags:
  - "npm"
---


# npm

**设置淘宝源**

```bash
npm config set registry http://registry.npm.taobao.org
```

**设置为官方源**

```bash
npm config set registry https://registry.npmjs.org
```

**查看当前源**

```bash
npm get registry
```

**使用代理**

必须要是科学上网，并且工具在你的本地机器上开启了一个第三方服务器

```bash
npm config set proxy http://127.0.0.1:1080  # 1080是端口号
```

**取消代理**

```bash
npm config delete proxy
```

# yarn

**设置淘宝源**

```bash
yarn config set registry https://registry.npm.taobao.org/
```

**设置为官方源**

```bash
yarn config set registry https://registry.npmjs.org
```

**查看当前源**

```bash
yarn config get registry
```

**使用代理**

```bash
yarn config set proxy http://127.0.0.1:1080  # 1080是端口号
```

**取消代理**

```bash
yarn config delete proxy
```

# 各种源地址

```bash
npm --- https://registry.npmjs.org/

cnpm --- https://r.cnpmjs.org/

taobao --- https://registry.npm.taobao.org/

nj --- https://registry.nodejitsu.com/

rednpm --- https://registry.mirror.cqupt.edu.cn/

npmMirror --- https://skimdb.npmjs.com/registry/

deunpm --- http://registry.enpmjs.org/
```

