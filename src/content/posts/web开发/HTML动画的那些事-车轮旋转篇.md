---
title: "HTML动画的那些事-车轮旋转篇"
date: 2021-1-31 21:54:15
categories:
  - "web开发"
tags:
  - "CSS"
  - "动画"
---


最近一直在研究网页动画相关的东西，因为一个网页要想让人觉得惊艳和有趣，网页动画肯定是少不了的。

上一篇文章：[用CSS做出漂亮的字体动画](/2021/01/31/web开发/用css做出漂亮的字体动画/)。

本篇文章就着重讲解一下如何让SVG动起来。

**注：上一篇文章多次含有SVG相关的代码，可能阅读体验不是那么的好，所以本篇文章仅在最后贴出含有SVG的代码。**

其中涉及到的CSS属性：

- transform-origin：更改一个元素变形的原点。
- transform-box：设置该元素对应的边框，会影响transform-origin对应的原点位置。

最终效果：

![show-wheel](https://source.cclliang.com:4433/public/image/web/HTML动画的那些事-车轮旋转篇/show-wheel.gif)

# 1. 基本

## 1.1 下载SVG

在[undraw](https://undraw.co/)这个网站上面有大量的SVG格式的图片可以免费下载，而本次我们要使用的是下面这张SVG。

![image-20210131221644231](https://source.cclliang.com:4433/public/image/web/HTML动画的那些事-车轮旋转篇/image-20210131221644231.png)

该网站还有个特色，就是可以自行选择SVG的主题颜色：

![unDraw](https://source.cclliang.com:4433/public/image/web/HTML动画的那些事-车轮旋转篇/unDraw.gif)

## 1.2 给车轮编组

当下载了SVG后，我们就需要将它粘贴到项目中，本次的目的是为了让自行车的两个轮子转动起来，所以我们首先需要找到两个轮子是由哪段代码渲染出来的。

这个时候用打开Adobe XD，选择车轮。

![wheel](https://source.cclliang.com:4433/public/image/web/HTML动画的那些事-车轮旋转篇/wheel.gif)

选择完毕后试着拖动一下，发现确实选择了整个轮胎，就按右键将它们编组。

![image-20210131223628778](https://source.cclliang.com:4433/public/image/web/HTML动画的那些事-车轮旋转篇/image-20210131223628778.png)

组名在代码中会以id呈现。

同理，左边的车轮也以同样的方式进行编组。

## 1.3 编写CSS

到了这一步就需要开始编写CSS样式，经过上一步，可以发现车轮已经被划分成了组，并且拥有了id。

```css
#right-wheel,
#left-wheel {
  animation: wheel 2s infinite linear;
  transform-origin: center;
  transform-box: fill-box;
}

@keyframes wheel {
  from {
    transform: rotateZ(0deg);
  }
  to {
    transform: rotateZ(360deg);
  }
}
```

其中`transform-origin: center`和`transform-box: fill-box`代表了让当前元素变形的原点等于当前元素的中心，经过这么几段代码，我们已经完成了开始期望的效果。

# 2. 进阶

之前我提到过gsap这个动画库，说它是什么动画都能做，今天我来尝试融入这个库，让鼠标能够转动自行车的轮胎。

前景提要：

1. [GSAP（GreenSock）：最健全的web动画库之一](/2020/06/03/web开发/gsapgreensock最健全的web动画库之一/)
2. [GSAP动画插件-ScrollTrigger（一）](/2021/01/23/web开发/gsap动画插件-scrolltrigger一/)

本次需要引入的GSAP插件是：

1. **Draggable**：这是一个专门用来处理DOM元素拖拽的插件
2. **InertiaPlugin**：让动画具有惯性。

这次直接通过CDN进行引入。

```html
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.0/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.0/Draggable.min.js"></script>
<script src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/16327/InertiaPlugin.min.js"></script>
```

JavaScript代码如下：

```js
// 将left-wheel设置为绕着中心旋转
gsap.set("#left-wheel", {transformOrigin:"center center"})
gsap.set("#right-wheel", {transformOrigin:"center center"})
// 创建动画 inertia为惯性插件
Draggable.create("#left-wheel", {type: "rotation", inertia: true});
Draggable.create("#right-wheel", {type: "rotation", inertia: true});
```

- **transformOrigin**：设置元素旋转的原点。
- **type**：设置元素拖拽的方式，"rotation"为旋转。
- **inertia**：元素是否具有惯性效果。

注：官方文档中说`type: "rotation"`默认会依照`transformOrigin:"center center"`进行旋转，但是我测试到的结果是默认情况下会按照当前元素的左上角的点进行旋转。

效果：

![gsap-wheel](https://source.cclliang.com:4433/public/image/web/HTML动画的那些事-车轮旋转篇/gsap-wheel.gif)

可以看到最终效果还是比较丝滑的。

最后附上完整代码：

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <meta content="ie=edge" http-equiv="X-UA-Compatible"/>
    <title>自行车转动</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.0/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.0/Draggable.min.js"></script>
    <script src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/16327/InertiaPlugin.min.js"></script>
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        #logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        #right-wheel,
        #left-wheel {
            animation: wheel 2s infinite linear;
            transform-origin: center;
            transform-box: fill-box;
        }

        @keyframes wheel {
            from {
                transform: rotateZ(0deg);
            }
            to {
                transform: rotateZ(360deg);
            }

        }
    </style>
</head>
<body>
<!-- 这里是SVG -->
<svg
        id="logo"
        viewBox="0 0 1057.688 784.976"
        width="600"
        xmlns="http://www.w3.org/2000/svg"
>
    <g transform="translate(0.007 0.002)">
        <path
                d="M280.88,172.94c-5.48,6.65-7.59,16-9.95,24.79-19,70.84-65.82,127.82-119.14,168.52-22.58,17.24-50.07,40.36-45.13,71.27,2.66,16.6,14.33,28.82,23,42.34,18.19,28.31,24.09,66.79,15.42,100.68-9.86,38.56-37.42,74.54-28.89,113.53,5.26,24,23.12,41.08,40.29,55.76q40.18,34.33,82.92,64.32c19.1,13.4,39.42,26.44,61.68,28.14,19.6,1.5,38.8-5.95,57.41-13.3,16.26-6.43,32.93-13.12,46.06-26,8.57-8.41,15.4-19.26,24.92-26.14,22.36-16.16,51.08-5.92,76.72,1,60.5,16.24,125.25,10.31,182.82-16.74,88.31-41.5,155.62-128.65,242-175.41,37.14-20.11,77.54-32.59,111.93-58.49a209.088,209.088,0,0,0,49.2-53.24c26-40.11,40.06-91.25,36-141.31s-26.81-98.3-62.54-126.56c-38.25-30.25-86.56-35.9-130.08-54.06-44.94-18.76-84.92-51-129.43-71.13A264.67,264.67,0,0,0,577.82,86c-48.44,24.5-97.54,81.66-151.49,86C387.89,175,310.9,136.52,280.88,172.94Z"
                fill="#6c63ff"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M913.25,505.83s17.5,34.35,3.24,53.14,1.3,70,1.3,70"
                fill="none"
                stroke="#6c63ff"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M918.43,495.14c0,6.62-4.86,12-4.86,12s-4.86-5.37-4.86-12,4.86-12,4.86-12S918.43,488.52,918.43,495.14Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M922.32,505.83s-17.5,34.35-3.24,53.14-1.3,70-1.3,70"
                fill="none"
                stroke="#6c63ff"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M917.14,495.14c0,6.62,4.86,12,4.86,12s4.86-5.37,4.86-12-4.86-12-4.86-12S917.14,488.52,917.14,495.14Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M917.14,495.14c0,6.62,4.86,12,4.86,12s4.86-5.37,4.86-12-4.86-12-4.86-12S917.14,488.52,917.14,495.14Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M929.45,505.83s17.5,34.35,3.24,53.14,1.3,70,1.3,70"
                fill="none"
                stroke="#6c63ff"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M934.63,495.14c0,6.62-4.86,12-4.86,12s-4.86-5.37-4.86-12,4.86-12,4.86-12S934.63,488.52,934.63,495.14Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M931.91,522.66c-4.59,4.77-11.82,5.27-11.82,5.27s.22-7.24,4.81-12,11.82-5.27,11.82-5.27S936.5,517.89,931.91,522.66Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M932.37,546.36c-4.59,4.77-11.82,5.27-11.82,5.27s.22-7.24,4.81-12,11.82-5.27,11.82-5.27S937,541.58,932.37,546.36Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M907.47,559.39c4.34,5,4.19,12.24,4.19,12.24s-7.19-.87-11.53-5.87-4.19-12.24-4.19-12.24S903.13,554.39,907.47,559.39Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M911.5,508.86c5.52,3.66,7.31,10.68,7.31,10.68s-7.16,1.09-12.68-2.57-7.31-10.68-7.31-10.68S906,505.2,911.5,508.86Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M911.5,508.86c5.52,3.66,7.31,10.68,7.31,10.68s-7.16,1.09-12.68-2.57-7.31-10.68-7.31-10.68S906,505.2,911.5,508.86Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M938.52,505.83S921,540.18,935.28,559s-1.3,70-1.3,70"
                fill="none"
                stroke="#6c63ff"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M933.34,495.14c0,6.62,4.86,12,4.86,12s4.86-5.37,4.86-12-4.86-12-4.86-12S933.34,488.52,933.34,495.14Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M933.34,495.14c0,6.62,4.86,12,4.86,12s4.86-5.37,4.86-12-4.86-12-4.86-12S933.34,488.52,933.34,495.14Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M934.74,523.17c-4.65,4.72-5,12-5,12s7.23-.41,11.88-5.13,5-12,5-12S939.38,518.45,934.74,523.17Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M934.74,523.17c-4.65,4.72-5,12-5,12s7.23-.41,11.88-5.13,5-12,5-12S939.38,518.45,934.74,523.17Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M943.85,556c-4.65,4.72-5,12-5,12s7.23-.41,11.88-5.13,5-12,5-12S948.5,551.26,943.85,556Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M930.44,548.77s10.53-16.6,25.32-17"
                fill="none"
                stroke="#6c63ff"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M922.24,538.74s-10.53-16.6-25.32-17"
                fill="none"
                stroke="#6c63ff"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M920.87,562.44s10.53-16.6,25.32-17"
                fill="none"
                stroke="#6c63ff"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(-71.15 -57.51)"
        />
        <circle
                cx="2.73"
                cy="2.73"
                fill="#6c63ff"
                r="2.73"
                transform="translate(881.4 471.44)"
        />
        <circle
                cx="2.73"
                cy="2.73"
                fill="#6c63ff"
                r="2.73"
                transform="translate(823.3 461.64)"
        />
        <path
                d="M928.62,567.91s-10.53-16.6-25.32-17"
                fill="none"
                stroke="#6c63ff"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(-71.15 -57.51)"
        />
        <circle
                cx="2.73"
                cy="2.73"
                fill="#6c63ff"
                r="2.73"
                transform="translate(829.68 490.81)"
        />
        <circle
                cx="2.73"
                cy="2.73"
                fill="#6c63ff"
                r="2.73"
                transform="translate(872.51 485.34)"
        />
        <path
                d="M986.84,609s-25.38-5.08-32.15-11.85S917.46,578.57,914.08,587s-45.69,37.23-16.92,42.31,67.69,6.77,76.15,3.38S986.84,609,986.84,609Z"
                fill="#a8a8a8"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M897.15,626.19c28.77,5.08,67.69,6.77,76.15,3.38,6.44-2.58,10.92-15,12.69-20.72l.85.18s-5.08,20.31-13.54,23.69-47.38,1.69-76.15-3.38c-8.3-1.47-10.68-4.91-9.78-9.26C888.08,622.91,891,625.1,897.15,626.19Z"
                opacity="0.2"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M138.4,645.66s8.38,11-3.87,27.5S112.18,703.67,116.27,714c0,0,18.48-30.73,33.52-31.16S154.94,664.14,138.4,645.66Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M138.4,645.66a13.57,13.57,0,0,1,1.71,3.44c14.68,17.24,22.49,33.33,8.39,33.74-13.14.38-28.9,23.87-32.68,29.81a12.74,12.74,0,0,0,.45,1.35s18.48-30.73,33.52-31.16S154.94,664.14,138.4,645.66Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M154,659.62c0,3.86-.43,7-1,7s-1-3.13-1-7,.54-2,1.07-2S154,655.77,154,659.62Z"
                fill="#ffd037"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M159.32,664.23c-3.39,1.85-6.34,3-6.59,2.49s2.28-2.35,5.67-4.19,2.05-.5,2.31,0S162.71,662.38,159.32,664.23Z"
                fill="#ffd037"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M94.13,645.66s-8.38,11,3.87,27.5,22.35,30.51,18.26,40.83c0,0-18.48-30.73-33.52-31.16S77.59,664.14,94.13,645.66Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M94.13,645.66a13.57,13.57,0,0,0-1.71,3.44C77.75,666.34,69.93,682.43,84,682.83c13.14.38,28.9,23.87,32.68,29.81a12.737,12.737,0,0,1-.45,1.35s-18.48-30.73-33.52-31.16S77.59,664.14,94.13,645.66Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M78.56,659.62c0,3.86.43,7,1,7s1-3.13,1-7-.54-2-1.07-2S78.56,655.77,78.56,659.62Z"
                fill="#ffd037"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M73.21,664.23c3.39,1.85,6.34,3,6.59,2.49s-2.28-2.35-5.67-4.19-2.05-.5-2.31,0S69.82,662.38,73.21,664.23Z"
                fill="#ffd037"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M81.22,712.55s23.43-.72,30.49-5.75,36-11,37.79-3,35.21,40.11,8.76,40.32S96.8,740,89.76,735.74,81.22,712.55,81.22,712.55Z"
                fill="#a8a8a8"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M158.74,741.35c-26.45.21-61.46-4.12-68.51-8.41-5.37-3.27-7.51-15-8.22-20.41h-.78s1.48,18.9,8.53,23.19,42.06,8.63,68.51,8.41c7.64-.06,10.27-2.78,10.13-6.8C167.34,739.79,164.42,741.3,158.74,741.35Z"
                opacity="0.2"
                transform="translate(-71.15 -57.51)"
        />
        <rect
                fill="#6c63ff"
                height="19"
                transform="translate(344.293 512.158) rotate(44.62)"
                width="60"
        />
        <rect
                height="19"
                opacity="0.1"
                transform="translate(344.293 512.158) rotate(44.62)"
                width="60"
        />
        <path
                d="M425.63,549.5S450.42,536.42,460,528a130.4,130.4,0,0,1,18.53-13.53l31.1-18.93,65-5,3,11s5,16-45,30-75,36-75,36S425.63,578.5,425.63,549.5Z"
                fill="#db8b8b"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M460.28,565.37c-1.77,1.35-2.65,2.13-2.65,2.13s-32,11-32-18c0,0,1.55-.82,4-2.19a27.67,27.67,0,0,0,23.58,13.19h3.39Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M576.69,507.06c-2.78,5.89-12.37,15.57-44.06,24.44-40.61,11.37-64.73,28-72.35,33.87-1.77,1.35-2.65,2.13-2.65,2.13s-32,11-32-18c0,0,1.55-.82,4-2.19,7.43-4.07,23.16-13,30.34-19.35a130,130,0,0,1,18.53-13.53l31.1-18.93,11.58-.89,53.42-4.11,3,11S578.3,503.65,576.69,507.06Z"
                opacity="0.05"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M426.13,543l-26.5,17.5,77.6,65s11.4-7,5.4-15c0,0-20-17-21-41l-6-8h-3.39A27.671,27.671,0,0,1,426.129,543Z"
                fill="#abb1d1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M576.69,507.06c-16-7.73-55.06-8.56-55.06-8.56l-.42-3.89,53.42-4.11,3,11S578.3,503.65,576.69,507.06Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M521.63,496.5s47,1,59,11l-6-121h-65Z"
                fill="#abb1d1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M521.63,496s47,1,59,11l-6-121h-65Z"
                opacity="0.05"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M559.39,422.46s117.65-4,120.05,11.21-117.65,8.8-117.65,8.8S552.19,432.86,559.39,422.46Z"
                fill="#535461"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M559.39,422.46s117.65-4,120.05,11.21-117.65,8.8-117.65,8.8S552.19,432.86,559.39,422.46Z"
                opacity="0.05"
                transform="translate(-71.15 -57.51)"
        />
        <circle
                fill="none"
                stroke="#c1272d"
                stroke-miterlimit="10"
                stroke-width="1"
                transform="translate(409.81 689.1)"
        />
        <rect
                fill="#6c63ff"
                height="180.88"
                transform="translate(372.19 425.78)"
                width="20.81"
        />
        <path
                d="M464.15,483.28v13.93c-6.92.9-14,1.47-20.81,1.69V483.28Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M462.59,468.29a77.732,77.732,0,0,1-8.7-1.17c-11.28-2.16-39.72-6.69-47.37,1-9.6,9.6-6.4,26.41,19.21,28.81s69.63-4,78.44-13.61C504.17,483.28,488.24,470.26,462.59,468.29Z"
                fill="#535461"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M387.4,475.4l218.5-68.03,7.2,16.81L389,501.01Z"
                fill="#6c63ff"
        />
        <path
                d="M452.23,582.65,609.9,427.38l8.8,12.8L468.23,597.05Z"
                fill="#6c63ff"
        />
        <path
                d="M724.73,587.36a87.775,87.775,0,0,0-14.56,5.17l-9.56-26.71a115.55,115.55,0,0,1,15.46-5.47Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M680.33,608.26l-11.2,6.4L590.99,396.33l-3.5-9.77,18.41-10.41,4.61,14.38Z"
                fill="#6c63ff"
        />
        <path
                d="M610.51,390.53l-19.52,5.8-3.5-9.77,18.41-10.41Z"
                opacity="0.1"
        />
        <path
                d="M562.59,464.88l109.65-37.62s19.21,2.4,12,18.41L573.8,478.48S557,480.08,562.59,464.88Z"
                fill="#535461"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M438.94,673.37a119.073,119.073,0,0,1-1.417,18.3C427.92,691,418,690,408.28,689a89.59,89.59,0,0,0-.79-35.88c9.47-2,19.14-3.91,28.58-5.64a117.568,117.568,0,0,1,2.87,25.89Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M464.15,642.89v21.27H443.34v-18C450.54,644.91,457.54,643.8,464.15,642.89Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M329.69,673s168.08-44,181.68-27.21S539.38,685,497,693s-170.48-13.61-170.48-13.61Z"
                fill="#6c63ff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M412.36,598.88,391,618.43a89,89,0,0,0-12.76-13.28l19.94-20.83a118.769,118.769,0,0,1,14.18,14.56Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M252.94,606.66,378.59,475.4l4.01,30.41L259.34,618.66Z"
                fill="#6c63ff"
        />
        <circle
                cx="12.81"
                cy="12.81"
                opacity="0.1"
                r="12.81"
                transform="translate(238.53 602.65)"
        />
        <g id="left-wheel">
            <path
                    d="M326.38,774.5l-6.27-97.94-.13-.21-6.32,2.2L284.58,763.4l-46.25-32.79L312,677.2l.07-.21.42-.15,4.75-3.45L240,630.08l-18.92,20.8,3,19.5L312.9,672v2l-90.48-1.62L219,650.24l25.9-28.46,38.75-45.2.69,1.87,8.2-9L323,574.91V573.7l2,.06v1.51l45.36,8.12L328.07,668l-6.17,4.47v1.06l97.8-34.08-11.61,91.21-83-52.87-2.92-1.64,48.94,78.88Zm-4-94.34,5.85,91.38,40.05-17.38ZM241.8,730.59l41.79,29.63,27.34-79.74Zm82.32-55.66,2.09,1.17,80.35,51.2,10.8-84.87Zm-7.1-7.2,2.51,4.05.44-.32,3-94.53-29.74-5.32-8.16,9ZM241.6,628.67l74.84,42-1.3-2.13-31.58-86.27-37.19,40.87ZM325,577.28,322,670l4.52-3.28,40.91-81.82Z"
                    fill="#535461"
                    transform="translate(-71.15 -57.51)"
            />
            <path
                    d="M436.07,647.46a117.22,117.22,0,0,0-23.71-48.58,117.885,117.885,0,1,0,26.58,74.49,117.568,117.568,0,0,0-2.87-25.91ZM320.88,761.81A88.75,88.75,0,1,1,408.28,689a88.75,88.75,0,0,1-87.4,72.81Z"
                    fill="#535461"
                    transform="translate(-71.15 -57.51)"
            />
            <circle
                    cx="12.81"
                    cy="12.81"
                    fill="#535461"
                    r="12.81"
                    transform="translate(236.92 602.65)"
            />
        </g>
        <circle
                cx="12.81"
                cy="12.81"
                opacity="0.1"
                r="12.81"
                transform="translate(662.32 601.45)"
        />
        <g id="right-wheel">
            <path
                    d="M752.57,774.9,746.31,677l-.13-.21-6.32,2.2L710.77,763.8,664.52,731l73.69-53.41.07-.21.42-.15,4.75-3.45-77.24-43.31-18.93,20.8,3,19.5,88.8,1.58v2l-90.48-1.62-3.4-22.11,25.89-28.45L709.81,577l.69,1.87,8.2-9,30.54,5.47v-1.21l2,.06v1.51l45.36,8.12-42.33,84.65-6.17,4.47V674l97.81-34.08L834.3,731.13l-83-52.87-2.92-1.64,48.94,78.89Zm-4-94.34,5.85,91.38,40.05-17.38ZM668,731l41.79,29.63,27.34-79.74Zm82.32-55.66,2.09,1.17,80.35,51.2,10.8-84.87Zm-7.1-7.2,2.51,4.05.44-.32,3-94.53L719.43,572l-8.16,9Zm-75.39-39.05,74.84,42-1.3-2.13-31.58-86.27-37.2,40.88ZM751.2,577.7l-3,92.69,4.52-3.28,40.91-81.82Z"
                    fill="#535461"
                    transform="translate(-71.15 -57.51)"
            />
            <path
                    d="M747.48,556.12a117.49,117.49,0,0,0-31.41,4.24,115.568,115.568,0,0,0-15.46,5.47,117.7,117.7,0,1,0,46.86-9.71Zm-.4,206.09a88.86,88.86,0,0,1-36.9-169.68,87.774,87.774,0,0,1,14.56-5.17,88.85,88.85,0,1,1,22.35,174.85Z"
                    fill="#535461"
                    transform="translate(-71.15 -57.51)"
            />
            <circle
                    cx="12.81"
                    cy="12.81"
                    fill="#535461"
                    r="12.81"
                    transform="translate(663.12 603.05)"
            />
        </g>
        <circle
                cx="25.61"
                cy="25.61"
                opacity="0.1"
                r="25.61"
                transform="translate(422.61 577.05)"
        />
        <circle
                cx="25.61"
                cy="25.61"
                fill="#535461"
                r="25.61"
                transform="translate(424.22 577.05)"
        />
        <rect
                fill="#6c63ff"
                height="47.54"
                rx="6.8"
                transform="matrix(0.801, -0.599, 0.599, 0.801, 437.538, 604.819)"
                width="13.61"
        />
        <path
                d="M636.63,304.5l-2.38,30.11a67.617,67.617,0,0,0,.15,11.45c.31,3.37,0,9.27-3.3,18.27a104.892,104.892,0,0,0-6.47,35.9V434.5l-23-4s1-48-3-56,3-79,3-79Z"
                fill="#db8b8b"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M635.45,319.42l-1.2,15.19a67.537,67.537,0,0,0,.16,11.44c.31,3.37,0,9.27-3.3,18.28a104.89,104.89,0,0,0-6.47,35.9V434.5l-23-4s1-48-3-56c-2.83-5.67-.15-42.94,1.66-64.21.75-8.75,1.34-14.79,1.34-14.79l35,9Z"
                opacity="0.05"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M522.65,534.5,514,583.7a112.206,112.206,0,0,1-5,19.3c-5.07,13.86-1.83,33.29.63,43.89,1.09,4.68,2,7.64,2,7.64l-26,14-8-6v-4l-1-89c-6-14-4-35,0-43a33.9,33.9,0,0,0,2.38-8.22c2-10.72,2.62-25.78,2.62-25.78s16-27,35-9c7,6.63,9.66,15.3,10.22,23.6C527.83,521.34,522.65,534.5,522.65,534.5Z"
                fill="#db8b8b"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M526.85,507.1c-13.87,8.24-38,10.57-47.84,11.18,2-10.72,2.62-25.78,2.62-25.78s16-27,35-9C523.63,490.13,526.29,498.8,526.85,507.1Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M560.63,482.5a182.994,182.994,0,0,0-30,20c-16,13-56,14-56,14v-15c1-18-16-38-39-94-14.69-35.76-10.62-56-5.76-66,2.76-5.65,5.76-8,5.76-8l125,24,4.47,13.4,9.53,28.6s-35,2-41,5-2,30-2,30C525.63,462.5,560.63,482.5,560.63,482.5Z"
                fill="#abb1d1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M636.63,304.5l-1.18,14.92c-17.4,5-29.82-10.92-29.82-10.92a45.8,45.8,0,0,0-5.34,1.79c.75-8.75,1.34-14.79,1.34-14.79Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M565.1,370.9c-.47,4.67-.47,7.6-.47,7.6s-23,19-62-10-74-21-74-21,.41-2.32,1.24-6c2.76-5.65,5.76-8,5.76-8l125,24Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M650.11,265.45c-2.61,20.35-3.48,46-3.48,46-23,18-41-5-41-5-21.72,5.83-31.93,24.84-36.74,41.43a110.761,110.761,0,0,0-4.26,27.57s-6.08,5-17.28,6c-10.59.89-25.77-1.87-44.72-16-39-29-74-21-74-21s7-40,23-52,28-27,48-73,61-35,61-35,2-9,39,0A60.27,60.27,0,0,1,624,197.36C643.79,214,653.39,239.81,650.11,265.45Z"
                fill="#fff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M613,207c-.05,1.33-.16,2.81-.34,4.46-2,18-4,16-26,20-13.68,2.49-20.79-8.94-24.21-17.94a56.628,56.628,0,0,1-2.79-10.06l48-8S613.41,193.85,613,207Z"
                fill="#db8b8b"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M568.89,347.93a110.761,110.761,0,0,0-4.26,27.57s-6.08,5-17.28,6a9.318,9.318,0,0,1-.72-2c-2-9-17-25-17-25-10-9-12-25-12-25L501.13,281l-5.5-4.5s23-18,43-3,14,47,14,47l4,11C560.78,335,565,341.17,568.89,347.93Z"
                opacity="0.05"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M498.63,275.5l22,53s2,16,12,25c0,0,15,16,17,25s30,33,30,33,26,24,28,33c0,0,24,7,21,21,0,0,18-18,0-35s-42-52-42-52-14-37-27-48l-4-11s6-32-14-47S498.63,275.5,498.63,275.5Z"
                fill="#db8b8b"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M511.63,654.5l-26,14-8-6v-4a60.56,60.56,0,0,0,25.55-7.71c2.4-1.36,4.66-2.7,6.47-3.88C510.7,651.54,511.63,654.5,511.63,654.5Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M541.63,233.5l14,55s-43,7-52,0-26-9-26-9,18-52,28-58S541.63,233.5,541.63,233.5Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M541.63,231.5l14,55s-43,7-52,0-26-9-26-9,18-52,28-58S541.63,231.5,541.63,231.5Z"
                fill="#fff"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M613,206a35,35,0,0,1-50.55,6.52,56.629,56.629,0,0,1-2.79-10.06l48-8S613.41,192.85,613,206Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M619.63,184.5a35,35,0,0,1-70,0v-1.74a35.011,35.011,0,1,1,70,1.74Z"
                fill="#db8b8b"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M619.63,184.5a34.89,34.89,0,0,1-1.32,9.54,13.47,13.47,0,0,1-8.08-.74c-3.74-1.47-6.95-4-10.54-5.82-10.11-5.12-22.14-4.11-33.34-2.42-5.56.84-11.87,1.62-16.24-1.92a5.108,5.108,0,0,1-.43-.38,35.011,35.011,0,1,1,70,1.74Z"
                opacity="0.1"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M524.31,162.85c2.13,6.68,12.36,6.89,17.61,11.81,2.73,2.56,4,6.34,7,8.62,4.17,3.18,10.19,2.48,15.5,1.72,10.69-1.52,22.17-2.43,31.81,2.18,3.42,1.63,6.49,3.91,10.06,5.24s8,1.49,10.9-.87c2.32-1.89,3.17-5,5.27-7.08,1.63-1.64,3.9-2.56,5.76-4,4.08-3.08,5.84-8.1,7-12.9,2.4-9.88,3.09-20.53-1-29.9s-13.93-17-24.67-16.51c-3.29.15-6.5,1-9.78,1.36-8.55.87-17-2-25.58-2.49a14.57,14.57,0,0,0-7.33,1,13.379,13.379,0,0,0-4.41,3.95,42.13,42.13,0,0,0-7.86,17.48c-1,4.79-1.05,9.13-6.92,9.87C541,153.21,519.8,148.72,524.31,162.85Z"
                fill="#472727"
                transform="translate(-71.15 -57.51)"
        />
        <path
                d="M456.13,292s26,7,34,25,29.64,21.29,29.64,21.29"
                opacity="0.05"
                transform="translate(-71.15 -57.51)"
        />
        <rect
                fill="#6c63ff"
                height="19"
                transform="translate(422.98 630.49)"
                width="60"
        />
        <rect
                height="19"
                opacity="0.1"
                transform="translate(422.98 630.49)"
                width="60"
        />
        <path
                d="M503.14,652.74c5.37-3,10.08-6,10.49-7.24,0,0,13.55,14.3,25.62,18.74A60.393,60.393,0,0,1,550.84,670c4.78,3,11.6,6.53,17.79,6.53,0,0,14,12-13,14s-90-5-90-5l4-25h0A60.77,60.77,0,0,0,503.14,652.74Z"
                fill="#abb1d1"
                transform="translate(-71.15 -57.51)"
        />
    </g>
</svg>


<script>
  /* 下面的代码和CSS动画有冲突
  // 将left-wheel设置为绕着中心旋转
  gsap.set("#left-wheel", {transformOrigin: "center center"});
  gsap.set("#right-wheel", {transformOrigin: "center center"});
  // 创建动画 inertia为惯性插件
  Draggable.create("#left-wheel", {type: "rotation", inertia: true});
  Draggable.create("#right-wheel", {type: "rotation", inertia: true});*/
</script>
</body>
</html>
```

# 3. 最后

HTML中动画的制作是一件非常耗时的过程，同时也非常考验一个开发者对于CSS的掌握程度，因为大多数关于动画的CSS属性在我们平时的编写网页中都不会用到，所以平时积累HTML动画的知识就显得很重要，万一有一天你要开发一个网页就会用到呢~
