---
title: "利用background制作拉窗帘效果"
date: 2020-06-02
categories:
  - "web开发"
tags:
  - "Web 开发"
  - "HTML+CSS"
  - "CSS"
---


往往利用CSS可以做出一些意想不到的效果，比如说下面的这个示例：

![img](/images/v2-1c842c67816442adf6c8ba34c6b7411a_b.webp)

为了演示随手做的，官网并不是这样

滚动鼠标滚轮，就有一种背景图被覆盖的错觉，之所以说是错觉，并不是背景图真的被覆盖了，其实背景图所在的盒子是跟着下面的图片一起在滚动，只是图片和窗口之间相对没有移动造成的错觉。

可能听起来比较复杂，但是实际上只需要一个属性就能做出这种效果，那就是`background-attachment: fixed;`

还是同一个案例，我们将背景图片去掉，看看效果就明白了。

![img](/images/v2-0b2fa904729127861768553783f16c26_b.webp)

# background-attachment

CSS 属性决定背景图像的位置是在视口内固定，或者随着包含它的区块滚动。

- `fixed`此关键属性值表示背景相对于视口固定。即使一个元素拥有滚动机制，背景也不会随着元素的内容滚动。
- `local`此关键属性值表示背景相对于元素的内容固定。如果一个元素拥有滚动机制，背景将会随着元素的内容滚动， 并且背景的绘制区域和定位区域是相对于可滚动的区域而不是包含他们的边框。
- `scroll`此关键属性值表示背景相对于元素本身固定， 而不是随着它的内容滚动（对元素边框是有效的）。

# 资料参考

https://developer.mozilla.org/zh-CN/docs/Web/CSS/background-attachment
