---
title: "前端开发过程中，好的开源项目和第三方库整理"
date: 2020-9-17 23:07:02
categories:
  - "web开发"
tags:
  - "游戏设计"
---


# 桌面开发

[Waypoints.js](http://imakewebthings.com/waypoints/)：滚动到一个元素时候触发事件。

[gitalk](https://github.com/gitalk/gitalk)：添加评论系统。

[electron](https://github.com/electron/electron)：可以通过开发网页的形式开发桌面应用，VSCode就是用这个项目开发的。

[electron-react-boilerplate](https://github.com/electron-react-boilerplate/electron-react-boilerplate)：使用react模板创建electron桌面项目。

[electron-vue](https://github.com/SimulatedGREG/electron-vue)：使用vue模板创建electron桌面项目，缺点是该项目很久没有更新，依赖的electron的版本还在`2.x`。

[nw.js](https://github.com/nwjs/nw.js)：（原名 node-webkit）是一个基于 `Chromium` 和 `node.js` 的应用运行时，通过它可以用 HTML 和 JavaScript 编写原生应用程序。它还允许您从 DOM 调用Nodejs的模块 ，实现了一个用所有 Web 技术来写原生应用程序的新的开发模式。

[meteor](https://github.com/meteor/meteor)：Meteor或MeteorJS是使用Node.js编写的免费和开源的同构JavaScript Web框架。Meteor允许快速进行原型设计并生成跨平台代码。它与MongoDB集成在一起，并使用分布式数据协议和发布-订阅模式将数据更改自动传播到客户端，而无需开发人员编写任何同步代码。

# 页面布局

[glide](https://github.com/glidejs/glide)：轮播图。

[lib-flexible](https://github.com/amfe/lib-flexible)：自适应布局rem文件，如果需要使用到自适应，推荐直接引用该文件然后使用rem代替px，不过随着vw，vh得到越来越多的浏览器支持，现在推荐使用vw作为自适应单位。

[viewerjs](https://github.com/fengyuanchen/viewerjs)：图片查看器，附加了很多查看图片时需要用到的功能和选项，如果自己手写可能要花很久，而通过这个库就可以很快的实现图片查看器。

[vue-grid-layout](https://github.com/jbaysolutions/vue-grid-layout)：是一个类似于Gridster的栅格布局系统，适用于Vue.js。

[vue-waterfall-easy](https://github.com/lfyfly/vue-waterfall-easy)：一个vue组件，包含瀑布流布局和无限滚动加载。

[vue-virtual-collection](https://github.com/starkwang/vue-virtual-collection)：vue瀑布流。

[vue-waterfall](https://github.com/MopTym/vue-waterfall)：还是vue瀑布流。

# 实用工具

[refined-github](https://github.com/sindresorhus/refined-github)：一款 GitHub 的浏览器插件，支持 Chrome和Firefox。这款插件给 GitHub 增加了非常多强大的功能，比如一键合并冲突修复。

[Highlight.js](https://highlightjs.org/)：页面内容的语法高亮。

[bignumber.js](https://github.com/MikeMcl/bignumber.js)：JavaScript因为存在计算的精度问题，比如 `0.1+0.2!=0.3`，bignumber.js是一个用于任意精度计算的js库，经常操作金额计算的朋友不容错过。

[moment](https://github.com/moment/moment)：Moment.js是一个轻量级的JavaScript时间库，它方便了日常开发中对时间的操作，提高了开发效率，提供了获取时间、设置时间、格式化时间、比较时间等功能，并且提供多地区、语言支持。

[lodash](https://github.com/lodash/lodash)：lodash是一个一致性、模块化、高性能的JavaScript实用工具库，不需要引入其他第三方依赖，定义了非常多实用且高性能的方法，不限于操作数组、集合 、日期、函数、对象、数学，而且可以统一方法的一致性。Lodash使用了一个简单的`_`符号作为所有方法的前缀。

[underscore](https://github.com/jashkenas/underscore)：underscore和lodash类似，Underscore是一个JavaScript实用库，提供了一整套函数式编程的实用功能，但是没有扩展任何JavaScript内置对象。Underscore提供了100多个函数，包括常用的：**map**， **filter**， **invoke**，当然还有更多专业的辅助函数，如：函数绑定， JavaScript模板功能，创建快速索引， 强类型相等测试， 等等。

[ramda](https://github.com/ramda/ramda)：和lodash、underscore 类似，ramda是一个函数式编程风格的JavaScript工具包，同样提供了非常多的有用的方法。

[outils](https://github.com/proYang/outils)：outils是一个前端业务代码工具库，提供了日期格式化、url参数转对象、浏览器类型判断、节流函数等常用函数，文档更为本土化，能解决你很多实际开发中经常遇到的需求。（实际功能和lodash，moment有所冲突，而后两者人气太高了。）

[js-cookie](https://github.com/js-cookie/js-cookie)：js-cookie是一个简单的，轻量级的处理cookie的工具，有了它，操作cookie将变得非常简单。

[tough-cookie](https://github.com/salesforce/tough-cookie)：操作cookie。

[basket.js](https://github.com/addyosmani/basket.js)：basket.js是一个文件缓存插件，使用本地储存`localStorage`缓存`JavaScript`文件，第一次访问页面过后会将静态文件缓存到本地，下次打开直接读取本地。

[sheetjs](https://github.com/SheetJS/sheetjs)：前端操作Excel以及类似的二维表的最佳选择之一（体验还是很好滴，配合js的爬虫将爬取的数据保存为excal）。

[validator.js](https://github.com/validatorjs/validator.js)：validator.js是一个对字符串进行数据验证和过滤的工具库，同时支持Node端和浏览器端，用来做表单验证和数据比较最好不过。

[chroma.js](https://github.com/gka/chroma.js)：颜色转换插件。

[cheerio](https://github.com/cheeriojs/cheerio)：简易版的jQuery（可以用在js爬虫）。

[ws](https://github.com/websockets/ws)：展示爬取过程。

[request](https://github.com/request/request)：让Node.js http请求变得超简单，（现在可以使用axios）。

[bluebird](https://github.com/petkaantonov/bluebird)：Promise库。

[puppeteer](https://github.com/puppeteer/puppeteer)：谷歌官方出品的一个通过DevTools协议控制headless Chrome的Node库。可以通过Puppeteer的提供的api直接控制Chrome模拟大部分用户操作来进行UI Test或者作为爬虫访问页面来收集数据。

# 图标资源

[测试用的图片](https://picsum.photos/)。

# 服务开发

[express](https://github.com/expressjs/express)：Nodejs开发服务端最主流的框架之一。

[koa](https://github.com/koajs/koa)：Nodejs开发服务端最主流的框架之一，虽然在使用率上远低于express，但据说体验和性能高于express。

[async](https://github.com/caolan/async)：异步和并发控制库。

# 过渡动画

[SpinKit](https://github.com/tobiasahlin/SpinKit)：纯 CSS 实现加载动画的项目。

[Typed.js](http://www.mattboldt.com/demos/typed-js/)：打字机效果，可以非常简单的在界面上展示打字机。

[smooth-scroll](https://github.com/cferdinandi/smooth-scroll)：页面中的平滑滚动，让通过锚点进行跳转到页面某一位置显得更加平滑。

[scrollreveal](https://github.com/jlmakes/scrollreveal)：滑动到某一个元素通过动画展示。

[masonry](https://github.com/desandro/masonry)：页面瀑布流，可以参考[HTML中的瀑布流masonry在Vue中应用](/2020/06/12/web开发/html中的瀑布流masonry在vue中应用/)。

# 最后

本篇文章会在：[前端开发过程中，好的开源项目和第三方库整理](/2020/09/17/web开发/前端开发过程中好的开源项目和第三方库整理/)中持续更新。
