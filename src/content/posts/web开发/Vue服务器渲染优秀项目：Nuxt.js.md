---
title: "Vue服务器渲染优秀项目：Nuxt.js"
date: 2020-06-22
categories:
  - "Vue"
tags:
  - "服务端渲染"
---


# 1. 服务器渲染

## 1. 1 什么是服务器渲染

[Vue.js 服务器端渲染指南](https://ssr.vuejs.org/zh/#%E4%BB%80%E4%B9%88%E6%98%AF%E6%9C%8D%E5%8A%A1%E5%99%A8%E7%AB%AF%E6%B8%B2%E6%9F%93-ssr-%EF%BC%9F)

Vue官网已经对服务器渲染讲解的很详细了，可以直接参考上面的文档。不过就是因为讲解的太过于全面，所以有时候可能看的一头雾水。

## 1.2 为什么需要服务器渲染

**更好的 SEO，搜索引擎的爬虫抓取工具可以直接查看完全渲染的页面。**

这段话是什么意思呢，大家可以打开自己的Vue项目，右键查看源代码：

![img](/images/v2-28299477732b658e149ce2d986203754_720w.png)

可以看到，不管你的Vue项目有多庞大，作为入口的`index.html`一般就长成上面这个样子，页面中的`DOM`元素是由`JavaScript`进行生成，无法在`index.html`中直接查看到。

就是因为无法在`index.html`中直接查看到完全渲染的页面，尤其是在页面上的内容使用`ajax`动态获取渲染时，**搜索引擎的爬虫就很难爬取得到你页面上的内容，不利于搜索引擎对你的页面进行排名 。**

如果你并不是指望着通过网站带来的广告费赚钱，那么完全不需要使用服务器渲染，当你想通过网站的流量获得广告费时，服务器渲染就显得特别重要，因为完全渲染的页面才有利于搜索引擎爬虫的爬取。不信你可以看看现在的主流网站，全是服务器渲染。



**更快的内容到达时间 (time-to-content)**

对于缓慢的网络情况或运行缓慢的设备。无需等待所有的 JavaScript 都完成下载并执行，才显示服务器渲染的标记，所以你的用户将会更快速地看到完整渲染的页面。通常可以产生更好的用户体验。

没有人能够忍受打开一个网站需要长达10多秒的响应时间，服务器渲染也解决了这个痛点。

# 2. Nuxt.js

如果你想要从头搭建服务端渲染，那么需要耗费大量的精力去进行研究，而`Nuxt.js`的出现，让一切都变得很简单。

https://zh.nuxtjs.org/

# 3. 创建项目

Nuxt.js团队创建了脚手架工具[create-nuxt-app](https://github.com/nuxt/create-nuxt-app)。

```powershell
npx create-nuxt-app <项目名>
yarn create nuxt-app <项目名>

运行
npm run dev
```

# 4. 目录结构

`assets` 用于组织未编译的静态资源如 LESS、SASS 或 JavaScript。

`components` 用于组织应用的 Vue.js 组件。

`layouts` 用于组织应用的布局组件。

`middleware` 用于存放应用的中间件。

`pages` 页面目录，用于组织应用的路由及视图。

`plugins` 插件目录。

`static` 存放应用的静态文件，此类文件不会被 Nuxt.js 调用 Webpack 进行构建编译处理。

`store` 用于组织应用的 Vuex 状态树 文件。

`nuxt.config.js`文件用于组织Nuxt.js 应用的个性化配置，以便覆盖默认配置。

`package.json`文件用于描述应用的依赖关系和对外暴露的脚本接口。

# 5. 创建页面

到了这一步开始，几乎就和正常的使用Vue创建项目差不多，将页面放在pages中，`Nuxt.js`会自动找到`index.vue`作为入口文件。

如果是之前创建的项目，直接将项目按照目录文件逐渐移动过来。

# 6. asyncData fetch

https://zh.nuxtjs.org/guide/async-data/

上面的问题解决后，还存在一个问题，就是通过`ajax`取得的数据然后进行渲染的`DOM`元素并没有出现在`html`文件上， 解决这个问题就需要使用到`asyncData`，`fetch`。

**注：需要写在第一个入口文件中。**

在这两个方法中都无法调用`this`获取**组件实例，**因为它们是在**组件初始化之前**被调用。

- `asyncData` 可以在设置组件的数据之前能异步获取或处理数据。
- `fetch` 用于在渲染页面前填充应用的状态树（store）数据， 与`asyncData`方法类似，不同的是它不会设置组件的数据。

# 7. 部署上线

## 7.1 服务端渲染应用部署（常用）

因为我的服务上装了宝塔面板，强烈推荐宝塔面板！很多东西都是一键安装，非常方便。

https://bt.cn/

**第一步**

打包应用`npm run build` 。

**第二步**

在宝塔面板中的`网站`-`添加网站`，如果是要部署到服务器，那么相信大家都对服务器有一定了解。

添加网站后在`根目录`可以看到一个文件夹地址，将项目中的`.nuxt`，`static`，`nuxt.config.js`，`package.json`拷贝到该文件夹中。

**第三步**

服务器中安装`pm2` 直接在宝塔面板的`软件商店`里面搜索后安装。安装完成后进入到刚才拷贝项目的文件夹中，运行`npm install`。

**第四步**

等待`npm`运行完成后，运行项目：

```powershell
pm2 start npm --name "nuxt-name" -- run start
```

`nuxt-name`为`package.json`中的项目名称。

**第五步**

如果上面的步骤都没有问题的话，就开启反向代理，在宝塔面板中`网站`-`设置`-`反向代理`，`目标url`中填写`http://localhost:3000`，`发送域名`中就填写你自己的域名。 到这里，项目就部署完毕了。

## 7.2 静态应用部署

Nuxt.js 可依据路由配置将应用静态化，使得我们可以将应用部署至任何一个静态站点主机服务商。

可利用下面的命令生成应用的静态目录和文件：

```text
npm run generate
```

这个命令会创建一个 `dist` 文件夹，所有静态化后的资源文件均在其中。

注意：`nuxt generate`在 build/generate 时间内仍然需要SSR引擎，**而且如果首页是动态获取数据的话，那千万别用这种方式打包，因为如果数据发生了改变，你就必须要重新进行打包。**

## 7.3 单页面应用程序部署 (SPA)

使用时启用SPA模式`mode: 'spa'`或`--spa`。

- 将`nuxt.config.js`中的`mode`更改为`spa`。
- 运行 `npm run build`.
- 自动生成`dist/`文件夹，部署到您的服务器，如`Surge`，`GitHub Pages`或`nginx`。

注意：这种方式打包出来的文件就和`Vue`直接打包出来的文件没有什么区别，最主要的用途就是将项目部署到`GitHub Pages`。

# 8. 错误

## 8.1 `Interface 'NuxtApp' incorrectly extends interface 'Vue'`

https://github.com/nuxt/typescript/issues/49

可能是由于`element-ui`的`$loading`和`nuxt.js` 中的`$loading`冲突导致。

解决方法：

在`tsconfig.json`添加：

```json
"skipLibCheck":true,
```

## 8.2 `window is not defined`

一些只兼容客户端的脚本被打包进了服务端的执行脚本中去。 对于只适合在客户端运行的脚本，需要通过使用`process.client`变量来判断导入。比如我要引入`smooth-scroll`这个页面平滑滚动插件，就需要写在`if`条件判断中，不然就会报错。

```js
created () {
  if (process.client) {
    const SmoothScroll = require('smooth-scroll')
    const scroll = new SmoothScroll('a[href*="#"]')
  }
}
```

# 9. 总结

使用`Nuxt.js`最主要的原因就是为了`SEO`给网站带来更高的流量，提高网页的打开速度，如果不靠着网站盈利，我个人觉得完全没有必要使用服务器渲染，因为服务器渲染给服务器带来更大的压力，增加服务器的成本。
