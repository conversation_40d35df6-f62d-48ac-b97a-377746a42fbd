---
title: "提高处理Excel效率的终极神器-VBA初步入门"
date: 2020-6-29 20:44:16
categories:
  - "Excel"
tags:
  - "宏命令"
  - "VBA"
---


# 1. 前言

## 1.1 什么是VBA？

`Visual Basic For Application(VBA)`可以认为 VBA 是非常流行的应用程序开发语言`VASUAL BASIC`的子集。实际上VBA是"寄生于"VB应用程序的版本。

也就是说为了自动化需要，微软开发出的一款对于自家的产品（例如：Excel，Word等）的一种通用的自动化语言。也就是说可以帮助人们完成对Excel的一些重复性操作。

## 1.2 为什么要学习VBA？

相信不管是什么岗位，在现在这个年代，或多或少都会跟Excel打交道，而VBA比起Excel的公式来说更具有学习成本，但是VBA比Excel的公式更为强大，因为它本身就是一种**脚本语言**。

## 1.3 VBA能做到什么？

对于Excel而言你能做到的，它都能做到，同时它能比你完成的更快，更好。如果是重复性的工作交给人来办，多多少少都会有错误，但是你交给VBA来办，只要在你代码编写正确的情况下，就算是再多的数据，它都不可能出错。

## 1.4 感受VBA

![image-20200629132556351](/images/image-20200629132556351.png)

这是最近在做的一个游戏数据排行榜，有兴趣的话可以到[游戏排行榜](https://www.cclliang.com/categories/%E6%B8%B8%E6%88%8F%E6%8E%92%E8%A1%8C%E6%A6%9C/)进行查看。

### 1.4.1 需求

我的需求是右边的数据对比左边的数据，比如说**迷你世界**，右边的数据比左边的数据下降了2个排名，那我就需要在右边的数据中标出`↓2`并且将它们的文字颜色更改为绿色。

而对于**我的世界**这种排行上升的游戏我也需要在右边的数据中标出`↑1`并将文字改变成红色。

对于**QQ飞车手游**这种前一天排行榜上面没有的游戏就标记为`↑new`就跟下面的图一样：

![image-20200629133034681](/images/image-20200629133034681.png)

如果对于你来说，没有学习过VBA那么怎么进行操作？右边对着左边一项一项的比对，然后就行标记？没错，我前2天就是这样，但是后面觉得麻烦，编写了一段VBA代码实现自动化，下面是效果：

![](/images/sort.gif)

可以看到几乎在几秒钟的时间内，就完成了我们一项一项去比对少花费数分钟或者数十分钟的时间，而且自行比对还容易出错。

# 2. Excel宏命令

## 2.1 调出开发工具

如果你只是要实现简单的自动化，那么使用录制宏功能就可以了。

首先调出**开发工具**，点击`文件`-`选项`-`自定义功能区`

![image-20200629161719166](/images/image-20200629161719166.png)

勾选后点击`确定`

![image-20200629161849883](/images/image-20200629161849883.png)

可以看到主界面上出现了一个开发工具。

**注：如果是盗版office可能会出现打不开开发工具的情况。**

## 2.2 录制宏

点击录制宏，会将你所有的动作录制下来，下次再运行宏时会重复你这次录制的操作。

![](/images/录制宏.gif)

## 2.3 运行宏

![](/images/运行宏.gif)

## 2.4 编辑宏

编辑宏的方式是在上面的运行宏时，选择`编辑`

![image-20200629162624012](/images/image-20200629162624012.png)

打开后会看到下面的界面

![image-20200629162653844](/images/image-20200629162653844.png)

# 3. 修改宏命令

如果你想用宏实现更为复杂的功能，那就需要学习下面的知识，**上面那仅仅是录制宏，下面开始才是真正的VBA知识。**

## 3.1 标识符

标识符是一种标识变量、常量、过程、函数、类等语言构成单位的符号，利用它可以完成对变量、常量、过程、函数、类等引用。

### 3.1.1 命名规则

- 字母打头，由字母、数字和下划线组成，如`A987b_23Abc`
- 字符长度小于40，（Excel2002以上中文版本等，可以用汉字且长度可达254个字符）
- 不能与VB保留字重名，如`public，private，dim，goto，next，with，integer，single`等
- 尽量见名知意，比如名字命名为`name`，而不要命名为`n`或者其它不知道含义的名字。

## 3.2 运算符

- 赋值运算符  `=`
- 数学运算符 `&、+ (字符连接符)、+(加)、-（减）、Mod（取余）、\（整除）、*（乘）、/（除）、-（负号）、^（指数）`
- 逻辑运算符 `Not（非）、And（与）、Or(或)、Xor(异或)、Eqv(相等)、Imp(隐含)`
- 关系运算符 `= （相同）、<>（不等）、>（大于）、<（小于）、>=（不小于）、<=（不大于）、Like、Is`
- 位运算符（不常用）  `Not（逻辑非）、And（逻辑与）、Or（逻辑或）、Xor（逻辑异或）、Eqv（逻辑等）、Imp（隐含）` 

## 3.3 数据类型

| 数据类型         | 类型标识符 | 字节                    |
| ---------------- | ---------- | ----------------------- |
| String 字符串型  | $          | 字符长度(0-65400)       |
| Byte 字节型      | 无         | 1                       |
| Boolean 布尔型   | 无         | 2                       |
| Integer 整数型   | %          | 2                       |
| Long 长整数型    | &          | 4                       |
| Single 单精度型  | !          | 4                       |
| Double 双精度型  | #          | 8                       |
| Date 日期型      | 无         | 8 公元 100/1/1-99/12/31 |
| Currency 货币型  | @          | 8                       |
| Decimal 小数点型 | 无         | 14                      |
| Variant 变体型   | 无         | 以上任意类型，可变      |
| Object 对象型    | 无         | 4                       |

**PS：这一部分了解就行了，如果不是要做更深入的研究用处不大。**

## 3.4 变量与常量

### 3.4.1 未定义变量

**VBA允许使用未定义的变量，默认是变体变量**例如：

```vb
Sub test()

a = 100
MsgBox a

End Sub
```

### 3.4.2 强制定义变量

**在模块通用说明部分，加入Option Explicit语句可以强迫用户进行变量定义**

### 3.4.3变量定义语句及变量作用域

`Dim 变量 as 类型`定义为局部变量，如`Dim xyz as integer` 

`Private 变量 as 类型`定义为私有变量，如`Private xyz as byte` 

`Public 变量as 类型`定义为公有变量，如`Public xyz as single` 

`Global 变量as 类型`定义为全局变量，如`Globlal xyz as date` 

`Static 变量 as 类型`定义为静态变量，如`Static xyz as double`

一般变量作用域的原则是，那部分定义就在那部分起作用，模块中定义则在该模块那作用。

### 3.4.4 常量

常量为变量的一种特例，用`Const`定义，且定义时赋值，程序中不能改变值，作用域也如同变量作用域。如下定义：`Const Pi As Single = 3.1415926`

## 3.5 数组

数组是包含相同数据类型的一组变量的集合，对数组中的单个变量引用通过数组索引下标进行。在内存中表现为一个连续的内存块，必须用`Global`或`Dim`语句来定义。定义规则如下：

`Dim 数组名([lower to ]upper [, [lower to ]upper, ….]) as type ;Lower`缺省值为0。二维数组是按行列排列，如`XYZ(行，列)`。

除了以上固定数组外，`VBA` 还有一种功能强大的动态数组，定义时无大小维数声明；在程序中再利用`Redim`语句来重新改变数组大小，原来数组内容可以通过加`preserve`关键字来保留。

## 3.6 注释和赋值语句

注释语句是用来说明程序中某些语句的功能和作用；

VBA中有两种方法标识为注释语句。

- 单引号 `’`  ;如：`’定义全局变量；`可以位于别的语句之尾，也可单独一行
- `Rem`如：`Rem定义全局变量；`只能单独一行

```vb
Sub 宏1()
'
' 宏1 宏
'
Rem 定义全局变量
'数学运算符
a = 1
b = 2

End Sub
```

赋值语句是进行对变量或对象属性赋值的语句，采用赋值号 `=`，如`X=123`,`Form1.caption=”我的窗口”`

对对象的赋值采用：`set myobject=object` 或  `myobject:=object`

## 3.7 书写规范

- VBA不区分标识符的字母大小写，一律认为是小写字母；
- 一行可以书写多条语句，各语句之间以冒号 :  分开；
- 一条语句可以多行书写，以空格加下划线 `_` 来标识下行为续行；
- 标识符最好能简洁明了，不造成歧义。

## 3.8 判断语句

### 3.8.1 If…Then…Else语句（最常用）

```vb
If 条件 Then
表达式
ElseIf 条件 Then
表达式
Else
表达式
End If

例如
If Number < 10  Then
     Digits = 1
ElseIf Number < 100  Then
     Digits = 2
Else
     Digits = 3
End If
```

### 3.8.2 Select Case…Case…End Case语句

```bv
Select Case  Pid
   Case  “A101”
   Price=200
   Case  “A102”
   Price=300
   ……
   Case Else
   Price=900
End Case
```

### 3.8.3 Choose 函数

`choose(index, choce-1,choice-2,…,choice-n)`，可以用来选择自变量串列中的一个值，并将其返回，index 必要参数，数值表达式或字段，它的运算结果是一个数值，且界于 1 和可选择的项目数之间。

`choice` 必要参数，`Variant`表达式，包含可选择项目的其中之一。如：`GetChoice = Choose(Ind, "Speedy", "United", "Federal")`

### 3.8.4 Switch函数

`Switch(expr-1, value-1[, expr-2, value-2 _ [, expr-n,value-n]])switch` 函数和 `Choose` 函数类似，但它是以两个一组的方式返回所要的值，在串列中，最先为`TRUE` 的值会被返回。

 `expr` 必要参数，要加以计算的 `Variant` 表达式。`value` 必要参数。如果相关的表达式为 `True`，则返回此部分的数值或表达式，没有一个表达式为 `True`，`Switch` 会返回一个 `Null`值。

## 3.9 循环语句

### 3.9.1For Next语句

以指定次数来重复执行一组语句

```vb
For counter = start To end [Step step]                  ＇step 缺省值为1
[statements]
[Exit For]
[statements]
Next [counter]

如1：
For Words = 10 To 1 Step  -1                               ＇建立 10 次循环 
      For Chars = 0 To 9                                        ＇建立 10 次循环
           MyString = MyString & Chars                      ＇将数字添加到字符串中
      Next Chars                                                   ＇Increment counter
      MyString = MyString & " "                                ＇添加一个空格
Next  Words
```

### 3.9.2 For Each…Next 语句

主要功能是对一个数组或集合对象进行，让所有元素重复执行一次语句

```vb
For Each element In  group
Statements
[Exit for]
Statements
Next  [element]

如1：
For Each rang2 In  range1
     With range2.interior
             .colorindex=6
             .pattern=xlSolid
     End with
Next
```

### 3.9.3 其他循环语句

因为不介意使用所以这里就不过多介绍了。

## 3.10 错误语句处理

执行阶段有时会有错误的情况发生，利用OnError语句来处理错误，启动一个错误的处理程序。

语法如下：

`On Error Goto Line`     当错误发生时，会立刻转移到line行去
`On Error Resume Next`   当错误发生时，会立刻转移到发生错误的下一行去
`On Erro Goto 0`       当错误发生时，会立刻停止过程中任何错误处理过程

## 3.11 过程和函数

过程是构成程序的一个模块，往往用来完成一个相对独立的功能。过程可以使程序更清晰、更具结构性。VBA具有四种过程：Sub 过程、Function函数、Property属性过程和Event事件过程。

### 3.11.1 Sub过程

Sub 过程的参数有两种传递方式：按值传递(ByVal)和按地址传递(ByRef)。如下例：

```vb
Sub password (ByVal x as integer, ByRef y as integer)
   If y=100 then y=x+y else y=x-y
   x=x+100
End sub
Sub call_password ()
   Dim x1 as integer
   Dim y1 as integer
   x1=12
   y1=100
   Callpassword(x1,y1)   ‘调用过程方式：1.Call  过程名(参数1,  参数2…);2.  过程名 参数1, 参数2…
   debug.print x1,y1     ‘结果是12、112，y1按地址传递改变了值，而x1按值传递，未改变原值
End sub
```

### 3.11.2 Function函数

函数实际是实现一种映射，它通过一定的映射规则，完成运算并返回结果。参数传递也两种：按值传递(ByVal)和按地址传递(ByRef)。如下例：

```vb
Function password(ByVal x as integer, byref y as integer) as boolean
    If y=100 then y=x+y else y=x-y
    x=x+100
    if y=150 then password=true else password=false
End Function
Sub call_password ()
   Dim x1 as integer
   Dim y1 as integer
   x1=12
   y1=100
   if password then ‘调用函数：1. 作为一个表达式放在=右端 ; 2. 作为参数使用
   debug.print x1
   end if
End sub
```

### 3.11.3 Property属性过程和Event事件过程

这是VB在对象功能上添加的两个过程，与对象特征密切相关，也是VBA比较重要组成，技术比较复杂，可以参考相关书籍。

## 3.12 内部函数

在VBA程序语言中有许多内置函数，可以帮助程序代码设计和减少代码的编写工作。

### 3.12.1 测试函数

`IsNumeric(x)`      ‘是否为数字,  返回Boolean结果，True  or False

`IsDate(x)`        ‘是否是日期,  返回Boolean结果，True  or False

`IsEmpty（x）`     ‘是否为Empty, 返回Boolean结果，True  or False

`IsArray(x)`       ‘指出变量是否为一个数组。

`IsError(expression)`  ‘指出表达式是否为一个错误值

`IsNull(expression)`   ‘指出表达式是否不包含任何有效数据 (Null)。

`IsObject(identifier)`  ‘指出标识符是否表示对象变量

### 3.12.2 数学函数

`Sin(X)`、`Cos(X)`、`Tan(X)`、`Atan(x)`  三角函数，单位为弧度

`Log(x)` 返回x的自然对数

`Exp(x)`返回 ![img](https://atts.w3cschool.cn/attachments/day_161019/201610190955366418.png)

`Abs(x)` 返回绝对值

`Int(number)`、`Fix(number)` 都返回参数的整数部分，区别：`Int` 将 -8.4 转换成 -9，而 `Fix` 将-8.4 转换成 -8

`Sgn(number)` 返回一个 `Variant (Integer)`，指出参数的正负号

`Sqr(number)` 返回一个 `Double`，指定参数的平方根

`VarType(varname)` 返回一个 `Integer`，指出变量的子类型

`Rnd（x）`返回0-1之间的单精度数据，x为随机种子

### 3.12.3 字符串函数

`Trim(string)`去掉string左右两端空白

`Ltrim(string)`去掉string左端空白

`Rtrim(string)`去掉string右端空白

`Len(string)`计算string长度

`Left(string, x)`取string左段x个字符组成的字符串

`Right(string, x)`取string右段x个字符组成的字符串

`Mid(string, start,x)`取string从start位开始的x个字符组成的字符串

`Ucase(string)`转换为大写

`Lcase(string)`转换为小写

`Space(x)`返回x个空白的字符串

`Asc(string)`返回一个 integer，代表字符串中首字母的字符代码

`Chr(charcode)`返回 string,其中包含有与指定的字符代码相关的字符

### 3.12.4 转换函数

`CBool(expression)`转换为Boolean型

`CByte(expression)`转换为Byte型

`CCur(expression)`转换为Currency型

`CDate(expression)`转换为Date型

`CDbl(expression)`转换为Double型

`CDec(expression)`转换为Decemal型

`CInt(expression)`转换为Integer型

`CLng(expression)`转换为Long型

`CSng(expression)`转换为Single型

`CStr(expression)`转换为String型

`CVar(expression)`转换为Variant型

`Val(string)`转换为数据型

`Str(number)`转换为String

### 3.12.5 时间函数

`Now`  返回一个 Variant (Date)，根据计算机系统设置的日期和时间来指定日期和时间。

`Date`返回包含系统日期的 Variant (Date)。

`Time` 返回一个指明当前系统时间的 Variant (Date)。

`Timer` 返回一个 Single，代表从午夜开始到现在经过的秒数。

`TimeSerial(hour, minute, second)` 返回一个 Variant (Date)，包含具有具体时、分、秒的时间。

`DateDiff(interval, date1, date2[, firstdayofweek[, firstweekofyear]])` 返回 Variant(Long) 的值，表示两个指定日期间的时间间隔数目

`Second(time) 返回一个 Variant (Integer)`，其值为 0 到 59 之间的整数，表示一分钟之中的某个秒

`Minute(time) 返回一个 Variant (Integer)`，其值为 0 到 59 之间的整数，表示一小时中的某分钟Hour(time)  返回一个 

`Variant(Integer)`，其值为 0 到 23 之间的整数，表示一天之中的某一钟点

`Day(date)`  返回一个 `Variant(Integer)`，其值为 1 到 31 之间的整数，表示一个月中的某一日

`Month(date)` 返回一个 `Variant(Integer)`，其值为 1 到 12 之间的整数，表示一年中的某月

`Year(date)`  返回 `Variant (Integer)`，包含表示年份的整数。

`Weekday(date, [firstdayofweek])` 返回一个 `Variant (Integer)`，包含一个整数，代表某个日期是星期几

# 4. 最后

最后给出之前的`1.4 感受VBA`中我使用到的代码。

```vb
Sub 游戏排行宏()
'
' 宏2 宏
'

'核心代码
    For i = 2 To 31
    isHave = False
     For j = 2 To 31
     If Range("F" & i) = Range("B" & j) Then
     isHave = True
     If Range("E" & i) - Range("A" & j) > 0 Then
     Count = Range("E" & i) - Range("A" & j)
     Range("F" & i) = Range("F" & i) & " ↓" & Count
     Range("E" & i & ":" & "G" & i).Select
     With Selection.Font
        .ThemeColor = xlThemeColorAccent6
        .TintAndShade = 0
     End With
     ElseIf Range("E" & i) - Range("A" & j) < 0 Then
     Count = Abs(Range("E" & i) - Range("A" & j))
     Range("F" & i) = Range("F" & i) & " ↑" & Count
     Range("E" & i & ":" & "G" & i).Select
     With Selection.Font
        .Color = -16776961
        .TintAndShade = 0
     End With
     Exit For
     End If
     End If
     Next
    If isHave = False Then
    Range("F" & i) = Range("F" & i) & " ↑new"
    End If
    Next
' 下面为调整格式，直接通过录制宏拷贝过来的代码
    Columns("E:E").ColumnWidth = 7
    Columns("F:F").ColumnWidth = 50
    Columns("G:G").ColumnWidth = 21.5
    Range("E1:G31").Select
    Range("F16").Activate
    Selection.Borders(xlDiagonalDown).LineStyle = xlNone
    Selection.Borders(xlDiagonalUp).LineStyle = xlNone
    With Selection.Borders(xlEdgeLeft)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlEdgeTop)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlEdgeBottom)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlEdgeRight)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlInsideVertical)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Borders(xlInsideHorizontal)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    With Selection.Font
        .Name = "阿里巴巴普惠体"
        .Size = 11
        .Strikethrough = False
        .Superscript = False
        .Subscript = False
        .OutlineFont = False
        .Shadow = False
        .Underline = xlUnderlineStyleNone
        .TintAndShade = 0
        .ThemeFont = xlThemeFontNone
    End With
    Range("1:1,3:3,5:5,7:7,9:9").Select
    Range("A9").Activate
    ActiveWindow.SmallScroll Down:=6
    Range( _
        "1:1,3:3,5:5,7:7,9:9,11:11,13:13,15:15,17:17,19:19,21:21,23:23,25:25,27:27,29:29,31:31" _
        ).Select
    Range("A31").Activate
    With Selection.Interior
        .Pattern = xlSolid
        .PatternColorIndex = xlAutomatic
        .ThemeColor = xlThemeColorDark1
        .TintAndShade = -0.149998474074526
        .PatternTintAndShade = 0
    End With
End Sub
```

其实有个简单的方法，你先将你需要的功能通过`录制宏`录制下来，再进行编辑这个宏就可以了。

如果上面的知识你都差不多了解了的话，那么VBA就基本上入门了，如果遇到什么无法解决的问题，到时候再通过百度进行学习就可以了。

## 4.1 参考资料

[VBA语言的基础认识](https://www.w3cschool.cn/excelvba/excelvba-basics.html)
