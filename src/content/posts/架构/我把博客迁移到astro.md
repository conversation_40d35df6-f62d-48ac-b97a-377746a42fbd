---
title: 我把博客迁移到了Astro
date: 2025-07-21 20:24:45
categories:
  - 架构
tags:
  - astro
---

新的前端周刊网站[RenderedWeekly（每周渲染）](https://renderedweekly.com)已经上线了，欢迎大家访问。

---

## 以前的架构 Hexo

Hexo 我用了好几年，非常方便，开箱即用，同时它有着非常多的开源主题，让你非常轻易的就能搭出好看的博客。

当时为什么选择 Hexo 我已经忘记了，那个时候我才做前端没有多久，想要搭建一个属于自己的博客，对于当时的我来说，根本无法分辨各个框架之间的细节与具体的区别。

可能是机缘巧合下就遇到了 Hexo，这一用，就是几年。

开箱即用的 Hexo 拥有静态渲染博客的功能，可以直接将 .md 文件渲染为 Html 的功能，这就让网站拥有了良好的 SEO。

## 为什么迁移

博客的更新我停了两年半，在之前的文章中我也详细地讲解了那段时间我的经历，随着认知的提升，我接触到了很多新内容，选择 Astro 的原因在 [我选择了Astro作为周刊架构](/2025/06/16/杂谈/我选择了astro作为周刊架构/) 这篇文章中也有提到，但随着我深入使用，我发现 Astro 比起 Hexo 拥有极强的兼容性，可以让你使用 Vue、React、SolidJS 等等框架编写页面。

Astro 还拥有极强的定制化能力，只要你是一个前端开发者，你可以在极短的时间内就上手 Astro。

它几乎将性能发挥到了极致，由于它内部的孤岛架构，Astro 项目的页面打开速度非常快，这一点对于 SEO 来说非常重要，尤其是现在 AI 工具搜索功能被大范围使用，很多人开始用 AI 替代传统的搜索引擎，Astro 的快速响应可以让 AI 抓取的时候更加顺利。

## AI 的辅助

现在 AI 编程一天比一天强，AI 可以快速搭建出 Astro 项目的架构，你可以完全按照你所想的实现一个属于自己的网站。

所以为了持续的可维护性，我将博客迁移到了 Astro。

## 迁移过程

由于我想要保留之前博客获得的 SEO，所以先让 AI 分析了 Hexo 的目录结构，然后让 AI 生成了一个迁移计划，最后让 AI 按照迁移计划将文章迁移过来。

迁移完毕没有太大问题的时候就让 AI 实现界面结构和样式，并且实现搜索功能。

现在博客中使用到的搜索功能效率非常高，之前 Hexo 的搜索有 BUG，有时候会搜不出来，需要删除一个字符才能显示搜索结果，而迁移后的项目搜索功能几乎就是实时呈现。

虽然我努力的想要保持以前的目录结构，但是 Astro 在生成路由的时候，会将中文字符给舍去，比如中文的`，（）`等等字符，所以现在的路由和 Hexo 的路由还是有些区别，不过也无所谓，我之前也没有关注过博客的流量来源。

再后来就是调整 Hexo 独有的语法，比如 `{% post_link %}`这类语法，换成 Astro 支持的语法。

总之，这次的迁移是综合了**定制化**，**可维护性**，**SEO**等等方面的考虑。