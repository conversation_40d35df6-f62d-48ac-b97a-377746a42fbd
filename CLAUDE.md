# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a personal blog built with **Astro** and **TypeScript**, migrated from a Hexo blog. It's a static site generator (SSG) project focused on frontend development content, with Chinese language support and optimized for performance and SEO.

The blog features:
- Astro-based static site generation
- TypeScript for type safety
- Tailwind CSS for styling with custom typography
- Content collections for organized blog posts
- SEO optimization with structured data
- RSS feed and sitemap generation
- Responsive design with mobile-first approach
- Global search functionality with modal interface

## Development Commands

### Essential Commands
```bash
# Start development server (localhost:3000)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run Astro CLI commands
npm run astro [command]
```

### Development Server
- **Port**: 3000 (configured in astro.config.mjs)
- **Host**: Available on all network interfaces (host: true)

## Architecture Overview

### Core Components Architecture
The application follows a component-based architecture with shared components:

- **Header.astro**: Navigation with integrated search functionality
- **Footer.astro**: Site footer with links and social media
- **SearchModal.astro**: Global search modal with real-time filtering
- **BaseLayout.astro**: Base HTML structure with SEO meta tags
- **PostLayout.astro**: Blog post layout with content styling

### Page Structure and Routing
```
src/pages/
├── index.astro                    # Homepage with recent posts
├── archives.astro                 # All posts organized by year
├── categories.astro               # Categories overview page
├── categories/[category].astro    # Individual category pages
├── tags.astro                     # Tags cloud and list view
├── tags/[tag].astro              # Individual tag pages
├── [year]/[month]/[day]/[...slug].astro  # Dynamic blog post routes
├── page/[...page].astro          # Pagination pages
├── search-data.json.ts           # Search API endpoint
├── robots.txt.js                 # SEO robots file
└── rss.xml.js                    # RSS feed generation
```

### Content Management System
- **Content Collections**: Uses Astro's type-safe content collections
- **Schema**: Defined in `src/content/config.ts` with Hexo compatibility
- **Post Structure**: Date-based URLs: `/{year}/{month}/{day}/{slug}/`
- **Collections**: `posts` (blog articles) and `pages` (static content)

### Search System Architecture
- **Frontend**: Real-time search modal with keyboard navigation
- **Backend**: JSON API endpoint (`/search-data.json`) with cached responses
- **Features**: Multi-field search (title, description, tags, categories)
- **UX**: Keyboard shortcuts (Ctrl+K/Cmd+K), arrow navigation, ESC to close

### Styling System
- **Framework**: Tailwind CSS with extensive customization
- **Typography**: Custom prose styles optimized for Chinese content
- **Responsive**: Mobile-first with `sm:` and `lg:` breakpoints
- **Theme**: Material Design-inspired with consistent color scheme
- **Components**: Reusable utility classes and component patterns

## Configuration Files

### Main Configuration
- **astro.config.mjs**: Astro configuration with integrations and build settings
- **tailwind.config.js**: Tailwind with custom typography and Chinese optimization
- **src/config.ts**: Site configuration, URL generators, and social links
- **src/content/config.ts**: Content collection schemas with Hexo compatibility

### Key Configuration Details
- **Site URL**: https://www.cclliang.com
- **Build Format**: Directory-based with trailing slashes
- **Sitemap**: Custom priority and frequency settings for different page types
- **Markdown**: Shiki syntax highlighting with github-light theme
- **External Links**: Automatic target="_blank" with security attributes

## Content Guidelines

### Blog Post Front Matter
Required fields:
```yaml
---
title: "Post Title"
date: 2024-01-01
categories: ["Category"]
description: "Post description"
---
```

Optional fields:
```yaml
tags: ["tag1", "tag2"]
draft: false
updated: 2024-01-02
keywords: ["keyword1", "keyword2"]
author: "Author Name"
cover: "/path/to/image.jpg"
```

### URL Structure and SEO
- Blog posts: `/{year}/{month}/{day}/{slug}/`
- Categories: `/categories/{category}/`
- Tags: `/tags/{tag}/`
- Archives: `/archives/`
- Search data: `/search-data.json`

## Development Patterns

### Component Development
- Use Astro components (.astro files) for server-side rendering
- Implement TypeScript for type safety in scripts
- Follow Material Design principles for UI consistency
- Use Tailwind utility classes with custom component patterns

### Search Implementation
- Search data is generated at build time via API route
- Client-side search with debouncing (300ms) for performance
- Keyboard navigation with arrow keys and Enter selection
- Modal interface with backdrop click and ESC key closing

### Content Organization
- Posts organized by categories and tags in front matter
- Date-based folder structure optional (flat structure supported)
- Images stored in `public/` directory
- Markdown files in `src/content/posts/`

## SEO and Performance

### SEO Features
- Structured data (JSON-LD) for blog posts
- Open Graph and Twitter Cards metadata
- Canonical URLs for all pages
- RSS feed generation at `/rss.xml`
- Sitemap with prioritized URLs and custom pages
- External links with security attributes

### Performance Optimizations
- Static site generation with Astro
- Sharp integration for optimized images
- Tailwind CSS purging for minimal bundle size
- Inlined stylesheets in auto mode
- Efficient code splitting and lazy loading

## Content Creation Workflow

### Writing Process
1. Create markdown files in `src/content/posts/`
2. Use proper front matter with required fields
3. Organize content with categories and tags
4. Add images to `public/` directory
5. Test with `npm run dev` before publishing
6. Build and preview with `npm run build && npm run preview`

### Migration Notes
- Project migrated from Hexo with maintained URL structure
- Content collection schema supports Hexo front matter
- Legacy redirects handled through configuration
- Images and assets preserved in public directory

## Language and Localization

### Chinese Content Optimization
- **Primary Language**: Chinese (zh-CN)
- **Timezone**: Asia/Shanghai
- **Typography**: Custom Tailwind configuration for Chinese text
- **Line Height**: Optimized for Chinese character readability (1.7)
- **Font Sizes**: Responsive scaling for different screen sizes

### Content Categories
Common technical topics include:
- JavaScript, React, Vue, Node.js, TypeScript
- CSS, HTML, Web development tools
- Frontend frameworks and libraries
- Technical tutorials and guides
- Development workflow and best practices

## Cursor Rules Integration

Based on `.cursorrules`, when working on this project:
- Focus on modern web frontend development expertise
- Emphasize clear, accessible technical writing
- Use structured content with proper headings and code examples
- Maintain friendly, encouraging tone for Chinese technical audience
- Balance technical accuracy with ease of understanding
- Provide practical, real-world application examples
- Include constructive code review and improvement suggestions