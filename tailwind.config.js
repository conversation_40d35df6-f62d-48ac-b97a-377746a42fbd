/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      typography: {
        DEFAULT: {
          css: {
            // 整体设置 - 优化基础字体大小
            maxWidth: 'none',
            color: '#374151', // text-gray-700
            fontSize: '1rem', // 明确设置基础字体大小为16px
            lineHeight: '1.7', // 优化行高，更适合中文阅读
            
            // 标题样式 - 调整字体大小层次
            h1: {
              color: '#1f2937', // text-gray-800
              fontWeight: '700',
              fontSize: '2rem', // 从2.25rem调整为2rem，更合理
              marginTop: '2rem',
              marginBottom: '1rem',
              lineHeight: '1.2',
            },
            h2: {
              color: '#1f2937', // text-gray-800
              fontWeight: '600',
              fontSize: '1.625rem', // 从1.875rem调整为1.625rem
              marginTop: '2rem',
              marginBottom: '1rem',
              lineHeight: '1.3',
            },
            h3: {
              color: '#1f2937', // text-gray-800
              fontWeight: '600',
              fontSize: '1.375rem', // 从1.5rem调整为1.375rem
              marginTop: '1.5rem',
              marginBottom: '0.75rem',
              lineHeight: '1.4',
            },
            h4: {
              color: '#1f2937', // text-gray-800
              fontWeight: '600',
              fontSize: '1.125rem', // 从1.25rem调整为1.125rem
              marginTop: '1.5rem',
              marginBottom: '0.5rem',
              lineHeight: '1.4',
            },
            
            // 段落 - 优化段落间距
            p: {
              marginTop: '1.25rem',
              marginBottom: '1.25rem',
              lineHeight: '1.7', // 与整体行高保持一致
            },
            
            // 链接
            a: {
              color: '#3b82f6', // text-blue-500
              textDecoration: 'none',
              fontWeight: '500',
              '&:hover': {
                color: '#2563eb', // text-blue-600
                textDecoration: 'underline',
              },
            },
            
            // 列表
            ul: {
              marginTop: '1.25rem',
              marginBottom: '1.25rem',
              paddingLeft: '1.5rem',
            },
            ol: {
              marginTop: '1.25rem',
              marginBottom: '1.25rem',
              paddingLeft: '1.5rem',
            },
            li: {
              marginTop: '0.5rem',
              marginBottom: '0.5rem',
            },
            
            // 代码 - 优化代码字体大小
            code: {
              color: '#e11d48', // text-rose-600
              backgroundColor: '#f1f5f9', // bg-slate-100
              padding: '0.25rem 0.375rem',
              borderRadius: '0.25rem',
              fontSize: '0.9rem', // 从0.875rem增大到0.9rem，提高可读性
              fontWeight: '500',
              '&::before': {
                content: '""',
              },
              '&::after': {
                content: '""',
              },
            },
            
            // 代码块
            pre: {
              backgroundColor: 'var(--astro-code-color-background, #f6f8fa)',
              color: 'var(--astro-code-color-text, #24292f)',
              padding: '1.5rem',
              paddingRight: '60px', // 为复制按钮预留空间
              borderRadius: '0.75rem',
              fontSize: '0.9rem',
              lineHeight: '1.6',
              marginTop: '1.75rem',
              marginBottom: '1.75rem',
              overflow: 'auto',
              border: '1px solid #e1e4e8',
              position: 'relative', // 支持绝对定位的复制按钮
              code: {
                backgroundColor: 'transparent',
                color: 'inherit',
                padding: '0',
                fontSize: 'inherit',
                fontWeight: 'inherit',
              },
            },
            
            // 引用
            blockquote: {
              borderLeftWidth: '4px',
              borderLeftColor: '#3b82f6', // border-blue-500
              backgroundColor: '#f8fafc', // bg-slate-50
              padding: '1rem 1.5rem',
              marginTop: '1.75rem',
              marginBottom: '1.75rem',
              fontStyle: 'italic',
              color: '#64748b', // text-slate-500
              borderRadius: '0 0.5rem 0.5rem 0',
              p: {
                marginTop: '0',
                marginBottom: '0',
              },
            },
            
            // 表格
            table: {
              width: '100%',
              marginTop: '2rem',
              marginBottom: '2rem',
              borderCollapse: 'collapse',
            },
            thead: {
              borderBottomWidth: '1px',
              borderBottomColor: '#d1d5db', // border-gray-300
            },
            'thead th': {
              color: '#1f2937', // text-gray-800
              fontWeight: '600',
              textAlign: 'left',
              padding: '0.75rem 1rem',
              backgroundColor: '#f9fafb', // bg-gray-50
            },
            'tbody tr': {
              borderBottomWidth: '1px',
              borderBottomColor: '#e5e7eb', // border-gray-200
            },
            'tbody td': {
              padding: '0.75rem 1rem',
            },
            
            // 水平线
            hr: {
              borderColor: '#e5e7eb', // border-gray-200
              marginTop: '3rem',
              marginBottom: '3rem',
            },
            
            // 强调
            strong: {
              color: '#1f2937', // text-gray-800
              fontWeight: '600',
            },
            
            // 图片 - 优化样式和居中
            img: {
              borderRadius: '0.75rem',
              marginTop: '2rem',
              marginBottom: '2rem',
              marginLeft: 'auto',
              marginRight: 'auto',
              maxWidth: '100%',
              height: 'auto',
              display: 'block',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.02)',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              },
            },
          },
        },
        
        // 大屏幕优化
        lg: {
          css: {
            fontSize: '1.125rem', // 18px，适合大屏阅读
            lineHeight: '1.7',
            h1: {
              fontSize: '2.25rem', // 36px
            },
            h2: {
              fontSize: '1.875rem', // 30px
            },
            h3: {
              fontSize: '1.5rem', // 24px
            },
            h4: {
              fontSize: '1.25rem', // 20px
            },
            // 大屏幕图片样式
            img: {
              maxWidth: '90%', // 大屏幕上稍微限制宽度
              borderRadius: '1rem', // 更大的圆角
            },
          },
        },
        
        // 小屏幕优化
        sm: {
          css: {
            fontSize: '0.975rem', // 保持15.6px，略大于14px最小建议
            lineHeight: '1.65', // 小屏幕稍微紧凑的行高
            h1: {
              fontSize: '1.75rem', // 28px，适合移动端
            },
            h2: {
              fontSize: '1.5rem', // 24px
            },
            h3: {
              fontSize: '1.25rem', // 20px
            },
            h4: {
              fontSize: '1.125rem', // 18px
            },
            // 小屏幕图片样式
            img: {
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
              borderRadius: '0.5rem', // 小屏幕使用较小圆角
              maxWidth: '100%', // 小屏幕充分利用空间
            },
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    function({ addBase, addUtilities }) {
      // 添加自定义图片工具类
      addUtilities({
        '.img-center': {
          display: 'block',
          marginLeft: 'auto',
          marginRight: 'auto',
        },
        '.img-rounded': {
          borderRadius: '0.75rem',
        },
        '.img-shadow': {
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        },
        '.img-hover': {
          transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.02)',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          },
        },
        '.img-responsive': {
          maxWidth: '100%',
          height: 'auto',
        },
      });
      
      addBase({
        ':root': {
          '--astro-code-color-text': '#24292f',
          '--astro-code-color-background': '#f6f8fa',
          '--astro-code-token-constant': '#032f62',
          '--astro-code-token-string': '#032f62',
          '--astro-code-token-comment': '#6a737d',
          '--astro-code-token-keyword': '#d73a49',
          '--astro-code-token-parameter': '#e36209',
          '--astro-code-token-function': '#6f42c1',
          '--astro-code-token-string-expression': '#032f62',
          '--astro-code-token-punctuation': '#24292f',
          '--astro-code-token-link': '#032f62',
        },
        '@media (prefers-color-scheme: dark)': {
          ':root': {
            '--astro-code-color-text': '#e1e4e8',
            '--astro-code-color-background': '#24292e',
            '--astro-code-token-constant': '#79b8ff',
            '--astro-code-token-string': '#9ecbff',
            '--astro-code-token-comment': '#6a737d',
            '--astro-code-token-keyword': '#f97583',
            '--astro-code-token-parameter': '#ffab70',
            '--astro-code-token-function': '#b392f0',
            '--astro-code-token-string-expression': '#9ecbff',
            '--astro-code-token-punctuation': '#e1e4e8',
            '--astro-code-token-link': '#9ecbff',
          },
        },
      });
    },
  ],
}

