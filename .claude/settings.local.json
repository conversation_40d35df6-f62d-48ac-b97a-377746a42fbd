{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "Bash(node migrate-posts.js)", "Bash(cp:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "Bash(rm:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:hexo.fluid-dev.com)", "<PERSON><PERSON>(curl:*)", "Bash(rg:*)", "Bash(npm install:*)", "Bash(node:*)", "<PERSON><PERSON>(python3:*)", "Bash(pip3 install:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}}